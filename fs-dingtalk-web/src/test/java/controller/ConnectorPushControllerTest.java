//package controller;
//
//import base.BaseAbstractTest;
//import com.facishare.open.ding.web.controller.cloud.connector.ConnectorPushController;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//public class ConnectorPushControllerTest extends BaseAbstractTest {
//    @Autowired
//    private ConnectorPushController connectorPushController;
//
//    @Test
//    public void recvPushData() {
//        String json = "{\"ddDataGmtCreate\":1628221477404,\"ddDataCreateAppType\":\"premium_microapp\",\"ddDataModifiedAppId\":\"premium_microapp@61790\",\"ddDataAction\":\"add\",\"data\":{\"code\":\"SP00001\",\"baseUnitCode\":\"Pcs\",\"name\":\"测试商品1\",\"unitList\":[],\"barcode\":\"\",\"priceList\":[{\"unitCode\":\"Pcs\",\"costPrice\":\"0\",\"retailPrice\":\"0\"}]},\"ddDataModifiedAppType\":\"premium_microapp\",\"operateUserId\":\"manager8812\",\"ddDataGmtModified\":1628221477404,\"ddDataCorpId\":\"ding0c8ee222b9a38265f2c783f7214b6d69\",\"ddDataCreateAppId\":\"premium_microapp@61790\",\"ddDataModelId\":\"EM-10149F1A986F0B1738B3000H\"}";
//        connectorPushController.recvPushData(json,null);
//    }
//}
