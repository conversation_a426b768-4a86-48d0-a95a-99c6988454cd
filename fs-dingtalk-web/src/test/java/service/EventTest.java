package service;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.ding.api.enums.BizDataTypeEnum;
import com.facishare.open.ding.api.model.AuthEnterPriseModel;
import com.facishare.open.ding.api.model.CRMDingProductMap;
import com.facishare.open.ding.api.model.OrderModel;
import com.facishare.open.ding.api.result.PollingSyncResult;
import com.facishare.open.ding.api.service.PollingSyncService;
import com.facishare.open.ding.api.service.SyncBizDataService;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.open.ding.api.service.cloud.DingSupportSendMessageService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.api.vo.HighBizDataVo;
import com.facishare.open.ding.api.vo.PollingSyncDataVo;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.dao.OpenSyncBizDataDao;
import com.facishare.open.ding.cloud.entity.HighBizDataDo;
import com.facishare.open.ding.cloud.manager.CrmManager;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.cloud.manager.EventManager;
import com.facishare.open.ding.cloud.service.impl.*;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.result.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jayway.jsonpath.JsonPath;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.jws.Oneway;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * <AUTHOR>
 * @Date 2021/4/28 16:37
 * @Version 1.0
 */
@Slf4j
public class EventTest extends BaseAbstractTest {

    @Autowired
    private EnterpriseAuthBizType4ServiceImpl enterpriseAuthBizType4ServiceImpl;
    @Autowired
    private DeptBizType14ServiceImpl deptBizType14Service;
    @Autowired
    private EmpBizType13ServiceImpl empBizType13Service;
    @Autowired
    private EventManager eventManager;
    @Autowired
    private SyncBizDataService syncBizDataService;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private CrmManager crmManager;
    @Autowired
    private DingSupportSendMessageService dingSupportSendMessageService;
    @Autowired
    private DingAuthService dingAuthServiceImpl;
    @Autowired
    private OrderEvent17ServiceImpl orderEvent17Service;
    @Autowired
    private ChatEvent158ServiceImpl chatEvent158Service;
    @Autowired
    private DingObjData48EventServiceImpl dingObjData48EventService;
    @Autowired
    private OpenSyncBizDataDao openSyncBizDataDao;
    @Autowired
    private PollingSyncService pollingSyncService;


    @Test
    public void testEmpCount() {

        for (int i = 0; i < 10; i++) {
            //openSyncBizDataDao.setTenantId("0880").selectBizTypeByCorpId(17, "dinge746c724519c3388acaaa37764f94726", "19123003_0");


//            Result<String> result = dingAuthService.dingCorpIDtoEA("dinge746c724519c3388acaaa37764f94726");
            log.info("1111");
//            HighBizDataDo bizDataDo = openSyncBizDataDao.selectBizTypeByCorpId(17, "dinge746c724519c3388acaaa37764f94726", "19123003_0");

        }


    }

    @Test
    public void testEmpCountDingManager() {

        for (int i = 0; i < 10; i++) {
//            Integer result = dingManager.getEmpCount("ding6c3e64b40293965c24f2f5cc6abecb85", ConfigCenter.CRM_SUITE_ID);



           log.info("1111");
        }


    }

    @Test
    public void testMessage() {
        HighBizDataDo bizDataDo = new HighBizDataDo();
        bizDataDo.setBizId("16740006");
        bizDataDo.setBizData("{\"result\":[{\"creatorUserId\":\"manager7605\",\"gmtModified\":1645515974000,\"data\":{\"address\":{\"extendValue\":\"{\\\"detail\\\":{\\\"name\\\":\\\"上海\\\"}}\",\"value\":\"上海\"},\"customer_phone\":{\"extendValue\":\"{\\\"countryCode\\\":\\\"+86\\\",\\\"countryKey\\\":\\\"CN\\\",\\\"countryName\\\":\\\"China\\\",\\\"countryNamePy\\\":\\\"ZHONGGUO\\\",\\\"countryNameZh\\\":\\\"中国\\\",\\\"flag\\\":\\\"C\\\",\\\"flagPy\\\":\\\"Z\\\",\\\"mode\\\":\\\"phone\\\"}\",\"value\":\"15454545550\"},\"customer_follow_up_status\":{\"value\":\"{\\\\\\\"extendValue\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"extension\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"editFreeze\\\\\\\\\\\\\\\":true},\\\\\\\\\\\\\\\"label\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"新获取\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"key\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"option_new_acquisition\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"value\\\\\\\":\\\\\\\"新获取\\\\\\\"}\"},\"customer_name\":\"陈川\",\"email\":\"<EMAIL>\"},\"formCode\":\"PROC-CC1CD57C-6ACB-4248-B539-B800256762B0\",\"extendData\":{\"biz_customer_last_status_change_dateV2\":\"1645515974887\",\"biz_customer_biz_scene\":\"crm_customer_personal\"},\"permission\":{\"participantStaffIds\":[\"manager7605\"],\"ownerStaffIds\":[\"manager7605\"]},\"gmtCreate\":1645515973000,\"orgId\":314091096,\"objectType\":\"crm_customer_personal\",\"procInstStatus\":\"COMPLETED\",\"appUuid\":\"SWAPP-45C4B36964CB10B0DA7E216EFABF2E9E\",\"instanceId\":\"hDxJaLpSQe-4eBmabAtylQ03541645515973\",\"procOutResult\":\"agree\"}],\"syncAction\":\"ding_paas_object_data_update\",\"success\":true,\"dingOpenErrcode\":0,\"syncSeq\":\"85B1740953D85E53A6AE8EB544\"}");
        bizDataDo.setCorpId("dingfeca3fa3352c7d4ca39a90f97fcb1e09");
        bizDataDo.setBizType(48);
        bizDataDo.setSubscribeId("16740006_0");
        dingObjData48EventService.executeEvent(bizDataDo);
    }

    @Test
    public void testInitCorp() {
        HighBizDataDo bizDataDo = new HighBizDataDo();
        bizDataDo.setCorpId("ding6c3e64b40293965c24f2f5cc6abecb85");
        bizDataDo.setBizData("{\"auth_user_info\":{\"userId\":\"manager7736\"},\"auth_corp_info\":{\"corp_type\":0,\"corpid\":\"ding6c3e64b40293965c24f2f5cc6abecb85\",\"auth_level\":0,\"auth_channel\":\"4\",\"industry\":\"\",\"full_corp_name\":\"挪威森林\",\"corp_name\":\"挪威森林\",\"invite_url\":\"https://wx-in-i.dingtalk.com/invite-page/weixin.html?bizSource=____source____&corpId=ding6c3e64b40293965c24f2f5cc6abecb85&inviterUid=1B60E48F9F3CE0816B2F679C0AD92F4C\",\"auth_channel_type\":\"\",\"invite_code\":\"\",\"is_authenticated\":false,\"license_code\":\"\",\"corp_logo_url\":\"\"},\"syncAction\":\"org_suite_auth\",\"auth_scope\":{\"errcode\":0,\"condition_field\":[],\"auth_user_field\":[\"jobnumber\",\"isLeader\",\"name\",\"position\",\"isAdmin\",\"avatar\",\"department\",\"userid\",\"deviceId\",\"isHide\"],\"auth_org_scopes\":{\"authed_user\":[],\"authed_dept\":[1]},\"errmsg\":\"ok\"},\"auth_info\":{\"agent\":[{\"agentid\":**********,\"agent_name\":\"纷享销客CRM-112测试\",\"logo_url\":\"https://static-legacy.dingtalk.com/media/lALPDf0iySHuO1DM8Mzw_240_240.png\",\"appid\":70480,\"admin_list\":[\"manager7736\"]}]},\"permanent_code\":\"oJrzxhjKgtG_EumgiGU8rc7vXKZdyMRYh6ZJbmmJEMvDwvGaGhJ4t3ZOwS_6R1KD\",\"syncSeq\":\"864DD3DFE04AA22FC8A4D7DEE3\"}");
        AuthEnterPriseModel.BizData authModel = JSONObject.parseObject(bizDataDo.getBizData(), new TypeReference<AuthEnterPriseModel.BizData>() {
        });

    }

    @Test
    public void testAuth() {
        HighBizDataDo bizDataDo = new HighBizDataDo();
        bizDataDo.setBizId("16740006");
        bizDataDo.setBizData("{\"auth_user_info\":{\"userId\":\"manager7605\"},\"auth_corp_info\":{\"corp_type\":0,\"corpid\":\"dingfeca3fa3352c7d4ca39a90f97fcb1e09\",\"auth_level\":0,\"auth_channel\":\"4\",\"industry\":\"\",\"full_corp_name\":\"半盒子饮料\",\"corp_name\":\"半盒子饮料\",\"invite_url\":\"https://wx-in-i.dingtalk.com/invite-page/weixin.html?bizSource=____source____&corpId=dingfeca3fa3352c7d4ca39a90f97fcb1e09&inviterUid=1B60E48F9F3CE0816B2F679C0AD92F4C\",\"auth_channel_type\":\"\",\"invite_code\":\"\",\"is_authenticated\":false,\"license_code\":\"\",\"corp_logo_url\":\"\"},\"syncAction\":\"org_suite_change\",\"auth_scope\":{\"errcode\":0,\"condition_field\":[],\"auth_user_field\":[\"jobnumber\",\"isLeader\",\"name\",\"position\",\"isAdmin\",\"avatar\",\"department\",\"userid\",\"deviceId\",\"isHide\"],\"auth_org_scopes\":{\"authed_user\":[\"066711172737717760\",\"51375262644143\",\"092159206936640993\",\"301214332529072253\",\"011506253300889682\",\"484438381176257\",\"010762510468893014052\",\"094514581124355005\",\"041137603324111038\",\"manager7605\",\"104968262829305261\",\"0258205136694200\",\"181521440121517566\",\"303814632324386413\"],\"authed_dept\":[]},\"errmsg\":\"ok\"},\"auth_info\":{\"agent\":[{\"agentid\":**********,\"agent_name\":\"纷享销客CRM-112测试\",\"logo_url\":\"https://static-legacy.dingtalk.com/media/lALPDf0iySHuO1DM8Mzw_240_240.png\",\"appid\":70480,\"use_app_role\":false,\"admin_list\":[\"070918523636548759\",\"020611544221374513\",\"manager7605\",\"181521440121517566\"]}]},\"permanent_code\":\"E2OKYdEOvxGPY8JEAhRCjRMtCApjcFkmW7MOz56MSelDnIcnP-8mgY83FaQLGzQm\",\"syncSeq\":\"FF369EC3DD3B047733941A2DCE\"}");
        bizDataDo.setCorpId("dingfeca3fa3352c7d4ca39a90f97fcb1e09");
        bizDataDo.setBizType(4);
        bizDataDo.setSubscribeId("16740006_0");
        enterpriseAuthBizType4ServiceImpl.executeEvent(bizDataDo);
    }

    @Test
    public void testOrder() {

        HighBizDataDo bizDataDo = new HighBizDataDo();
        bizDataDo.setBizId("2029101328059580209");//MGqBzYCwrL0aiPaiiG7EnLcQiEiE  QnTO44W6kWlBShHwVhSI2giEiE
        bizDataDo.setBizData("{\"orderType\":\"BUY\",\"orderId\":2029101328059580209,\"syncAction\":\"market_order\",\"openId\":\"MGqBzYCwrL0aiPaiiG7EnLcQiEiE\",\"itemCode\":\"DT_GOODS_881624603524784_654001\",\"saleModelType\":\"CYC_UPGRADE_MEMBER\",\"maxOfPeople\":2147483647,\"itemName\":\"试用规格\",\"payFee\":3840000,\"serviceStopTime\":1676023177000,\"serviceStartTime\":1644310862000,\"suiteKey\":\"suiteah3t5k13w2k7lqce\",\"goodsName\":\"纷享销客CRM\",\"minOfPeople\":1,\"suiteId\":19854003,\"unionId\":\"MGqBzYCwrL0aiPaiiG7EnLcQiEiE\",\"corpId\":\"dinge746c724519c3388acaaa37764f94726\",\"subQuantity\":50,\"goodsCode\":\"DT_GOODS_881624603524784\",\"paidtime\":1622476716000,\"syncSeq\":\"FCD53B341284A295288F28E5A8\"}");
        bizDataDo.setBizType(17);
        orderEvent17Service.executeEvent(bizDataDo);
    }

    @Test
    public void testVersion(){

        Map<String, CRMDingProductMap> dingStarMap = Maps.newHashMap();


        CRMDingProductMap dingProductMap = CRMDingProductMap.builder().crmProductId("6115f5d45a6b4000015016a1").dingItemCode("DT_GOODS_881621847020813_572029").crmVersion("dingtalk_standard_edition").build();
        dingStarMap.put("dingtalk_standard_edition",dingProductMap);
        CRMDingProductMap proMap = CRMDingProductMap.builder().crmProductId("6108dff309cfdc0001b62e75").dingItemCode("DT_GOODS_881621847020813_572030").crmVersion("dingtalk_standardpro_edition").build();
        dingStarMap.put("dingtalk_standardpro_edition",proMap);
        CRMDingProductMap strengthMap = CRMDingProductMap.builder().crmProductId("6115f5d45a6b4000015016a1").dingItemCode("DT_GOODS_881621847020813_747007").crmVersion("dingtalk_strengthen_edition").build();
        dingStarMap.put("dingtalk_strengthen_edition",strengthMap);
        String data = JSONObject.toJSONString(dingStarMap);
        log.info("data");
    }


    @Test
    public void remove() {
        HighBizDataDo bizDataDo = new HighBizDataDo();
        bizDataDo.setBizId("*********");
        bizDataDo.setBizData("{\"syncAction\":\"org_dept_remove\",\"syncSeq\":\"6E80F68CB5E5F85F57300279B3\"}");
        bizDataDo.setCorpId("dinge1345e5355704161ffe93478753d9884");
        bizDataDo.setBizType(14);
        deptBizType14Service.executeEvent(bizDataDo);
    }

    @Test
    public void modifyEmp() {
        HighBizDataDo bizDataDo = new HighBizDataDo();
        bizDataDo.setBizId("****************");
        bizDataDo.setBizData("{\"errcode\":0,\"exclusiveAccount\":false,\"unionid\":\"uNSD1ouLiSiSkea6MBGLYDHAiEiE\",\"syncAction\":\"user_modify_org\",\"roles\":[{\"id\":**********,\"name\":\"主管理员\",\"groupName\":\"默认\",\"type\":101}],\"userid\":\"****************\",\"isLeaderInDepts\":\"{1:false}\",\"isBoss\":false,\"isSenior\":false,\"department\":[1],\"orderInDepts\":\"{1:176290699533012512}\",\"errmsg\":\"ok\",\"active\":true,\"avatar\":\"\",\"isAdmin\":true,\"tags\":{},\"isHide\":false,\"name\":\"熊涛\",\"realAuthed\":false,\"syncSeq\":\"344ED601CCC0267CD540210FEF\"}");
        bizDataDo.setBizType(13);
        bizDataDo.setSubscribeId("16740006_0");
        bizDataDo.setCorpId("ding2ecc12d52d60e1bba39a90f97fcb1e09");
        empBizType13Service.executeEvent(bizDataDo);
    }


    @Test
    public void testManager() {
        Result<PollingSyncResult> syncResult = pollingSyncService.queryPolling(BizDataTypeEnum.HIGH_LEVEL.getType());
        Long lastSyncTime = syncResult.getData().getLastSyncTime();
        PollingSyncDataVo pollingSyncDataArg = PollingSyncDataVo.builder().lastSyncTime(new Date(Instant.now().getEpochSecond() * 1000)).eventLevel(1).build();
        pollingSyncService.updateSync(pollingSyncDataArg);
        Result<List<HighBizDataVo>> syncDataList = syncBizDataService.batchHighQueryData();
        eventManager.doProcessEvent(syncDataList.getData());
    }

    @Test
    public void testEmp() {
        HighBizDataDo bizDataDo = new HighBizDataDo();
        bizDataDo.setBizType(4);
        bizDataDo.setCorpId("ding16c799b4ca32794ff2c783f7214b6d69");
        bizDataDo.setSubscribeId("16740006_0");
        bizDataDo.setBizData("{\"auth_user_info\":{\"userId\":\"manager5531\"},\"auth_corp_info\":{\"corp_type\":0,\"corpid\":\"ding16c799b4ca32794ff2c783f7214b6d69\",\"auth_level\":0,\"auth_channel\":\"4\",\"industry\":\"\",\"full_corp_name\":\"移动城堡\",\"corp_name\":\"移动城堡\",\"invite_url\":\"https://wx-in-i.dingtalk.com/invite-page/weixin.html?bizSource=____source____&corpId=ding16c799b4ca32794ff2c783f7214b6d69&inviterUid=85F9331581FF22D68B464DF4EBED111F\",\"auth_channel_type\":\"\",\"invite_code\":\"\",\"is_authenticated\":false,\"license_code\":\"\",\"corp_logo_url\":\"\"},\"syncAction\":\"org_suite_change\",\"auth_scope\":{\"errcode\":0,\"condition_field\":[],\"auth_user_field\":[\"jobnumber\",\"isLeader\",\"name\",\"position\",\"isAdmin\",\"avatar\",\"department\",\"userid\",\"deviceId\",\"isHide\"],\"auth_org_scopes\":{\"authed_user\":[\"2127060061691456\",\"012668660314774889\",\"1460185315774889\"],\"authed_dept\":[]},\"errmsg\":\"ok\"},\"auth_info\":{\"agent\":[{\"agentid\":**********,\"agent_name\":\"纷享销客CRM-112测试\",\"logo_url\":\"https://static-legacy.dingtalk.com/media/lALPDf0iySHuO1DM8Mzw_240_240.png\",\"appid\":70480,\"admin_list\":[\"manager5531\"]}]},\"permanent_code\":\"O9LHoXPcvpVjAEPS6OolJUXi6N_1s9OjdKISBJJ-JCw5kBa6bu2DAdTnC4ph45-s\",\"syncSeq\":\"FF0C9EC37D3BE47133941A2DCE\"}");
        bizDataDo.setBizId("16740006");
        enterpriseAuthBizType4ServiceImpl.executeEvent(bizDataDo);
    }

    @Test
    public void testQueryDept() {
        crmManager.queryCrmDept(82414, "_id", "9999919");
    }


    @Test
    public void queryUser() {
        String pa = null;
        String p1 = null;
        boolean equals = pa.equals(p1);

        dingManager.getUserByUserId("dinge852faff822b4cdcacaaa37764f94726", "manager116", "19123003");
    }


    @Test
    public void testQueryEi() {
        Result<DingCorpMappingVo> dingCorpMappingVoResult = dingSupportSendMessageService.queryEnterpriseByEi(90111);
        log.info("result");
    }

    @Test
    public void testMockOrder() {
        String data = "{\n" +
                "\t\"orderType\": \"BUY\",\n" +
                "\t\"orderId\": 210221291909670,\n" +
                "\t\"syncAction\": \"market_order\",\n" +
                "\t\"openId\": \"QnTO44W6kWlBShHwVhSI2giEiE\",\n" +
                "\t\"itemCode\": \"DT_GOODS_881621847020813_572028\",\n" +
                "\t\"saleModelType\": \"CYC_UPGRADE_MEMBER\",\n" +
                "\t\"maxOfPeople\": 2147483647,\n" +
                "\t\"itemName\": \"试用规格\",\n" +
                "\t\"payFee\": 0,\n" +
                "\t\"serviceStopTime\": 1629820800000,\n" +
                "\t\"serviceStartTime\": 1628569209000,\n" +
                "\t\"suiteKey\": \"suitebkyxhhamppfr5xod\",\n" +
                "\t\"goodsName\": \"纷享销客CRM\",\n" +
                "\t\"minOfPeople\": 1,\n" +
                "\t\"suiteId\": 16740006,\n" +
                "\t\"unionId\": \"QnTO44W6kWlBShHwVhSI2giEiE\",\n" +
                "\t\"corpId\": \"dingfaf004f6391dd096ffe93478753d9884\",\n" +
                "\t\"orderChargeType\": \"TRYOUT\",\n" +
                "\t\"subQuantity\": 999999,\n" +
                "\t\"leadsFrom\": \"广场-搜索\",\n" +
                "\t\"goodsCode\": \"DT_GOODS_881621847020813\",\n" +
                "\t\"paidtime\": 1628569209000,\n" +
                "\t\"syncSeq\": \"6CD53B341284A295688F38E5AA\"\n" +
                "}";
        Result<Void> orderResult = dingAuthServiceImpl.mockCreateOrder(data, "dingfaf004f6391dd096ffe93478753d9884");

    }

    @Test
    public void queryLicense() {
        OrderModel orderModel = new OrderModel();
        orderModel.setItemCode("DT_GOODS_881621847020813_572028");
        orderModel.setCorpId("ding1f33529fa2a5ead424f2f5cc6abecb85");
        orderModel.setSuiteId("16740006");
        orderModel.setSubQuantity(10);
        orderModel.setServiceStartTime(1635698276958l);
        orderModel.setPaidTime(1635698276958l);
        orderModel.setServiceStopTime(1667234276000l);
        orderModel.setUnionId("QnTO44W6kWlBShHwVhSI2giEiE");
        orderModel.setGoodsCode("DT_GOODS_881621847020813");
        orderModel.setOrderId(210221291909610L);
        orderModel.setPayFee(100000l);
//        orderEvent17Service.createCrmOrder(orderModel, "dtanfx1965", "6115f5d45a6b4000015016a1");
    }

    @Test
    public void testEa() {
//        Result<String> corpResult = dingAuthService.EAtoDingCorpId("76301");
        Result<DingCorpMappingVo> result = dingAuthServiceImpl.queryEnterpriseByCorpId("ding216ede3869d777b6a1320dcb25e91351");
        Result<DingCorpMappingVo> eaResult = dingAuthServiceImpl.queryEnterpriseByEA("76301");

        Result<String> dingCorpIDtoEA = dingAuthServiceImpl.dingCorpIDtoEA("ding216ede3869d777b6a1320dcb25e91351");

        log.info("");
    }


    @Test
    public void testData() throws Exception {
        String result="{\"errCode\":\"s106240000\",\"errMsg\":\"成功\",\"traceMsg\":\"fs-dingtalk-cloud-d91550a2122f4bc9915f58312bad9cc1\"}";
        String errCode= JsonPath.read(result,"$.errCode");

        if(!"s106240000".equals(errCode)){
            throw new Exception("initErpSetting fail ei"+"--"+result);
        }

        String name="1121楠楠";

            Pattern pattern = Pattern.compile("[0-9]*");
            Matcher isNum = pattern.matcher(name);
            if (!isNum.matches()) {
                log.info("false");
            }
        log.info("true");
        }

        @Test
    public void testAuthPer(){
            dingAuthServiceImpl.getPersonalData("dingoadabmbclx2awjzig2","7800457a58d333259377c842d09e832f");
        }

}
