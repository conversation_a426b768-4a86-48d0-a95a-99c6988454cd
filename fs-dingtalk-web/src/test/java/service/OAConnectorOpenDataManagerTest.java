package service;

import com.facishare.open.ding.api.enums.CloudDataTypeEnum;
import com.facishare.open.ding.api.enums.SourceTypeEnum;
import com.facishare.open.ding.api.model.OAConnectorOpenDataModel;
import com.facishare.open.ding.cloud.manager.OAConnectorOpenDataManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class OAConnectorOpenDataManagerTest extends BaseAbstractTest {
    @Autowired
    private OAConnectorOpenDataManager oaConnectorOpenDataManager;

    @Test
    public void pollHistoryCustomerData() {
        OAConnectorOpenDataModel model = new OAConnectorOpenDataModel();
        model.setAppId("16740006");
        model.setChannelId(SourceTypeEnum.DING_CLOUD.name());
        model.setCorpId("dingfeca3fa3352c7d4ca39a90f97fcb1e09");
        model.setDataTypeId(CloudDataTypeEnum.EMPLOYEE_LOGIN.getDataType());
        model.setErrorCode("100");
        model.setErrorMsg("开通失败");
        oaConnectorOpenDataManager.send(model);
    }
}
