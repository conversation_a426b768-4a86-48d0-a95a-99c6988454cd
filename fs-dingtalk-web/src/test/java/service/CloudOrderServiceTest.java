package service;

import base.BaseAbstractTest;
import com.facishare.open.ding.api.service.cloud.CloudOrderService;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.outer.oa.connector.common.api.info.EnterpriseTrialInfo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class CloudOrderServiceTest extends BaseAbstractTest {
    @Autowired
    private CloudOrderService cloudOrderService;

    @Test
    public void getEnterpriseTrialInfo() {
        Result<EnterpriseTrialInfo> enterpriseTrialInfoResult = cloudOrderService.getEnterpriseTrialInfo("ddqybhzyl");
        System.out.println(enterpriseTrialInfoResult);
    }
}
