//package service;
//
//import com.facishare.open.ding.api.service.DingCorpMappingService;
//import com.facishare.open.ding.api.service.DingMappingEmployeeService;
//import com.facishare.open.ding.api.service.cloud.connector.ConnectorHistoryDataService;
//import com.facishare.open.ding.api.vo.DingCorpMappingVo;
//import com.facishare.open.ding.cloud.constants.ConfigCenter;
//import com.facishare.open.ding.cloud.manager.NCrmCloudManager;
//import com.facishare.open.ding.common.result.Result;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//@Slf4j
//public class ConnectorTest extends BaseAbstractTest {
//    @Autowired
//    private ConnectorHistoryDataService connectorHistoryDataService;
//    @Autowired
//    private DingMappingEmployeeService dingMappingEmployeeService;
//    @Autowired
//    private DingCorpMappingService dingCorpMappingService;
//
//    @Autowired
//    private NCrmCloudManager nCrmManager;
//
//    @Test
//    public void pollHistoryCustomerData() {
//        ConfigCenter.NOT_SYNC_ROLE.size();
//        boolean contains = ConfigCenter.NOT_SYNC_ROLE.contains("62009000");
//        DingCorpMappingVo dingCorpMappingVo = new DingCorpMappingVo();
//        dingCorpMappingVo.setDingCorpId("ding0c8ee222b9a38265f2c783f7214b6d69");
//        dingCorpMappingVo.setAppCode(71075L);
//        dingCorpMappingVo.setEi(81243);
//        dingCorpMappingVo.setEa("81243");
//        //connectorHistoryDataService.processHistoryCustomerData(dingCorpMappingVo);
//        connectorHistoryDataService.processHistorySalesOrderData(dingCorpMappingVo);
//    }
//
//    @Test
//    public void test() {
////        CrmProductCategoryArg arg = new CrmProductCategoryArg();
////        arg.setCategory_code("abc3");
////        arg.setName("abc3");
////        arg.setOrder_field("0");
////        CrmResponseResult<CrmProductCategoryResult> result = nCrmManager.createProductCategoryRest(81243,arg);
////
////        System.out.println(result);
//
//        Result<DingCorpMappingVo> dingCorpMappingVoResult = dingCorpMappingService.queryMappingByAppId("ding0c8ee222b9a38265f2c783f7214b6d69",71075L);
//        DingCorpMappingVo dingCorpMappingVo = dingCorpMappingVoResult.getData();
//        dingCorpMappingService.update(dingCorpMappingVo);
//    }
//
//}
