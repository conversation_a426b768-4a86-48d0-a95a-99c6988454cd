package service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.model.OrderModel;
import com.facishare.open.ding.cloud.template.outer.event.order.DingTalkOpenEnterpriseHandlerTemplate;
import com.facishare.open.ding.cloud.template.outer.event.order.DingTalkOrderPaidEventHandlerTemplate;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class DingTalkOrderPaidEventHandlerTemplateTest  extends BaseAbstractTest {
    @Autowired
    private DingTalkOpenEnterpriseHandlerTemplate dingTalkOpenEnterpriseHandlerTemplate;

    @Test
    public void execute() {
        String json = "{\n" +
                "            \"item_code\": \"DT_GOODS_881621847020813_1019031\",\n" +
                "            \"service_stop_time\": \"1734019200000\",\n" +
                "            \"open_id\": \"iPC2zjPksAIiPii9pZyxeduCgiEiE\",\n" +
                "            \"goods_code\": \"DT_GOODS_881621847020813\",\n" +
                "            \"paid_time\": \"1728720335000\",\n" +
                "            \"min_of_people\": 1,\n" +
                "            \"item_name\": \"免费规格\",\n" +
                "            \"service_start_time\": \"1728720335000\",\n" +
                "            \"discount_fee\": 0,\n" +
                "            \"sub_quantity\": 3,\n" +
                "            \"union_id\": \"iPC2zjPksAIiPii9pZyxeduCgiEiE\",\n" +
                "            \"max_of_people\": 3,\n" +
                "            \"nominal_pay_fee\": 0,\n" +
                "            \"pay_fee\": 0,\n" +
                "            \"id\": 53281,\n" +
                "            \"suite_key\": \"suitebkyxhhamppfr5xod\",\n" +
                "            \"ea\": \"dtbbddzy123\",\n" +
                "            \"good_name\": \"纷享销客CRM\",\n" +
                "            \"corp_id\": \"dingfeca3fa3352c7d4ca39a90f97fcb1e09\",\n" +
                "            \"order_id\": 239665531880487,\n" +
                "            \"order_type\": \"BUY\",\n" +
                "            \"suite_id\": 16740006\n" +
                "        }";
        OrderModel orderModel = JSONObject.parseObject(json,OrderModel.class);
        System.out.println(orderModel);
        TemplateResult result = dingTalkOpenEnterpriseHandlerTemplate.execute(orderModel);
        System.out.println("result="+result);
    }
}
