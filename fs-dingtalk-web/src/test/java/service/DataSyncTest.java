package service;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.arg.CrmUserRoleArg;
import com.facishare.open.ding.api.enums.BizDataTypeEnum;
import com.facishare.open.ding.api.model.AuthEnterPriseModel;
import com.facishare.open.ding.api.model.DingObjData;
import com.facishare.open.ding.api.result.DingAppResult;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.OrderInfoResult;
import com.facishare.open.ding.api.result.PollingSyncResult;
import com.facishare.open.ding.api.service.CrmSyncObjService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.service.PollingSyncService;
import com.facishare.open.ding.api.service.SyncBizDataService;
import com.facishare.open.ding.api.service.cloud.*;
import com.facishare.open.ding.api.vo.DingTaskVo;
import com.facishare.open.ding.api.vo.HighBizDataVo;
import com.facishare.open.ding.cloud.arg.CreateCustomerArg;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.constants.Constant;
import com.facishare.open.ding.cloud.dao.OpenSyncBizDataDao;
import com.facishare.open.ding.cloud.entity.HighBizDataDo;
import com.facishare.open.ding.cloud.manager.CrmManager;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.cloud.manager.HttpCloudManager;
import com.facishare.open.ding.cloud.manager.RedisManager;
import com.facishare.open.ding.cloud.service.impl.CloudExternalTodoServiceImpl;
import com.facishare.open.ding.cloud.task.CrmHistoryDataPushJob;
import com.facishare.open.ding.cloud.task.DingAutoHighLevelJob;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.model.UserVo;
import com.facishare.open.ding.common.result.Result;
import com.fxiaoke.message.extrnal.platform.api.ExternalMessageService;
import com.fxiaoke.message.extrnal.platform.model.KeyValueItem;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import com.fxiaoke.message.extrnal.platform.model.result.CreateTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DealTodoResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jayway.jsonpath.JsonPath;
import com.xxl.job.core.biz.model.TriggerParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/4/28 16:37
 * @Version 1.0
 */
@Slf4j
public class DataSyncTest extends BaseAbstractTest {
    @Autowired
    private SyncBizDataService syncBizDataService;
    @Autowired
    private CrmManager crmManager;
    @Autowired
    private HttpCloudManager httpManager;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private OpenSyncBizDataDao openSyncBizDataDao;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private CloudEmpService cloudEmpService;
    @Autowired
    private CloudExternalTodoServiceImpl cloudExternalTodoService;
    @Autowired
    private DingProxyService dingProxyService;
    @Autowired
    private DingAutoHighLevelJob autoHighLevelJob;
    @Autowired
    private DingAuthService dingAuthServiceImpl;
    @Autowired
    private CloudDingRequestService cloudDingRequestService;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private CrmSyncObjService crmSyncObjService;
    @Autowired
    private CloudOrderService cloudOrderService;
    @Autowired
    private CrmHistoryDataPushJob crmHistoryDataPushJob;
    @Resource(name = "cloudExternalMessageServiceImpl")
    private ExternalMessageService externalMessageService;
    @Autowired
    private PollingSyncService pollingSyncService;

    @Test
    public void testTime(){
        Result<HighBizDataVo> result = syncBizDataService.selectByBizType("19854003", "4", "dingfeca3fa3352c7d4ca39a90f97fcb1e09");
        syncBizDataService.getUpdateSql("2021-01-02 12:32:00", 1947);
    }
    @Test
    public void testInfo(){
        OrderInfoResult lastOrder = cloudOrderService.queryLastOrder("ding3959290e81fe7b844ac5d6980864d335", null);
        log.info("lastorder");
    }

    @Test
    public void testQueryBiz(){
        HighBizDataDo bizDataDo = openSyncBizDataDao.selectBizTypeByCorpId(4,
                "ding2ecc12d52d60e1bba39a90f97fcb1e09",
                "19854003");
        log.info("bizdata:{}");
    }
    @Test
    public void testQUery(){
        TriggerParam triggerParam=new TriggerParam();
        try {
            autoHighLevelJob.execute(triggerParam);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testCrmHistoryDataPushJob(){
        TriggerParam triggerParam=new TriggerParam();
        try {
            crmHistoryDataPushJob.execute(triggerParam);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testSql(){
        String nowTime="2021-08-24 17:26:00";
        String enterPriseId="ding16c799b4ca32794ff2c783f7214b6d69";
        String updateSql="update open_sync_biz_data set gmt_modified = "+'\''+nowTime+'\''+"where corp_id="+'\''+enterPriseId+'\''+";";
        Result<Integer> result = syncBizDataService.updateSql(updateSql);
        Result<Integer> result1 = syncBizDataService.updateSql(updateSql);
        log.info("result1");
    }

    @Test
    public void testUpDept(){
        cloudDingRequestService.queryListParentById("dingf45bdda251bf4b4d4ac5d6980864d335",491866427L,"");
    }




    @Test
    public void testSaveData() {
        List<HighBizDataVo> voList = Lists.newArrayList();
        HighBizDataVo bizDataVo = new HighBizDataVo();
        bizDataVo.setId(1499051L);
        bizDataVo.setBizId("16740006");
        bizDataVo.setCorpId("ding0c8ee222b9a38265f2c783f7214b6d691");
        bizDataVo.setGmtCreate(1619734965000L);
        bizDataVo.setGmtModified(1619734965000L);
        bizDataVo.setStatus(1);
        bizDataVo.setSubscribeId("1674110006_0");
        bizDataVo.setOpenCursor(0L);
        bizDataVo.setBizType(13);
        bizDataVo.setBizData("bizdata");
        voList.add(bizDataVo);
        Result<Integer> result = syncBizDataService.batchSaveData(voList);
    }


    //角色创建
    @Test
    public void testAddRole() {
        String fsEa = "aly7522084";
        for (int i = 1000; i < 1010; i++) {
            Result<String> roleCode = crmManager.createRoleCode(fsEa, eieaConverter.enterpriseAccountToId(fsEa), Lists.newArrayList(1001), "99");
        }
    }

    //角色创建
    @Test
    public void testRemoveRole() {
        crmManager.removeRoleCode("dtszsh1884", 83031, Lists.newArrayList(1004), 79788L);
    }


    @Test
    public void testCreateOrder() {
//        EnterpriseModel enterpriseModel=new EnterpriseModel();
//        String ea = crmManager.createCustomEnterpriseAccount("KK测试企业");
//        enterpriseModel.setEa(ea);
//        enterpriseModel.setBeginTime(1619671845186L);
//        enterpriseModel.setExpireTime(1622263845000L);
//        enterpriseModel.setDingCorpId("ding0c8ee222b91a38265f2c783f7214b6d6119");
//        enterpriseModel.setManagerName("柯南颖");
//        enterpriseModel.setMobile("***********");
//        enterpriseModel.setOrderId("8E4DD3DFE04AAF2F88A4D7DE67");
//        enterpriseModel.setResoueceCount(5);
//        enterpriseModel.setOrderAmount("0.00");
//        AddCrmOrderDto.Result crmOrder = crmManager.createCrmOrder(enterpriseModel, "5c90b2a77cfed910a04f94dd");
////        log.info("crmOrder:{}",crmOrder);


    }
    
    @Test
    public void testCrmManager(){
        //openSyncBizDataDao.updateModifyTimeByBizId(getAfterTime(),"2022315128059570209","dinge746c724519c3388acaaa37764f94726");
        CreateCustomerArg createCustomerArg = CreateCustomerArg.builder().enterpriseAccount("dtbbddzy123").
                enterpriseName("贝贝钉钉专用测试企业3").managerName("贝贝").source(1).outEid("ding3ce62d40b921b195bc961a6cb783455b001").build();

        Result<String> customer = crmManager.createCustomer(createCustomerArg);
        log.info("customer");
    }
    private Date getAfterTime() {
        Date date = new Date();
        Date afterDate = new Date(date.getTime() + 300000);
        return afterDate;
    }
    @Test
    public void testRole(){
        crmManager.addManagerRole(84375,1017);
    }


    @Test
    public void testOkhttp() {
        String url = "http://172.17.4.230:36337/versionRegisterService/crmOrderToDetail";
        Map<String, Object> bodyMap = Maps.newHashMap();
        bodyMap.put("enterpriseAccount", "dingkncsqy");
        bodyMap.put("enterpriseName", "柯南测试企业");
        bodyMap.put("managerMobile", "***********");
        bodyMap.put("managerName", "柯南颖");
        bodyMap.put("outEid", "ding0c8ee222b9a38265f2c783f7214b6d69");
        String result = httpManager.postUrl(url, bodyMap, createHeader());
        log.info("result:{}", result);
    }

    @Test
    public void testGoodsCode(){
        String objectData="{\"DT_GOODS_881621847020813_572028\":[\"5c90b2a57cfed910a04f92be\"],\"DT_GOODS_881621847020813_572029\":[\"5c90b2a57cfed910a04f92be\"],\"DT_GOODS_881621847020813_572030\":[\"5c90b2a57cfed910a04f92be\"],\"DT_MOCK_UPGRADE\":[\"5c90b2a57cfed910a04f92be\"],\"DT_SERVICE_CODE\":[\"5e731bb9530409000163ef16\"]}";
        Map<String, List<String>> goodsMap = JSONObject.parseObject(objectData, new TypeReference<Map<String, List<String>>>() {
        });
        log.info("goods");
    }

    public static Map<String, String> createHeader() {
        Map<String, String> headers = Maps.newHashMapWithExpectedSize(1);
        headers.put("Content-Type", "application/json");
        return headers;
    }

    @Test
    public void testJson() {
        String plainText = "{\n" +
                "    \"EventType\": \"SYNC_HTTP_PUSH_HIGH\",\n" +
                "    \"bizData\": [\n" +
                "        {\n" +
                "            \"gmt_create\": 1620195884000,\"biz_type\":2,\"open_cursor\":0,\"subscribe_id\":\"16740006_0\",\"id\":150850,\"gmt_modified\":1620195884000,\"biz_id\":\"16740006\",\"biz_data\":\"{\\\"syncAction\\\":\\\"suite_ticket\\\",\\\"suiteTicket\\\":\\\"IcN3uU7x4HHmO9n1WAOXGXNnkaQnO4RnC0mVs30PeAch0vWLYhLXLTgHCfCYlBCnIDa2wtJDWGPpBmpmhsm24p\\\",\\\"syncSeq\\\":\\\"CD8B5832B6AF84DEE8CA8D33A9\\\"}\",\n" +
                "                \"corp_id\": \"ding0c8ee222b9a38265f2c783f7214b6d69\",\n" +
                "                \"status\": 0\n" +
                "            }\n" +
                "    ]\n" +
                "}";

        JSONObject jsonObject = JSONObject.parseObject(plainText);
        log.info("dingcallBack plaintext:{}", plainText);
        if (!ObjectUtils.isEmpty(jsonObject.get("bizData"))) {
            List<String> voList = JSONArray.parseArray(jsonObject.get("bizData").toString(), String.class);
            List<HighBizDataVo> dataVos = Lists.newArrayList();
            voList.stream().forEach(item -> {
                HighBizDataVo bizDataVo = JSONObject.parseObject(item, HighBizDataVo.class);
                dataVos.add(bizDataVo);
                dataVos.add(bizDataVo);
            });

            Result<Integer> result = syncBizDataService.batchSaveData(dataVos);
            log.info("result:{}", result);
        }
    }

    @Test
    public void testQuery() {
        Result<PollingSyncResult> syncResult = pollingSyncService.queryPolling(BizDataTypeEnum.MEDIUM_LEVEL.getType());
        Long lastSyncTime = syncResult.getData().getLastSyncTime();
        Result<List<HighBizDataVo>> listResult = syncBizDataService.batchMediumQueryData();
        log.info("listResult:{}", listResult);
    }

    @Test
    public void getSuiteAccessToken() {
        Result<String> tokenResult = dingManager.getAccessToken("dingfeca3fa3352c7d4ca39a90f97fcb1e09", ConfigCenter.CRM_SUITE_ID);
        log.info("result:{}",tokenResult);
//        log.info("accessToken:{}",accessToken);

    }
    @Test
    public void testRedis(){
        String cacheKey=String.format(Constant.DING_CORP_SUITE_TOKEN,"dinge852faff822b4cdcacaaa37764f94726","19071005");
        Result<Void> result = redisManager.deleteInfos(cacheKey);
        Result<String> result1 = redisManager.getInfoFromRedis(cacheKey);
        log.info("result");
    }



    @Test
    public void testTicket() {
        Result<String> ticket = syncBizDataService.queryByTicket(ConfigCenter.CRM_SUITE_ID);
        log.info("high:{}", ticket);
    }

    @Test
    public void testGetEnterPriseData() {
        AuthEnterPriseModel.BizData enterPriseModel = dingManager.getEnterPriseData("dingacfeafbb16cff7b8f5bf40eda33b7ba0",ConfigCenter.CRM_SUITE_ID);
        log.info("enterPriseModel:{}", enterPriseModel);
    }


    @Test
    public void testGetDept() {
        List<Dept> depts = dingManager.queryDingDept("dingf6cc1d9dfe8223b6f2c783f7214b6d69", Lists.newArrayList(1L),ConfigCenter.CRM_SUITE_ID);
    }

    @Test
    public void name() {
        System.out.println(dingManager.getAllVisibleDepts("dingfeca3fa3352c7d4ca39a90f97fcb1e09", ConfigCenter.CRM_SUITE_ID));
    }

    @Test
    public void queryDetail() {
        Dept dept = dingManager.queryDeptDetail("dingcc7ead824f9fa7e0a39a90f97fcb1e09", 485339126L,ConfigCenter.CRM_SUITE_ID);
        log.info("dept:{}", dept);
    }

    @Test
    public void initAllDept() {
        List<Dept> allDept = dingManager.getListDeptByScope(Lists.newArrayList(1L), "ding68a9ef40f35d7ded4ac5d6980864d335",ConfigCenter.CRM_SUITE_ID);
        log.info("allDept:{}", allDept);
    }

    @Test
    public void queryDeptUser() {
        List<EmployeeDingVo> employeeDingVoList = dingManager.queryUserByDept("dingcc7ead824f9fa7e0a39a90f97fcb1e09", 484866820L,ConfigCenter.CRM_SUITE_ID);
    }

    @Test
    public void convertEa() {
        try {
            int result = eieaConverter.enterpriseAccountToId("ddqybjydfxkjyxzrgs");
            log.info("result");
        } catch (Exception e) {
//            e.printStackTrace();
            log.info("error convert");
        }

    }

    @Test
    public void setUserRole() {
        CrmUserRoleArg crmUserRoleArg = new CrmUserRoleArg();
        crmUserRoleArg.setRoleCode("00000000000000000000000000000015");
        crmUserRoleArg.setUserIds(Lists.newArrayList(1011, 1010));
        Map<String, String> headers = Maps.newHashMap();
        headers.put("content-type", "application/json");
        headers.put("x-app-id", "CRM");
        headers.put("x-fs-ea", "dingszjrkj");
        headers.put("x-fs-ei", String.valueOf(82365));
        headers.put("x-fs-userinfo", String.valueOf(-10000));
        String url = ConfigCenter.USE_ROLE_URL + "user_role";
        String result = httpManager.postUrl(url, JSONObject.toJSONString(crmUserRoleArg), headers);
        log.info("result:{}", result);
    }


    @Test
    public void removeRole() {
        CrmUserRoleArg crmUserRoleArg = new CrmUserRoleArg();
        String roleCode = Optional.ofNullable(ConfigCenter.ROLE_MAP.get(70480)).orElseGet(() -> "00000000000000000000000000000015");
        crmUserRoleArg.setRoleCode(roleCode);
        crmUserRoleArg.setUserIds(Lists.newArrayList(1000));
        Map<String, String> headers = Maps.newHashMap();
        headers.put("content-type", "application/json");
        headers.put("x-app-id", "CRM");
        headers.put("x-fs-ea", "dingkncsqy");
        headers.put("x-fs-ei", String.valueOf(82343));
        headers.put("x-fs-userinfo", String.valueOf(-10000));
        String url = ConfigCenter.USE_ROLE_URL + "user_role/delete_users";
        String result = httpManager.postUrl(url, JSONObject.toJSONString(crmUserRoleArg), headers);
        log.info("result:{}", result);
    }

    @Test
    public void addEmp() {
        List<EmployeeDingVo> authEmp = dingManager.getDingEmp("ding0c8ee222b9a38265f2c783f7214b6d69", Lists.newArrayList("02260405201328731083"),"16740006");
        Result<Integer> createEmpResult = cloudEmpService.cloudCreateEmp(authEmp.get(0), 82365, 70480L, "dinge9ad97cd998113e3ee0f45d8e4f7c288");
    }

    @Test
    public void testAdmin(){
        dingManager.getListAdmins("dingfeca3fa3352c7d4ca39a90f97fcb1e09",ConfigCenter.CRM_SUITE_ID);
    }

    @Test
    public void testUUid() {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        log.info("uuid");
    }

    @Test
    public void testDept() {
        dingManager.queryDeptParents("dinge9ad97cd998113e3ee0f45d8e4f7c288", 487931823L,ConfigCenter.CRM_SUITE_ID);

    }
    @Test
    public void testTodo(){
        CreateTodoArg todoArg=new CreateTodoArg();
        todoArg.setEa("ddqybhzyl1");
        todoArg.setEi(823791);
        todoArg.setSenderId(-10000);
        todoArg.setReceiverIds(Lists.newArrayList(100000, 1004, 1000001));
        todoArg.setSourceId("60a6829653f26e125919c30b");
        todoArg.setBizType("452");
        todoArg.setUrl("todo?apiname=AccountObj&id=60a68293f938e300014f8202&ea=ddqybhzyl");
        todoArg.setTitle("待处理的CRM审批流程");
        todoArg.setContent("新建客户(2021-05-20 23:39)");
        List<KeyValueItem> keyValueItems=Lists.newArrayList();
        KeyValueItem item=new KeyValueItem("成交状态","未成交");
        KeyValueItem item1=new KeyValueItem("负责人","柯南颖AJMAN");
        KeyValueItem item2=new KeyValueItem("流程主题","新建客户(2021-05-20 23:39)");
        KeyValueItem item3=new KeyValueItem("客户名称","花海");
        keyValueItems.add(item);
        keyValueItems.add(item1);
        keyValueItems.add(item2);
        keyValueItems.add(item3);
        todoArg.setForm(keyValueItems);

        cloudExternalTodoService.createTodo(todoArg);
    }

    @Test
    public void testJsSignature(){
//        String moreApp="{\"DT_GOODS_881621847020813_572028\":[\"609d087534a5d700013d4027\"],\"DT_GOODS_881621847020813_572029\":[\"609d087534a5d700013d4027\"],\"DT_GOODS_881621847020813_572029_UPGRADE_STANDARDPRO\":[\"60ae04360f485a0001b3a4a0\"],\"DT_GOODS_881621847020813_572029_UPGRADE_STRENGTHEN\":[\"5c90b2a57cfed910a04f92be\"],\"DT_GOODS_881621847020813_572030\":[\"609d08d08d72c3000147c51f\"],\"DT_GOODS_881621847020813_572030_UPGRADE_STRENGTHEN\":[\"611141e32a35ac00010a58ea\"],\"DT_GOODS_881621847020813_747007\":[\"6111012091a23a0001107229\"],\"DT_GOODS_881624603524784_654001\":[\"6112474cb83a900001abf393\"],\"DT_SERVICE_CODE\":[\"609d087534a5d700013d4027\"]}";
//
//        Map<String, List<String>> MORE_APP_NEIGHBOR = JSONObject.parseObject(moreApp, new TypeReference<Map<String, List<String>>>() {
//        });
//        log.info("moreApp");

        Result<DingAppResult> result = dingAuthServiceImpl.createJsApiSignature("https://www.ceshi112.com/hcrm/dingtalk/eservice", "ddqybhzyl", 79788L);
        log.info("result:{}",result);
    }

    @Test
    public void testTodoByISV() {
        CreateTodoArg arg = new CreateTodoArg();
        arg.setEa("ddqybhzyl");
        arg.setEi(82379);
        arg.setContent("最后一次发出的代办任务");
        arg.setTitle("待处理的CRM审批流程");
        arg.setSourceId("00005");
        arg.setBizType("401");
        List<Integer> list = new LinkedList<>();
//        list.add(1064);
        list.add(100000);
//        list.add(2156);
        list.add(1041);
//        list.add(2161);
//        list.add(1241455);
        list.add(1009);
        // list.add(1000);
//         list.add(1007);
//         list.add(100001);
        arg.setReceiverIds(list);
        arg.setForm(Lists.newArrayList(new KeyValueItem("流程主题", "zongxin chen"), new KeyValueItem("姓名", "王一三"), new KeyValueItem("不能出现在标题中", "不能出现在标题中")));
        CreateTodoResult todo = cloudExternalTodoService.createTodo(arg);
        System.out.println(todo);
    }

    @Test
    public void testEi() {
        Result<DingEnterpriseResult> mappingEnterprise = objectMappingService.queryEnterpriseByEi(83384, "");
        System.out.println(mappingEnterprise);
    }

    @Test
    public void getSourceCount() {
        Result<Integer> result = objectMappingService.getSourceCount(83384, "61b6ff73b88ad244c41b3690", "");
        System.out.println(result);
    }

    @Test
    public void queryUserId() {
        Result<String> result = objectMappingService.queryUserId(83384, 1006, "");
        System.out.println(result);
    }

    @Test
    public void queryUserIds() {
        Result<List<String>> result = objectMappingService.queryUserIds(83384, Lists.newArrayList(1006), "");
        System.out.println(result);
    }




    @Test
    public void getDingTaskVo() {
        Result<List<DingTaskVo>> result = objectMappingService.getDingTaskVo(83384, "61b70296b88ad244c41b37f7", "");
        System.out.println(result);
    }

    @Test
    public void getUserDetail() {
        Result<UserVo> result = objectMappingService.getUserDetail("ffec3576ce143da09b6f7bdaaca08cf5", "manager1076", "http://www.ceshi112.com/dingtalk/", "");
        System.out.println(result);
    }

    @Test
    public void testDeal(){
        DealTodoArg vag = new DealTodoArg();
        vag.setEa("ddqybhzyl");
        vag.setEi(82379);
        vag.setSourceId("00005");
        vag.setBizType("401");
        vag.setOperators(Lists.newArrayList(10641, 45454, 5456456, 2156, 2161));
        DealTodoResult result = cloudExternalTodoService.dealTodo(vag);
        System.out.println(result);
    }

    @Test
    public void testDelete(){
        DeleteTodoArg vag = new DeleteTodoArg();
        vag.setEa("ddqybhzyl");
        vag.setEi(82379);
        vag.setSourceId("00005");
        vag.setBizType("401");
        vag.setDeleteEmployeeIds(Lists.newArrayList(45555, 10641,1234565, 21561, 15645, 21611, 10411, 10509));
        cloudExternalTodoService.deleteTodo(vag);
    }

    @Test
    public void testInit(){
        Integer result = crmSyncObjService.initSetting("dingfeca3fa3352c7d4ca39a90f97fcb1e09", 82379);
        log.info("result");
    }

    @Test
    public void testData(){
        String data="{\"result\":[{\"creatorUserId\":\"manager7605\",\"gmtModified\":1641905322000,\"data\":{\"customer_follow_up_status\":{\"extendValue\":\"{\\\"extension\\\":{\\\"editFreeze\\\":true},\\\"label\\\":\\\"新获取\\\",\\\"key\\\":\\\"option_new_acquisition\\\"}\",\"value\":\"新获取\"},\"customer_name\":\"数据同步\"},\"formCode\":\"PROC-CA5E9C3D-C96F-4E0E-9F33-7A4C2E9C3556\",\"extendData\":{\"biz_customer_last_status_change_dateV2\":\"1641905322393\",\"biz_customer_biz_scene\":\"crm_customer\"},\"permission\":{\"participantStaffIds\":[],\"ownerStaffIds\":[\"manager7605\"]},\"gmtCreate\":1641905321000,\"orgId\":314091096,\"objectType\":\"crm_customer\",\"procInstStatus\":\"COMPLETED\",\"appUuid\":\"SWAPP-45C4B36964CB10B0DA7E216EFABF2E9E\",\"instanceId\":\"3bf62ea0-3731-4b3e-9693-5626c9d441e9\",\"procOutResult\":\"agree\"}],\"syncAction\":\"ding_paas_object_data_update\",\"success\":true,\"dingOpenErrcode\":0,\"syncSeq\":\"**************************\"}";
        Map<String,Object> map= JsonPath.read(data,"$.result[0]");
        DingObjData dingObjData = JSONObject.parseObject(JSONObject.toJSONString(map), new TypeReference<DingObjData>() {
        });
        log.info("dingObj");
    }

    @Test
    public void testManager(){
        Map<String, Object> dataMap=Maps.newHashMap();
        dataMap.put("customer_name",null);
        dataMap.put("company",null);
        dataMap.put("account_name",null);
        String s = convertCustomerName(dataMap);
    }
    private String convertCustomerName(Map<String, Object> dataMap) {
        StringBuilder complexCustomerName = new StringBuilder();
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(dataMap.get("customer_name"))) {
            complexCustomerName.append(dataMap.get("customer_name").toString());
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(dataMap.get("company"))) {
            complexCustomerName.append("-").append(dataMap.get("company").toString());
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(dataMap.get("account_name"))) {
            complexCustomerName.append("-").append(dataMap.get("account_name").toString());
        }
        if (StringUtils.isEmpty(complexCustomerName.toString())) {
            complexCustomerName .append("未知") ;
        }
        //去掉首尾-；
        String finalCustomerName = complexCustomerName.toString().startsWith("-") ? complexCustomerName.substring(1) : complexCustomerName.toString();
        return finalCustomerName;
    }

    @Test
    public void messageSendTest() {
//        SendTextCardMessageArg arg = new SendTextCardMessageArg();
//        arg.setEi(82379);
//        arg.setEa("ddqybhzyl");
//        arg.setReceiverIds(Lists.newArrayList(2161));
//        arg.setMessageContent("钉钉bi文件111");
//        arg.setTitle("钉钉bi文件11");
//        arg.setUrl("ava-bi-message?id=BI_64101b00e3c8c10001346bb3&diagram=1&from=subscription&dataType=2");
//        arg.setGenerateUrlType(4);
//        externalMessageService.sendTextCardMessage(arg);

        SendTextCardMessageArg arg = new SendTextCardMessageArg();
        arg.setEi(82379);
        arg.setEa("ddqybhzyl");
        arg.setReceiverIds(Lists.newArrayList(2161));
        arg.setMessageContent("钉钉bi文件1111");
        arg.setTitle("钉钉bi文件11");
        Map<String, String> extraDataMap = new HashMap<>();
        extraDataMap.put("filePath", "N_202304_04_704867498c3f49e3ad766b8bfdd4d3bf.xlsx");
        arg.setExtraDataMap(extraDataMap);
        arg.setUrl("https://www.ceshi112.com/dps/preview/bypath?path=N_202304_04_704867498c3f49e3ad766b8bfdd4d3bf.xlsx&showHeader=1");
        arg.setGenerateUrlType(5);
        externalMessageService.sendTextCardMessage(arg);
    }

    @Test
    public void testMessageCard() {
//        String x = "{\"ea\":\"ddqybhzyl\",\"ei\":82379,\"extraDataMap\":{\"objectApiName\":\"DeliveryNoteObj\",\"workflowInstanceId\":\"6638be95f73f0432dabe09cb\",\"objectId\":\"65a4f287b1aa0f000129e8ee\"},\"generateUrlType\":1,\"groupKeys\":[],\"messageContent\":\"审批意见：\",\"receiverChannelData\":\"{\\\"appId\\\":\\\"approval\\\"}\",\"receiverChannelType\":1,\"receiverIds\":[2161],\"senderId\":0,\"title\":\"wjh 同意了审批: 发货\",\"url\":\"todo?apiname=DeliveryNoteObj&id=65a4f287b1aa0f000129e8ee&ea=90061&ea=90061\"}";
//        SendTextCardMessageArg arg = new Gson().fromJson(x, SendTextCardMessageArg.class);
//        externalMessageService.sendTextCardMessage(arg);

        SendTextCardMessageArg arg = new SendTextCardMessageArg();
        arg.setGenerateUrlType(4);
        arg.setEi(82379);
        arg.setEa("ddqybhzyl");
        arg.setReceiverIds(Lists.newArrayList(2161));
        arg.setMessageContent("测试123");
        arg.setTitle("待办测试");
        arg.setUrl("ava-bi-message?id=BI_66b34e3939dda80001f9cb43&diagram=1&from=subscription&dataType=2");
        Map<String,String> data = Maps.newHashMap();
        data.put("diagram","1");
        data.put("dataType","2");
        data.put("viewUrl","https://www.ceshi112.com/h5app/bi-report?fromapp=1#/report/BI_66b34e3939dda80001f9cb43?from=subscription&schType=1&dataType=2");
        data.put("from","subscription");
        data.put("subscriptionId","BI_66b34e3939dda80001f9cb43");
        arg.setExtraDataMap(data);
        externalMessageService.sendTextCardMessage(arg);
    }

    @Test
    public void deleteEmpByFsId() {
        Result<Integer> result = objectMappingService.deleteEmpByFsId(11111, Lists.newArrayList(11111));
        System.out.println(result);
    }


}
