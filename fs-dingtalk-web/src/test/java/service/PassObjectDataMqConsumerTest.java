//package service;
//
//import com.facishare.open.ding.cloud.paas.PaasObjectDataMqConsumer;
//import com.facishare.open.ding.cloud.paas.models.ObjectDataMqData;
//import com.facishare.open.ding.cloud.paas.models.ObjectDataMqData.EventObject;
//import com.facishare.open.ding.cloud.paas.models.ObjectDataMqData.Context;
//import com.google.common.collect.Lists;
//import org.junit.Before;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//public class PassObjectDataMqConsumerTest extends BaseAbstractTest {
//    @Autowired
//    private PaasObjectDataMqConsumer paasObjectDataMqConsumer;
//
//    @Before
//    public void init() {
//    }
//
//    @Test
//    public void processMessage() throws Throwable {
//        Context context = new Context();
//        context.setAppId("CRM");
//        context.setTenantId("81243");
//        context.setUserId("-10000");
//        EventObject eventObject = new EventObject();
//        eventObject.setContext(context);
//        eventObject.setEntityId("AccountObj");
//        eventObject.setObjectId("60769258f6f275000155a3a5");
//        ObjectDataMqData message = new ObjectDataMqData();
//        message.setOp("i");
//        message.setBody(new EventObject[] {eventObject});
//        paasObjectDataMqConsumer.batchProcessMessage(Lists.newArrayList(message));
//    }
//}
