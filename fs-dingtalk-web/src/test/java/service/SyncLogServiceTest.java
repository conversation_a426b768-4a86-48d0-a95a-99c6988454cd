package service;

import base.BaseAbstractTest;
import com.facishare.open.ding.api.service.SyncLogService;
import com.facishare.open.ding.api.vo.SyncSettingVo;
import com.facishare.open.ding.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-08-28 19:14
 */
@Slf4j
public class SyncLogServiceTest extends BaseAbstractTest {
    @Autowired
    private SyncLogService syncLogService;

    @Test
    public void testQuerySyncLog(){
        SyncSettingVo vo = new SyncSettingVo();
        vo.setEi(71658);
        vo.setApiNameValue(10);
        Result<Map<String, Object>> result = syncLogService.querySyncLog(vo);

    }
}
