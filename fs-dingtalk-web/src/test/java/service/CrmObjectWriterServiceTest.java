package service;


import base.BaseAbstractTest;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.ding.api.model.StandardData;
import com.facishare.open.ding.api.service.CrmObjectWriterService;
import com.facishare.open.ding.common.result.Result;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class CrmObjectWriterServiceTest extends BaseAbstractTest {

    @Autowired
    CrmObjectWriterService objectWriterService;

    @Test
    public void writeCrmObjectData() throws JsonProcessingException {

        String testJson="{\"detailFieldVals\":{},\"masterFieldVal\":{\"_id\":\"5GZdmKy3RrWjLJRrWHRO9A03541657265664\",\"address\":\"天津天津市河东区东新街道11111111111111111111\",\"TextareaField-K55CWZ2E\":\"1asdfgad\",\"DDSelectField-K2U5GX3B\":\"option_K2U5LXIO\",\"customer_follow_up_status\":\"option_1\",\"creator_userid\":\"151863264421043569\",\"DDSelectField-K371T4RY\":\"option_1\",\"customer_name\":\"测试客户220708\",\"DDSelectField-K2U5GX39\":\"option_K2U5KPJU\",\"TextField_1DJNCI07S9OG0\":\"waewae\",\"DDSelectField-K55CWZ2C\":\"option_1\"},\"objAPIName\":\"AccountObj\"}";

        String corpId="dingfeca3fa3352c7d4ca39a90f97fcb1e09";
        StandardData standardData = JSONObject.parseObject(testJson, new TypeReference<StandardData>() {
        });
        Result result = objectWriterService.writeCrmObjectData(82379, corpId,standardData, null);
        log.info("{}",result);
    }


}
