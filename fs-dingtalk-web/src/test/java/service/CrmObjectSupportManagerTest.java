//package service;
//
//import com.facishare.open.ding.api.constants.CrmObjectApiName;
//import com.facishare.open.ding.cloud.paas.support.CrmObjectSupportManager;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//public class CrmObjectSupportManagerTest extends BaseAbstractTest {
//    @Autowired
//    private CrmObjectSupportManager crmObjectSupportManager;
//
//    @Test
//    public void createDefineObject() {
//        boolean result = crmObjectSupportManager.isObjCreate(81243, CrmObjectApiName.CUSTOMER_CATEGORY_API_NAME);
//        if(result==false) {
//            result = crmObjectSupportManager.createDefineObject(81243,CrmObjectApiName.CUSTOMER_CATEGORY_API_NAME);
//            System.out.println(result);
//        }
//    }
//}
