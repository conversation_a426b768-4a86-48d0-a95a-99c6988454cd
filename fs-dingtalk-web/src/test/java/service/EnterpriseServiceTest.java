package service;

import base.BaseAbstractTest;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.response.OapiSnsGetuserinfoBycodeResponse;
import com.facishare.open.ding.api.result.ConditionEmployeeResult;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.OrderInfoResult;
import com.facishare.open.ding.api.service.EnterpriseService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.service.cloud.CloudDeptService;
import com.facishare.open.ding.api.vo.*;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.model.ScopeVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.dao.OrderInfoDao;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.entity.DingDeptEntity;
import com.facishare.open.ding.provider.manager.DingDeptMananger;
import com.facishare.open.ding.provider.utils.DateUtils;
import com.facishare.open.ding.api.utils.HttpRequestUtils;
import com.facishare.open.webhook.messagesend.service.ExternalOaTodoService;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.ModifiedDepartment;
import com.facishare.organization.api.model.department.arg.BatchModifyDepartmentResumeArg;
import com.facishare.organization.api.model.department.arg.GetAllDepartmentNameArg;
import com.facishare.organization.api.model.department.result.BatchModifyDepartmentResumeResult;
import com.facishare.organization.api.model.department.result.GetAllDepartmentNameResult;
import com.facishare.organization.api.model.param.CreateDepartment;
import com.facishare.organization.api.model.param.ModifyDepartment;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseEditionArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseEditionResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.message.extrnal.platform.api.ExternalMessageService;
import com.fxiaoke.message.extrnal.platform.model.KeyValueItem;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>类的详细说明</p>
 *
 * @<NAME_EMAIL>
 * @version 1.0
 * @dateTime 2018/7/12 11:30
 */
@Slf4j
public class EnterpriseServiceTest extends BaseAbstractTest {

    @Autowired
    private EnterpriseService enterpriseService;
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private ExternalOaTodoService externalOaTodoService;

    @Autowired
    private DingDeptMananger dingDeptMananger;

    @Autowired
    private DepartmentProviderService departmentProviderService;

    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

    @Resource(name = "cloudExternalMessageServiceImpl")
    private ExternalMessageService externalMessageService;

//    @Autowired
//    private CircleService circleService;
    @Autowired
    private CloudDeptService cloudDeptService;
    @Autowired
    private OrderInfoDao orderInfoDao;

    @Test
    public void queryKcEnterpriseTest() {
        Result<DingEnterpriseResult> result = enterpriseService.queryEnterpriseByEa("fktest", null);
        Assert.assertTrue("queryKcEnterprise failed", result.isSuccess());
    }

    @Test
    public void orderTest() {
        OrderInfoResult result = orderInfoDao.queryLastOrderByCorpId("ding3959290e81fe7b844ac5d6980864d335", "16896002");
        log.info("result");
    }

    @Test
    public void startConnectTest() {
        ConnectionVo vo = new ConnectionVo();
        vo.setEa("80164");
        vo.setEi(80164);
        vo.setDingCorpId("ding5fe5aa1ba078facc24f2f5cc6abecb85");
        vo.setAgentId("824984539");
        vo.setAppKey("dingskqye5ljk4pogfbf");
        vo.setAppSecret("D4F0V5a8-HRUvXck3LDtP_AGw7UDQP_D_0k2l8kmttFOURFIYyrJkUcBGvvt_OwL");
        vo.setClientIp("http://**************:8089/");
        vo.setRedirectAppId("dingoac1a85lcn4qwqzqgd");
        vo.setRedirectAppSecret("SLLiwXKPjaAH42lSp-QgWXxeaMvliZw1JBYeS_HLxKdsX9sdxaFYlqAEEUA4xhtf");
        Result result = enterpriseService.startConnect(vo);
    }

    @Test
    public void testMessage(){
        /**
         * (ea=zjrobam, ei=708205, sender
         * Id=0, receiverIds=[9813], title=您发起的审批：项目报备审核流程（全公司） 已同意，请知晓, messageContent=同意, url=todo?apiname
         * =NewOpportunityObj&id=60555de1a45d440001470aee&ea=zjrobam, form=null, receiverChannelType=1, receiverChannelData={"appId":"app
         * roval"})
         */
        SendTextCardMessageArg arg=new SendTextCardMessageArg();
        arg.setEi(708205);
        arg.setEa("zjrobam");
        arg.setSenderId(0);
        arg.setReceiverIds(Lists.newArrayList(9813));
        arg.setUrl("todo?apinameNewOpportunityObj&id=60555de1a45d440001470aee&ea=zjrobam,");
        arg.setMessageContent("todo?apinameNewOpportunityObj&id=60555de1a45d440001470aee&ea=zjrobam,");
        arg.setForm(null);
        arg.setReceiverChannelData("app");
        externalMessageService.sendTextCardMessage(arg);

    }

    @Test
    public void testEnterPrise(){
        Long aLong = DateUtils.timeStamp("Mon May 24 23:42:17 CST 2021");
        log.info(aLong+"");
    }




    @Test
    public void startLinkConnect() {
        ConnectionVo vo = new ConnectionVo();
        vo.setEa("76246");
        vo.setEi(76246);
        vo.setEnterpriseName("测试企业14");
        vo.setEmployeeId(1000);
        vo.setDingCorpId("ding5fe5aa1ba078facc24f2f5cc6abecb85");
        vo.setAgentId("824708305");
        vo.setAppKey("dingf7ywauounqzphruj");
        vo.setAppSecret("dCV7hMkc_uPMa4cT6_HG1IuxkG5b1cU7icUp0GQHJQQBLtHytX6KUknAaDmi18TW");
        vo.setClientIp("http://**************:8089/");
        vo.setRedirectAppId("dingoa4iz5doprciob2hif");
        vo.setRedirectAppSecret("R1Mjd7LrTdKnU72xaQyfg0ZPZVwVuqk5Rfp2zpDr49QmkqNJAUl3q0e7of1j_R-U");
        vo.setDevModel(1);
        Result result = enterpriseService.startConnect(vo);

    }

    @Test
    public void testCondition() {
        QueryEmployeeVo vo = new QueryEmployeeVo();
        vo.setBindStatus(0);
        vo.setDingDeptId(1L);
        Result<ConditionEmployeeResult> conditionEmployeeResultResult = objectMappingService.conditionEmployee(vo, 78527, "");
    }


    @Test
    public void updateConnectionTest() {
        ConnectionVo vo = new ConnectionVo();
        vo.setEa("lkkcrm");
        vo.setEi(519454);
//        vo.setEa("asdff");
//        vo.setEmployeeId(12);
//        vo.setAppSecret("7502de9bcca14fa682937d09356679f1");
        vo.setAppKey("dingnvonwv9p4v7ghe20");
        vo.setAppSecret("Oag4r71sverQvhjg_gbzcOTXHCt0dch5o2e1yVd3PxmmtbpgE4avh_dSGKGZVQz2");
        vo.setClientIp("http://*************:8089/");
        vo.setDingCorpId("dingb3e22811353e0b6935c2f4657eb6378f");
        vo.setRedirectAppId("dingoatbzzz10d3opebhqz");
        vo.setRedirectAppSecret("0gFQ6ux6bSlBwXEtlweMyD7h5h-hMU92zuDOKkDsvKHqIRpN1C9W0HqpJE9cbhU0");
        vo.setAgentId("802181847");
        Result result = enterpriseService.updateConnection(vo);

    }

    @Test
    public void updateConnectionSelf() {
        ConnectionVo vo = new ConnectionVo();
        vo.setEa("fktest018");
        vo.setEi(589994);
//        vo.setEa("asdff");
//        vo.setEmployeeId(12);
//        vo.setAppSecret("7502de9bcca14fa682937d09356679f1");
        vo.setAppKey("dingnhstlt2v84bm0e7a");
        vo.setAppSecret("GstI7zALWFiy7apGChEaD7P2SKHjuA-YfcclnlLh_YGEbpJMZw6kk-__enrvNbV8");
        vo.setClientIp("http://**************:8089/");
        vo.setDingCorpId("dingb3e22811353e0b6935c2f4657eb6378f");
        vo.setRedirectAppId("dingoancfcoxbuo2stkmo9");
        vo.setRedirectAppSecret("J0yvqzPXdZOqQ4_uR2GeblTYSHs9TWkraD-EYxrlgcW4ELvqNa9VUQOjJD325Y8j");
        vo.setAgentId("798363804");
        Result result = enterpriseService.updateConnection(vo);

    }


    @Test
    public void testQueryDept() {
//        Result result = enterpriseService.queryDeptLevelOne(71646);
//        log.info("result={}",result);
        Gson gson = new Gson();
        //验证appKey和appSecret
        String clientUrl = DingRequestUtil.appendUrl("**********:8099");
        Map<String, Object> getTokenArg = new HashMap<>();
        getTokenArg.put("type", "GET");
        getTokenArg.put("token", "Ly2HXCIZG7/QlpFaFpUqaNxiKnzMq/eWQwVTNbjgF9Z9AcyFiQNHUvQgBir3geA0REYTme85lO6ivEcEEdIdwMk7DCy3NNv++dyE+6YT1NVGVt8/Pl3QFNUM+ph9HaN/OcJ1gwRv0t6X5i3IRTfAOpIfxs+1ng+jUc9RbyBgVm4=");
        String getTokenUrl = "http://oapi.dingtalk.com/gettoken?appkey=dingmstrwiyjiim35sqb&appsecret=TmqWEQb29RBeacUsKrvNXBDMBv0IYLy7mOWUZuxcUzEqVneqOo9Hfm8eqDXbCEld";
        getTokenArg.put("url", getTokenUrl);
        Object tokenResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(getTokenArg));
        JSONObject tokenJson = gson.fromJson(tokenResult.toString(), JSONObject.class);
        if (tokenJson.get("errcode").equals(HttpRequestUtils.DING_SUCCESS)) {
        }
        String accessToken = tokenJson.getString("access_token");
        System.out.println(accessToken);
    }

    @Test
    //测试获取钉钉的总人数
    public void testEmpCount(){
        Gson gson = new Gson();
        String clientURl=DingRequestUtil.appendUrl("http://**************:8089/");
        Map<String, Object> getTokenArg = new HashMap<>();
        getTokenArg.put("type", "GET");
        getTokenArg.put("token", "Ly2HXCIZG7/QlpFaFpUqaNxiKnzMq/eWQwVTNbjgF9Z9AcyFiQNHUvQgBir3geA0REYTme85lO6ivEcEEdIdwMk7DCy3NNv++dyE+6YT1NVGVt8/Pl3QFNUM+ph9HaN/OcJ1gwRv0t6X5i3IRTfAOpIfxs+1ng+jUc9RbyBgVm4=");
        String getCountUrl = "https://oapi.dingtalk.com/user/get_org_user_count?access_token=22db7a8dd48d36b998cb531190253d14&onlyActive=0";
        getTokenArg.put("url", getCountUrl);
        Object tokenResult = DingRequestUtil.proxyRequest(clientURl, gson.toJson(getTokenArg));
        JSONObject tokenJson = gson.fromJson(tokenResult.toString(), JSONObject.class);
    }

    //测试全部拉取人数
    @Test
    public void testEmployeeInsert(){
        objectMappingService.allPullEmployeeInsert(79478,1000,"zslceshi018","76235");
    }




    @Test
    public void testEmployee() {
        BindEmpVo bindEmpVo = new BindEmpVo();
        bindEmpVo.setBindStatus(0);
        bindEmpVo.setDingEmployeeName("");
        bindEmpVo.setDingEmployeePhone("");
        bindEmpVo.setPageNumber(1);
        bindEmpVo.setPageSize(20);
//        objectMappingService.queryNewEmployee(bindEmpVo, 76235, "76235", 1000,"76235");
    }


    @Test
    public void testMapping(){
         LoadingCache<String, String> cache = Caffeine.newBuilder().maximumSize(10000).expireAfterWrite(80, TimeUnit.MINUTES).refreshAfterWrite(60, TimeUnit.MINUTES).build(new CacheLoader<String, String>() {
            @Nullable
            @Override
            public String load(@NonNull String key) throws Exception {
                Result<DingEnterpriseResult> dingEnterpriseResultResult = enterpriseService.queryEnterpriseByEi(Integer.valueOf(key));
                DingEnterpriseResult result = dingEnterpriseResultResult.getData();
                return DingRequestUtil.getToken(result.getClientIp(), result.getAppKey(), result.getAppSecret());
            }
        });

        String token = cache.get("76246");
        cache.put("76246","1123");

       String refreshToken= cache.get("76246");
        System.out.println("EnterpriseServiceTest.testMapping"+refreshToken);

    }




    //测试OATODO
    @Test
    public void oaToDo() {
        /**
         * (ea=zhanghui0916, ei=78762, senderId=1024, receiverIds=[1024], sourceId=5f07d219480c70000135574a, bizTy
         * pe=452, url=todo?apiname=AccountObj&id=5f07d2174b16ca0001993706&ea=zhanghui0916, title=待处理的CRM审批流程, content=测试钉钉03,
         * form=[KeyValueItem(key=发起流程数据, value=测试钉钉08),
         * KeyValueItem(key=流程关联对象, value=客户),
         * KeyValueItem(key=负责人, value=AJMAN),
         * KeyValueItem(key=任务开始时间, value=2020-07-10 10:27),
         * KeyValueItem(key=流程主题, value=客户新建&&编辑(2020-07-10 10:27)),
         * KeyValueItem(key=客户名称, value=测试钉钉88)])
         */
        CreateTodoArg createTodoArg = new CreateTodoArg();
        createTodoArg.setEa("76246");
        createTodoArg.setEi(76246);
        createTodoArg.setSenderId(1000);
        createTodoArg.setReceiverIds(Arrays.asList(1000));
        createTodoArg.setSourceId("5f07d219480c70000135574a");
        createTodoArg.setBizType("452");
        createTodoArg.setUrl("todo?apiname=AccountObj&id=5f07d2174b16ca0001993706&ea=zhanghui0916");
        createTodoArg.setTitle("待处理的CRM审批流程");
        createTodoArg.setContent("测试钉钉88");
        List<KeyValueItem> form = Lists.newArrayList();
        KeyValueItem item = new KeyValueItem("流程主题", "客户新建&&编辑(2020-07-10 10:27))");
        KeyValueItem item2 = new KeyValueItem("客户名称", "测试钉钉03");
        KeyValueItem item3 = new KeyValueItem("任务开始时间", "2020-07-10 10:27");
        KeyValueItem item4 = new KeyValueItem("负责人", "AJMAN");
        KeyValueItem item5 = new KeyValueItem("流程关联对象", "客户");
        KeyValueItem item6 = new KeyValueItem("发起流程数据", "测试钉钉89");
        form.add(item);
        form.add(item2);
        form.add(item3);
        form.add(item4);
        form.add(item5);
        form.add(item6);
        createTodoArg.setForm(form);
        externalOaTodoService.createTodo(createTodoArg);
    }

    //测试消息通知
    @Test
    public void toMessage(){
//        SendTextMessageArg sendTextMessageArg=new SendTextMessageArg();
////        sendTextMessageArg.setEa("76246");
////        sendTextMessageArg.setEi(76246);
////        sendTextMessageArg.setMessageContent("就想测试一下CRM通知111");
////        sendTextMessageArg.setReceiverIds(Lists.newArrayList(1011));
////        sendTextMessageArg.setReceiverChannelType(1);
////        String message="{\n" +
////                "    \"appId\":\"CRM\",  \"subAppId\":\"Bpm\"\n" +
////                "}";
////        sendTextMessageArg.setReceiverChannelData(message);
////        sendTextMessageArg.setSenderId(1000);
////        externalMessageService.sendTextMessage(sendTextMessageArg);
        DeptVo vo=new DeptVo();
        String owner=null;
        vo.setDingDeptOwner(Optional.ofNullable(owner).orElse(StringUtils.EMPTY));


    }

    /**
     * args=[CreateTodoArg(ea=76246, ei=76246, senderId=
     * -10000, receiverIds=[1011, 1000], sourceId=5f86b2e09061f60001b1e809, bizType=452, url=todo?apiname=AccountObj&id=5f86b2deb6ce5600012b55bb&ea=76246, title=待处理的CRM审批流程,
     * content=yuq1, form=[KeyValueItem(key=发起流程数据, value=yuq1), KeyValueItem(k
     * ey=所属业务对象, value=客户), KeyValueItem(key=负责人, value=柯南颖019), KeyValueItem(key=任务开始时间, value=2020-10-14 16:12), KeyValueItem(key=流程主题, value=客户(2020-10-14 16:12)), KeyValueItem(key=客户名称, value=yuq1)])] result=BaseResult(code=2
     * 00, message=发送成功) duration=1937ms
     */
    //测试消息通知
    @Test
    public void toCardMessage(){
        SendTextCardMessageArg sendTextMessageArg=new SendTextCardMessageArg();
        sendTextMessageArg.setEa("ddqybhzyl");
        sendTextMessageArg.setEi(82379);
        sendTextMessageArg.setMessageContent("审批意见：同意。");
        sendTextMessageArg.setReceiverIds(Lists.newArrayList(2161));
        sendTextMessageArg.setReceiverChannelType(1);
//        List<KeyValueItem> form = Lists.newArrayList();
//        KeyValueItem item = new KeyValueItem("发起流程数据", "yuq1");
//        KeyValueItem item2 = new KeyValueItem("所属业务对象", "客户");
//        KeyValueItem item3 = new KeyValueItem("负责人", "柯南颖019");
//        KeyValueItem item4 = new KeyValueItem("任务开始时间", "2020-10-14 16:12");
//        KeyValueItem item5 = new KeyValueItem("流程主题", "客户(2020-10-14 16:12)");
//        KeyValueItem item6 = new KeyValueItem("客户名称", "yuq1");
//        form.add(item);
//        form.add(item2);
//        form.add(item3);
//        form.add(item4);
//        form.add(item5);
//        form.add(item6);
        String message="{\n" +
                "    \"appId\":\"CRM\",  \"subAppId\":\"Bpm\"\n" +
                "}";
        sendTextMessageArg.setUrl("todo?apiname=AccountObj&id=6602bf88491f7e00076d7652&ea=ddqybhzyl&ea=ddqybhzyl");
        sendTextMessageArg.setTitle("您发起的审批：大客户报备流程 已同意，请知晓");
        Map<String,String> arg=Maps.newHashMap();
        arg.put("objectApiName","AccountObj");
        arg.put("objectId","6602bf88491f7e00076d7652");
        sendTextMessageArg.setExtraDataMap(arg);
        sendTextMessageArg.setForm(null);
        sendTextMessageArg.setReceiverChannelData(message);
        sendTextMessageArg.setSenderId(1000);

//        SendTextCardMessageArg arg=new SendTextCardMessageArg();
//        arg.setEi(82379);
//        arg.setEa("83384");
//        arg.setSenderId(0);
//        arg.setReceiverIds(Lists.newArrayList(2161));
//        arg.setTitle("报表导出");
//        arg.setMessageContent("客户统计-1_副本 ********.xlsx");
//        arg.setUrl("https://crm.ceshi112.com/dps/preview/bypath?path=N_202408_14_c3c056ae456f45c3a8980b7fcb9bc848.xlsx&showHeader=1");
//        arg.setReceiverChannelData("{\"appId\":\"WJTZ\"}");
//
//        arg.setGenerateUrlType(5);
//        arg.setReceiverChannelType(5);
//        arg.setExtraDataMap(new HashMap<>());
//        arg.getExtraDataMap().put("fileName","客户统计-1_副本 ********.xlsx");
//        arg.getExtraDataMap().put("fileSize","11624");
//        arg.getExtraDataMap().put("filePath","N_202408_14_c3c056ae456f45c3a8980b7fcb9bc848.xlsx");
//        arg.getExtraDataMap().put("fileExt","");
//        arg.getExtraDataMap().put("filePre","1");
//        arg.setGroupKeys(new HashSet<>());
//        arg.getGroupKeys().add("webhookMessageDistribute");
//        arg.getGroupKeys().add("dingCloudGroup");

//        SendTextCardMessageArg arg=new SendTextCardMessageArg();
//        arg.setEi(82379);
//        arg.setEa("83384");
//        arg.setSenderId(0);
//        arg.setReceiverIds(Lists.newArrayList(2161));
//        arg.setTitle("报表订阅");
//        arg.setMessageContent("客户统计-1_副本 ********.xlsx");
//        arg.setUrl("ava-bi-message?id=BI_66b34e3939dda80001f9cb43&diagram=1&from=subscription&dataType=2");
//        arg.setReceiverChannelData("{\"appId\":\"BI_SUBSCRIPTION\"}");
//
//        arg.setGenerateUrlType(4);
//        arg.setReceiverChannelType(1);
//        arg.setExtraDataMap(new HashMap<>());
//        arg.getExtraDataMap().put("diagram","1");
//        arg.getExtraDataMap().put("dataType","2");
//        arg.getExtraDataMap().put("viewUrl","https://www.ceshi112.com/h5app/bi-report?fromapp=1#/report/BI_66b34e3939dda80001f9cb43?from=subscription&schType=1&dataType=2");
//        arg.getExtraDataMap().put("from","subscription");
//        arg.getExtraDataMap().put("subscriptionId","BI_66b34e3939dda80001f9cb43");
//        arg.setGroupKeys(new HashSet<>());
//        arg.getGroupKeys().add("dingCloudGroup");

        externalMessageService.sendTextCardMessage(sendTextMessageArg);
    }


//    @Test
//    public void toMessage(){
//        SendTextMessageArg sendTextMessageArg=new SendTextMessageArg();
//        sendTextMessageArg.setEa("76246");
//        sendTextMessageArg.setEi(76246);
//        sendTextMessageArg.setMessageContent("就想测试一下CRM通知");
//        sendTextMessageArg.setReceiverIds(Lists.newArrayList(1011));
//        sendTextMessageArg.setReceiverChannelType(1);
//        sendTextMessageArg.setSenderId(1000);
//        externalMessageService.sendTextMessage(sendTextMessageArg);
//    }




    @Test
    //测试应用范围
    public void testScope() {
        String clientIp = "https://www.fxiaoke.com/dingtalk/";
        String appkey = "dingd0vww6pgdoy16ozi";
        String appsecret = "28lTExiCrOCpDOfNGg-MaugItuNJWph99VZRFQsiYr6EMoZCJAfqLC-PHcfmDo53";
        String token = "NyB0KLZ0oEwvh8aRqrz1r67llBFg4MH2fy7rElNYcG2A9z0qurpLAvpmZ4NB8pSJ3wj04El0iM1Uw5+E8uym1IasRaLGPI8ijnoP76Ry2Uyj4tVMyiKblbHTf3T4StbimeArN8ZXhqZZjrsXjAspHOMLVAtKBpeIw2nxPwtdup8=";
        ScopeVo scopeVo = DingRequestUtil.queryScoreDeptEmployee(1,clientIp, appkey, appsecret, token,"dba09ae9692032f11a84b01dd210969479");
    }


    @Test
    //测试拉取员工
    public void testAllEmployee() {
        objectMappingService.syncNewEmployee(78762, 1024,"76235");
    }


    @Test
    //测试Guava的集合差集、交集。
    public void testGuava() {
        HashMap<String, String> objectObjectHashMap = Maps.newHashMap();
        objectObjectHashMap.put("key1", "value1");
        objectObjectHashMap.put("key2", "value2");
        objectObjectHashMap.put("key3", "value3");
        objectObjectHashMap.put("key4", "value4");
        HashMap<String, String> objectMap2 = Maps.newHashMap();
        objectMap2.put("key1", "value1");
        objectMap2.put("key2", "value2");
        objectMap2.put("key6", "value6");

        MapDifference<String, String> difference = Maps.difference(objectObjectHashMap, objectMap2);
        Map<String, String> stringStringMap = difference.entriesInCommon();
        Map<String, String> stringStringMap1 = difference.entriesOnlyOnLeft();
        Map<String, String> stringStringMap2 = difference.entriesOnlyOnRight();
        Map<String, MapDifference.ValueDifference<String>> differ = difference.entriesDiffering();


    }

    @Test
    public void testCollections() {
        HashMap<String, Dept> objectObjectHashMap = Maps.newHashMap();
        Dept dept = new Dept();
        Dept dept1 = new Dept();
        Dept dept2 = new Dept();
        dept.setParentid(1L);
        dept.setName("we");
        dept.setId(100L);
        dept1.setParentid(1L);
        dept1.setName("we");
        dept1.setId(100L);
        dept2.setParentid(1L);
        dept2.setName("we");
        dept2.setId(100L);
        objectObjectHashMap.put("01", dept);
        objectObjectHashMap.put("02", dept1);
        objectObjectHashMap.put("03", dept2);
        List<Long> collect = objectObjectHashMap.values().stream().map(Dept::getId).collect(Collectors.toList());
        System.out.println(collect);
    }

    @Test
    public void testDeptFindByEi() {
        Integer ei = 76246;
        List<DeptVo> deptList = dingDeptMananger.getDeptByEI(ei);
        //380890885
        Map<Long, DeptVo> deptMaps = deptList.stream().collect(Collectors.toMap(DeptVo::getDingDeptId, Function.identity(), (key1, key2) -> key2));
        DeptVo deptVo = deptMaps.get(380890885L);
        System.out.println(deptVo);
    }

    public List<Integer> treeDeptIds(HashMap<Long, DingDeptEntity> deptMaps, Long dingDeptId, List<Integer> ids) {
        if (deptMaps.get(dingDeptId) != null) {
            Integer crmDeptId = deptMaps.get(dingDeptId).getCrmDeptId();
            Long dingDeptID = deptMaps.get(dingDeptId).getDingParentId();
            ids.add(crmDeptId);
            treeDeptIds(deptMaps, dingDeptID, ids);

        }
        return ids;
    }

    //测试递归
    @Test
    public void testTree() {
        DingDeptEntity dingDeptEntity = new DingDeptEntity();
        dingDeptEntity.setDingDeptId(1L);
        dingDeptEntity.setCrmDeptId(1042);
        dingDeptEntity.setDingParentId(0L);
        dingDeptEntity.setName("啊南科技");

        DingDeptEntity dingDeptEntity1 = new DingDeptEntity();
        dingDeptEntity1.setDingDeptId(375320380L);
        dingDeptEntity1.setCrmDeptId(1043);
        dingDeptEntity1.setDingParentId(1L);
        dingDeptEntity1.setName("总裁办");


        DingDeptEntity dingDeptEntity2 = new DingDeptEntity();
        dingDeptEntity2.setDingDeptId(375403450L);
        dingDeptEntity2.setDingParentId(1L);
        dingDeptEntity2.setCrmDeptId(1044);
        dingDeptEntity2.setName("技术部");


        DingDeptEntity dingDeptEntity3 = new DingDeptEntity();
        dingDeptEntity3.setDingDeptId(376323342L);
        dingDeptEntity3.setCrmDeptId(1045);
        dingDeptEntity3.setDingParentId(375320380L);
        dingDeptEntity3.setName("会计处");

        DingDeptEntity dingDeptEntity4 = new DingDeptEntity();
        dingDeptEntity4.setDingDeptId(376461373L);
        dingDeptEntity4.setDingParentId(375320380L);
        dingDeptEntity4.setCrmDeptId(1046);
        dingDeptEntity4.setName("营销处");

        DingDeptEntity dingDeptEntity5 = new DingDeptEntity();
        dingDeptEntity5.setDingDeptId(379681594L);
        dingDeptEntity5.setDingParentId(376461373L);
        dingDeptEntity5.setCrmDeptId(1047);
        dingDeptEntity5.setName("营销一处");

        HashMap<Long, DingDeptEntity> deptMaps = Maps.newHashMap();
        deptMaps.put(dingDeptEntity.getDingDeptId(), dingDeptEntity);
        deptMaps.put(dingDeptEntity1.getDingDeptId(), dingDeptEntity1);
        deptMaps.put(dingDeptEntity2.getDingDeptId(), dingDeptEntity2);
        deptMaps.put(dingDeptEntity3.getDingDeptId(), dingDeptEntity3);
        deptMaps.put(dingDeptEntity4.getDingDeptId(), dingDeptEntity4);
        deptMaps.put(dingDeptEntity5.getDingDeptId(), dingDeptEntity5);
        List<Long> ids = new ArrayList<>();
        List<DingDeptEntity> deptEntity = new ArrayList<>();
        deptEntity.add(dingDeptEntity);
        deptEntity.add(dingDeptEntity1);
        deptEntity.add(dingDeptEntity2);
        deptEntity.add(dingDeptEntity3);
        deptEntity.add(dingDeptEntity4);
        deptEntity.add(dingDeptEntity5);
//        List<Long> integers = treeDeptIds(deptMaps, 376323342L, ids);
        List<Long> depts = Lists.newArrayList(1L);

        List<Long> integers = recallChildDepts(deptEntity, depts, ids);
        System.out.println(integers.size());
    }

    //批量创建员工
    @Test
    public void testCreateEmployee() {

        /**
         *  CreateCrmEmployeeVo(id=18131, name=罗美慧, gender=null, mobile=13620247560, dingEmployeeId=3160590432364848, updateBy=1003, ei=74315, dingDeptId=129318512)
         12319	74315					1	卢明	cx018	18959199609	wxaMy7nQXXNPnheiPsET7cgiEiE	36274114		福州代理公司	2020-11-06 00:46:16	2020-11-06 00:46:16	1003	1003	0
         12320	74315					1	黄嵘	04376907071283860	13509335478	QJmUe7wzszxQCIP8cgB5YgiEiE	36274114		福州代理公司	2020-11-06 00:46:16	2020-11-06 00:46:16	1003	1003	0
         12321	74315					1	郑晶	6018	13506986513	uGILZ6oxpTO63fICiSWP35QiEiE	36274114		福州代理公司	2020-11-06 00:46:16	2020-11-06 00:46:16	1003	1003	0
         12322	74315					1	姚国明	xd018	13615009769	SVypn0HiPTIpQCIP8cgB5YgiEiE	36274114		福州代理公司	2020-11-06 00:46:16	2020-11-06 00:46:16	1003	1003	0
         12323	74315					1	沈东良	8018	13950398855	INnLGYiSgFS5PnheiPsET7cgiEiE	36274114		福州代理公司	2020-11-06 00:46:16	2020-11-06 00:46:16	1003	1003	0
         */
        List<CreateCrmEmployeeVo> vo = Lists.newArrayList();

        CreateCrmEmployeeVo vo2 = new CreateCrmEmployeeVo();
        vo2.setDingEmployeeId("011160650760868648527");
        vo2.setDingDeptId(37540342222250L);
        vo2.setName("测试回调");
        vo2.setMobile("13524148231");
        vo.add(vo2);
        objectMappingService.batchCreateFxEmployee(vo, 76246, 1000,"76235");
    }

    @Test
    public void testCallDept() {
        List<Long> listParent = DingRequestUtil.
                getListParent
                        ("http://**************:8089/", 387388247L, "22db7a8dd48d36b998cb531190253d14");
        System.out.println(listParent);
    }

    private List<Long> recallChildDepts(List<DingDeptEntity> dingDeptEntities, List<Long> dingParentIds, List<Long> ids) {
        if (!CollectionUtils.isEmpty(dingParentIds)) {
            for (int i = 0; i < dingParentIds.size(); i++) {
                Long id = dingParentIds.get(i);
                List<Long> deptS = dingDeptEntities.stream().filter(item -> id.equals(item.getDingParentId())).map(DingDeptEntity::getDingDeptId).collect(Collectors.toList());
                List<Long> dingPars = Lists.newArrayList();
                dingPars.addAll(deptS);
                ids.addAll(deptS);
                recallChildDepts(dingDeptEntities, deptS, ids);
            }
        }
        return ids;
    }

    /**
     * ModifyDepartment.Arg arg = new ModifyDepartment.Arg();
     * ModifiedDepartment modifiedDepartment = new ModifiedDepartment();
     * modifiedDepartment.setDepartmentId(vo.getCrmDeptId());
     * modifiedDepartment.setParentDepartmentId(vo.getCrmParentId());
     * modifiedDepartment.setName(vo.getName());
     * modifiedDepartment.setDepartmentPrincipalId(afterResult.getData().getEmployeeId());
     * arg.setModifiedDepartment(modifiedDepartment);
     * arg.setOperatorId(OPERATOR);
     * arg.setEnterpriseId(ei);
     * arg.setUpdateTime(new Date().getTime());
     * //更新crm的部门
     * departmentProviderService.modifyDepartment(arg);
     */
    //测试修改crm的部门信息
    @Test
    public void updateDept() {
//        ModifyDepartment.Arg arg = new ModifyDepartment.Arg();
//        ModifiedDepartment modifiedDepartment = new ModifiedDepartment();
//        modifiedDepartment.setDepartmentId(vo.getCrmDeptId());
//        modifiedDepartment.setParentDepartmentId(vo.getCrmParentId());
//        modifiedDepartment.setName(vo.getName());
//        modifiedDepartment.setDepartmentPrincipalId(afterResult.getData().getEmployeeId());
//        arg.setModifiedDepartment(modifiedDepartment);
//        arg.setOperatorId(OPERATOR);
//        arg.setEnterpriseId(ei);
//        arg.setUpdateTime(new Date().getTime());
//        //更新crm的部门
//        departmentProviderService.modifyDepartment(arg);
//
//        departmentProviderService.modifyDepartment()
    }

    @Test
    public void queryDepts() {
//        MetaParam metaParam = new MetaParam();
//        metaParam.setEnterpriseAccount("75133");
//        metaParam.setCurrentEmployeeId(1026);
//        ListResult<Circle> allCircles = circleService.getAllCircles(metaParam);
//        ArrayList<Integer> objects = Lists.newArrayList();
//        objects.add(1014);
//        objects.add(1015);
//        objects.add(1016);
//        objects.add(1017);
//        objects.add(1018);
//        objects.add(1019);
//        objects.add(1020);
//        objects.add(1021);
//        objects.add(1022);
//        DeleteDepartmentArg arg = new DeleteDepartmentArg();
//        arg.setDepartmentId(1014);
//        arg.setOperatorId(1026);
//        arg.setEnterpriseId(75133);
//        arg.setUpdateTime(new Date().getTime());
//        BatchDeleteDepartmentArg batchDeleteDepartmentArg = new BatchDeleteDepartmentArg();
//        batchDeleteDepartmentArg.setDepartmentIds(objects);
//        batchDeleteDepartmentArg.setEnterpriseId(75133);
//        batchDeleteDepartmentArg.setCurrentEmployeeId(1026);
//        GetDepartmentDtoArg getDepartmentDtoArg = new GetDepartmentDtoArg();
//        getDepartmentDtoArg.setDepartmentId(1014);
//        getDepartmentDtoArg.setEnterpriseId(75133);
        GetAllDepartmentNameArg getAllDepartmentNameArg = new GetAllDepartmentNameArg();
        getAllDepartmentNameArg.setEnterpriseId(76246);
        getAllDepartmentNameArg.setRunStatus(RunStatus.ALL);
//        CreateDepartment.Result department = departmentProviderService.createDepartment(createDeparment);


        GetAllDepartmentNameResult allDepartmentName = departmentProviderService.getAllDepartmentName(getAllDepartmentNameArg);
        BatchModifyDepartmentResumeArg arg = new BatchModifyDepartmentResumeArg();
        List<Integer> departmentIds = Lists.newArrayList();
        departmentIds.add(1007);
        departmentIds.add(1008);
        departmentIds.add(1009);
        departmentIds.add(1010);
        departmentIds.add(1005);
        departmentIds.add(1006);
        departmentIds.add(1011);
        departmentIds.add(1012);
        departmentIds.add(1013);
        arg.setDepartmentIds(departmentIds);
        arg.setOperatorId(1000);
        arg.setEnterpriseId(76246);
        arg.setUpdateTime(new Date().getTime());
        BatchModifyDepartmentResumeResult batchModifyDepartmentResumeResult = departmentProviderService.batchModifyDepartmentResume(arg);
//        GetDepartmentDtoResult departmentDto = departmentProviderService.getDepartmentDto(getDepartmentDtoArg);

    }

    //测试汇报对象
    @Test
    public void testCrmOwner() {
//        Integer ei = 76246;
//        List<DeptVo> baseDept = dingDeptMananger.getDeptByEI(ei);
//        objectMappingService.createDeptOwner(baseDept, ei);
    }

    //测试回调部门事件
    @Test
    public void testCallBack() {
        String corpId = "ding5fe5aa1ba078facc24f2f5cc6abecb85";
        List<Long> dept = Lists.newArrayList();
        dept.add(472427463L);
        Result<Integer> integerResult = objectMappingService.modifyFxDept(corpId, dept,"76235");
    }

    @Test
    public void suffixName() {
        String deptName="对对部门";
        String dataName="对对部门__10006";
        String[] names = dataName.split("__");
        if (deptName.equals(names[0])) {
          log.info("false");
        }
        log.info("true");
    }

    //测试配额
    @Test
    public void testQuota() {
        GetEnterpriseEditionArg arg = new GetEnterpriseEditionArg();
        arg.setEnterpriseId(76246);

        GetEnterpriseEditionResult enterpriseEdition = enterpriseEditionService.getEnterpriseEdition(arg);
    }


    //测试部门负责人
    @Test
    public void testCrmDept() {

//        ModifyDepartment.Result result = departmentProviderService.modifyDepartment(arg);
        String entity=null;
        Gson gson=new Gson();
        OapiSnsGetuserinfoBycodeResponse.UserInfo dingUser = gson.fromJson(entity, OapiSnsGetuserinfoBycodeResponse.UserInfo.class);
        dingUser = Optional.ofNullable(dingUser).orElse(new OapiSnsGetuserinfoBycodeResponse.UserInfo());

        String name=dingUser.getNick();

    }


    //测试保存的状态
    @Test
    public void testSaveStatus(){
        ConnectionVo vo=new ConnectionVo();
        vo.setEi(76246);
        vo.setEa("76246");
        vo.setAlertStatus(1);
        enterpriseService.saveOperation(vo);
    }

    @Test
    public void testVi(){
       List<Integer> values=Lists.newArrayList(1,2,3);
        Integer[] integers = values.toArray(new Integer[values.size()]);

    }


    @Test
    public void testThread(){
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        Map<String,String> map=Maps.newHashMap();
        for(int i=0;i<100;i++){
            executorService.execute(new ThreadTask(i,map));

        }
    }

    private class ThreadTask implements Runnable{
        Integer a;
        Map<String,String> map;
        private ThreadTask(Integer a,Map<String,String> map){
            this.a=a;
            this.map=map;
        }
        @Override
        public void run() {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            map.put(a.toString(),a.toString());
        }
    }


    @Test
    public void testMappingEmployee(){
        objectMappingService.independenceAgainMapping(79478,"76235");
    }



//    private static List<Integer> conVertDepts(String value){
//        //376323342|375403450 转化成list
//        String[] split = value.split("|");
//        List<Integer> depts=new ArrayList<>(split.length);
//        for (int i = 0; i < split.length; i++) {
//            depts.add(Integer.valueOf(split[i]));
//        }
//        return depts;
//    }

    @Test
    public void testSpilt(){
        String value="123-456";
        String[] split = value.split("-");
        List<Integer> depts=new ArrayList<>(split.length);
        System.out.println(depts);
    }


//    @Test
//    public void createDept(){
//        MetaParam metaParam = new MetaParam("76246", 1000);
//        BeanResult<CreatedCircle> circle = circleService.createCircle(metaParam, "测试部门102", 999999);
//        System.out.println(circle);
//    }

    @Test
    public void testSplitter(){
   String name="1|259335|539307|540053|585503|58683|589993|589996|589997|590001|590083|597566|601362|615315|625892|629927|635139|635311|640383|642332|646917|649256|649888|652454|654530|655827|663013|663052|672394|676519|678853|679362|682674|683729|687961|690928|691742|706332";
   List<String> tenantIds = Splitter.on("|").splitToList(name.replaceAll("white:|;| ", ""));
        System.out.println(tenantIds);
    }

    @Test
    public void testModify(){
        ModifyDepartment.Arg arg=new ModifyDepartment.Arg();
        ModifiedDepartment modifiedDepartment=new ModifiedDepartment();
        modifiedDepartment.setParentDepartmentId(1525);
        modifiedDepartment.setDepartmentId(1536);
        modifiedDepartment.setName("教育部门01");
        modifiedDepartment.setIsStop(false);
        modifiedDepartment.setDepartmentPrincipalId(1062);
        arg.setModifiedDepartment(modifiedDepartment);
        arg.setEnterpriseId(76246);
        arg.setOperatorId(-10000);
        ModifyDepartment.Result result = departmentProviderService.modifyDepartment(arg);
        System.out.println(result);
    }

    @Test
    public void testCreate(){
        CreateDepartment.Arg arg=new CreateDepartment.Arg();
        arg.setOperatorId(-10000);
        arg.setName("教育部门02");
        arg.setDepartmentPrincipalId(1062);
        arg.setEnterpriseId(76246);
        arg.setParentId(1536);
        arg.setDescription("12");

        CreateDepartment.Result department = departmentProviderService.createDepartment(arg);

    }

    @Test
    public void testModifyDept(){
        Dept dept=new Dept(548463271L,1L,"测试部门","0134333134461075194");
        Result<Integer> result = cloudDeptService.modifyDept(dept, "dingfaf004f6391dd096ffe93478753d9884", 83015, "","19123003");

    }


}
