package template;

import base.BaseAbstractTest;
import com.facishare.open.ding.web.template.inner.login.DingtalkLoginTemplate;
import com.facishare.open.ding.web.template.model.FsUserModel;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.outer.oa.connector.common.api.info.ticket.GenFsTicketModel;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class DingtalkLoginTemplateTest extends BaseAbstractTest {
    @Autowired
    private DingtalkLoginTemplate dingtalkLoginTemplate;

    @Test
    public void genFsTicketAndGetFsUserByTicket() {
        GenFsTicketModel ticketModel = new GenFsTicketModel();
        ticketModel.setAppId("70480");
        ticketModel.setOutEa("dingfeca3fa3352c7d4ca39a90f97fcb1e09");
        ticketModel.setOutUserId("175718530421871316");
        ticketModel.setFsEa("ddqybhzyl");

        MethodContext context = MethodContext.newInstance(ticketModel);
        dingtalkLoginTemplate.genFsTicket(context);

        com.facishare.open.outer.oa.connector.common.api.result.Result<String> result = context.getResultData();
        String ticket = result.getData();
        log.info("DingtalkLoginTemplateTest.result,ticket={}",ticket);

        context = MethodContext.newInstance(ticket);
        dingtalkLoginTemplate.getFsUserInfoByTicket(context);
        Result<FsUserModel> result2 = context.getResultData();
        log.info("DingtalkLoginTemplateTest.result,FsUserModel={}",result2.getData());
    }
}
