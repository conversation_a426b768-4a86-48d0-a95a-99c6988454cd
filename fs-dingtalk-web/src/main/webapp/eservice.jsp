<%@ page import="org.owasp.esapi.ESAPI" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="wpk-bid" content="dta_2_79344">
    <script>    !(function (c, i, e, b) {
        var h = i.createElement("script");
        var f = i.getElementsByTagName("script")[0];
        h.type = "text/javascript";
        h.crossorigin = true;
        h.onload = function () {
            c[b] || (c[b] = new c.wpkReporter({bid: "dta_2_79344"}));
            c[b].installAll()
        };
        f.parentNode.insertBefore(h, f);
        h.src = e
    })(window, document, "https://g.alicdn.com/woodpeckerx/jssdk??wpkReporter.js", "__wpk");</script>
    <title>纷享服务通</title>
    <style>
        .weui-loading {
            width: 20px;
            height: 20px;
            display: inline-block;
            vertical-align: middle;
            animation: weuiLoading 1s steps(12, end) infinite;
            background: transparent url("data:image/svg+xml;charset=utf8, %3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='120' viewBox='0 0 100 100'%3E%3Cpath fill='none' d='M0 0h100v100H0z'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23E9E9E9' rx='5' ry='5' transform='translate(0 -30)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23989697' rx='5' ry='5' transform='rotate(30 105.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%239B999A' rx='5' ry='5' transform='rotate(60 75.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23A3A1A2' rx='5' ry='5' transform='rotate(90 65 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23ABA9AA' rx='5' ry='5' transform='rotate(120 58.66 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23B2B2B2' rx='5' ry='5' transform='rotate(150 54.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23BAB8B9' rx='5' ry='5' transform='rotate(180 50 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23C2C0C1' rx='5' ry='5' transform='rotate(-150 45.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23CBCBCB' rx='5' ry='5' transform='rotate(-120 41.34 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23D2D2D2' rx='5' ry='5' transform='rotate(-90 35 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23DADADA' rx='5' ry='5' transform='rotate(-60 24.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23E2E2E2' rx='5' ry='5' transform='rotate(-30 -5.98 65)'/%3E%3C/svg%3E") no-repeat;
            background-size: 100%;
        }

        .weui-icon_toast.weui-loading {
            margin: 30px 0 0;
            width: 38px;
            height: 38px;
            vertical-align: baseline;
        }

        @-webkit-keyframes weuiLoading {
            0% {
                transform: rotate3d(0, 0, 1, 0deg);
            }

            100% {
                transform: rotate3d(0, 0, 1, 360deg);
            }
        }

        @keyframes weuiLoading {
            0% {
                transform: rotate3d(0, 0, 1, 0deg);
            }

            100% {
                transform: rotate3d(0, 0, 1, 360deg);
            }
        }

        .weui-toast {
            position: fixed;
            z-index: 5000;
            width: 7.6em;
            min-height: 7.6em;
            top: 180px;
            left: 50%;
            margin-left: -3.8em;
            background: rgba(17, 17, 17, 0.7);
            text-align: center;
            border-radius: 5px;
            color: #FFFFFF;
        }

        .weui-toast__content {
            margin: 0 0 15px;
        }

        .common-class {
            background-size: 100%;
            background-repeat: no-repeat;
            height: 100vh;
            padding-top: 313px;
            text-align: center;
            font-size: 14px;
            color: #16263C;
            line-height: 22px;
            display: none;
            background-size: 375px;
            background-position: top center;
        }

        .error {
            background-image: url('https://a9.fspage.com/FSR/uipaas/dd-error.svg');
        }

        .initialize-error {
            background-image: url('https://a9.fspage.com/FSR/uipaas/dd-error2.svg');
        }

        .user-excess-error {
            background-image: url('https://a9.fspage.com/FSR/uipaas/ddUserExcess.svg');
        }

        .expiration-reminder-error {
            background-image: url('https://a9.fspage.com/FSR/uipaas/ddExpirationReminder.svg');
        }

        .title {
            color: #000;
            font-size: 20px;
        }

        .text {
            color: #67717F;
            font-size: 13px;
        }

        .new-text {
            font-size: 16px;
            color: #545861;
            line-height: 30px;
        }

        .new-text-orange {
            color: #FF8000;
        }

        .expiration-reminder-button {
            width: 351px;
            height: 48px;
            background: #FF8000;
            border-radius: 4px;
            margin: 24px auto;
            font-weight: 400;
            color: #FFFFFF;
            line-height: 48px;
            text-align: center;
        }
    </style>
    <div class="index-wrapper">
        <div class="weui-loading_toast">
            <div class="weui-mask_transparent"></div>
            <div class="weui-toast">
                <i class="weui-loading weui-icon_toast"></i>
                <p class="weui-toast__content">加载中</p>
            </div>
        </div>
        <div class="error common-class">
        </div>
        <div class="user-excess-error common-class">
        </div>
        <div class="expiration-reminder-error common-class">
        </div>
        <!-- Failed initialize -->
        <div class="initialize-error common-class">
            <div class="toast">
                <p class="title">初始化中</p>
                <p class="text">正在进行初始化，可能需要几分钟，请耐心等待</p>
            </div>
        </div>
    </div>
    <script src="https://a9.fspage.com/open/cdn/jquery/2.2.4/jquery.min.js"></script>
    <script src="https://g.alicdn.com/dingding/dingtalk-jsapi/2.10.3/dingtalk.open.js"></script>
    <script src="https://a9.fspage.com/open/cdn/axios.min.js"></script>
    <script>
        var corpId =
        <%= "\"" + request.getParameter("corpId").replace("&","&amp").replace("<","&lt").replace(">","&gt").replace("\"","&quot").replace("\'","&#x27").replace("/","&#x2F") + "\"" %>
        var suiteId = <%= "\"" + request.getParameter("suiteId").replace("&","&amp").replace("<","&lt").replace(">","&gt").replace("\"","&quot").replace("\'","&#x27").replace("/","&#x2F") + "\"" %>
                <%--var suiteId=<%= "\""+request.getParameter("corpId")!=null?request.getParameter("corpId").replace("&","&amp").replace("<","&lt").replace(">","&gt").replace("\"","&quot").replace("\'","&#x27").replace("/","&#x2F"):null%>--%>
                UA = typeof window !== 'undefined' && window.navigator.userAgent.toLowerCase(),
            isAndroid = (UA && UA.indexOf('android') > 0),
            isIOS = (UA && /iphone|iPhone|ipad|ipod|ios/.test(UA)),
            isPC = !isAndroid && !isIOS;
        window.onload = function () {
            dd.ready(function () {
                var url;
                var code;
                var timer;
                dd.runtime.permission.requestAuthCode({
                    corpId: corpId,
                    onSuccess: function (info) {
                        code = info.code;
                        url = "/dingtalk/cloud/login?corpId=" + corpId + "&code=" + code + "&suiteId=" + suiteId;
                        axios.get(url).then(function (response) {
                            $('.weui-loading_toast').remove()
                            if (response.data.errorCode === 0) {
                                window.open(response.data.data.url, '_self');
                            } else if (response.data.errorCode === 9009) {
                                $('.expiration-reminder-error').show().html("<p class='new-text'>尊敬的<span class='new-text-orange'>" + (response.data.data.userName || '--') + "</span>，您好！贵企业的CRM服务已于<span class='new-text-orange'>" + (response.data.data.stopTime || '--') + "</span>到期，欢迎您续费后继续使用。<p>" + (!isPC ? "<div class='expiration-reminder-button'>立即续费</div>" : ""))
                                $(".expiration-reminder-button").click(function () {
                                    window.open("https://h5.dingtalk.com/appcenter/detail.html?showmenu=false&dd_share=false&articleCode=DT_GOODS_881635417957372&goodsCode=DT_GOODS_881635417957372&corpId=" + corpId + "&source=isvRedirect&funnelsource=isvRedirect", '_self');
                                });
                            } else if (response.data.errorCode === 9007) {
                                $('.user-excess-error').show().html("<p class='new-text'>尊敬的<span class='new-text-orange'>" + (response.data.data.userName || '--') + "</span>，您好！您的账号未能授权，无法继续访问CRM，请联系管理员。可能的原因：贵企业选择的产品规格与使用范围数量不等。</p>")
                            } else if (response.data.errorCode === 9012) {
                                $('.user-excess-error').show().html("<p class='new-text'>由于当前应用需要专业版以上版本，您目前使用的CRM标准版暂不支持使用，可联系客服为您处理。</p>")
                            } else if (response.data.errorCode == 9002) {
                                $('.initialize-error').show()
                                var userId = response.data && response.data.data.userId,
                                    corpId = response.data && response.data.data.corpId;
                                suiteId = response.data && response.data.data.suiteId;
                                //定时器
                                timer = setInterval(function () {
                                    axios.post('/dingtalk/cloud/longPolling', {
                                        userId: userId,
                                        corpId: corpId,
                                        suiteId: suiteId,
                                    }).then(function (response) {
                                        if (response.data.errorCode === 0) {
                                            timer && clearInterval(timer)
                                            window.open(response.data.data.url, '_self');
                                        }
                                    }).catch(function (err) {
                                        timer && clearInterval(timer)
                                        alert(err)
                                    })
                                }, 1500)
                            } else if (response.data.errorCode === 9020) {
                                $('.user-excess-error').show().html("<p class='new-text'>尊敬的<span class='new-text-orange'>" + (response.data.data.userName || '--') + "</span>，您好！您的账号未能授权，无法继续访问CRM，请联系管理员。可能的原因：该企业纷享crm员工账号已达上限，请联系纷享客服处理。</p>")
                            }
                        }).catch(function (err) {
                            alert(err)
                        })
                    },
                    onFail: function (err) {
                        $('.weui-loading_toast').remove()
                        $('.error').show().text('requestAuthCode error: ' + JSON.stringify(err))
                    }
                });
            });
        }
    </script>
</head>
<body>

</body>
</html>