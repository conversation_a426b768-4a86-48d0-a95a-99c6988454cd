package com.facishare.open.ding.cloud.service.impl;

import com.facishare.open.ding.api.model.AppParams;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.service.cloud.CloudDeptService;
import com.facishare.open.ding.api.service.cloud.CloudToolsService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.dao.OpenMediumSyncBizDataDao;
import com.facishare.open.ding.cloud.dao.OpenSyncBizDataDao;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEmployeeBindMapper;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service(value = "cloudToolsServiceImpl")
@Slf4j
// IgnoreI18nFile
public class CloudToolsServiceImpl implements CloudToolsService {
    //只能Autowired装载，不能用Resource
    @Resource
    private CloudDeptService cloudDeptService;
    @Resource
    private ObjectMappingService objectMappingService;
    //只能Autowired装载，不能用Resource
    @Resource
    private DingCorpMappingService dingCorpMappingService;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Resource
    private DingManager dingManager;
    @Resource
    private OpenSyncBizDataDao openSyncBizDataDao;
    @Resource
    private OpenMediumSyncBizDataDao openMediumSyncBizDataDao;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaEmployeeBindMapper outerOaEmployeeBindMapper;

    @Override
    public Result<Void> updateDeptBind(List<OuterOaDepartmentBindEntity> deptVos) {
        return cloudDeptService.updateDeptBind(deptVos);
    }

    @Override
    public Result<Void> deleteEmployeeBind(Integer ei, List<Integer> fsEmpIds) {
        Thread thread = new Thread(() -> deleteEmployeeBind2(ei, fsEmpIds));
        thread.start();
        return new Result<>();
    }

    private void deleteEmployeeBind2(Integer ei, List<Integer> fsEmpIds) {
        objectMappingService.deleteEmpByFsId(ei, fsEmpIds);
    }

    @Override
    public Result<String> queryFsEnterpriseOpen(String outEa) {
        if(StringUtils.isEmpty(outEa)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        //corpId获取到的企业信息
        Result<List<DingCorpMappingVo>> corpResult = dingCorpMappingService.queryCorpMappingByCorpId(outEa, null);
        if(CollectionUtils.isEmpty(corpResult.getData())) {
            return Result.newSuccess("该企业没有自动创建的纷享企业，如果不是手动删除了绑定关系或者输错了账号，请联系集成平台相关研发人员排查");
        }

        DateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = outputFormat.format(corpResult.getData().get(0).getCreateTime());
        if(corpResult.getData().get(0).getIsInit() == 0) {
            return Result.newSuccess(String.format("该企业初始化通讯录中，企业ea=%s，创建时间=%s", corpResult.getData().get(0).getEa(), format));
        }
        return Result.newSuccess(String.format("该企业已创建成功，企业ea=%s，创建时间=%s", corpResult.getData().get(0).getEa(), format));
    }

    @Override
    public Result<String> queryFsEmployeeOpen(String outEa, String outUserId) {
        if(StringUtils.isAnyEmpty(outEa, outUserId)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        //corpId获取到的企业信息
        Result<List<DingCorpMappingVo>> corpResult = dingCorpMappingService.queryCorpMappingByCorpId(outEa, null);
        if(CollectionUtils.isEmpty(corpResult.getData())) {
            return Result.newSuccess("该企业没有自动创建的纷享企业，如果不是手动删除了绑定关系或者输错了账号，请联系集成平台相关研发人员排查");
        }
        Integer ei = corpResult.getData().get(0).getEi();
        String ea = corpResult.getData().get(0).getEa();
        //获取人员绑定关系
        final List<OuterOaEmployeeBindEntity> byEaAndOutEmpId = outerOaEmployeeBindManager.getByEaAndOutEmpId(ChannelEnum.dingding, ea, outEa, outUserId);
        if(CollectionUtils.isEmpty(byEaAndOutEmpId)) {
            return Result.newSuccess("该人员创建失败，请联系集成平台相关研发人员排查");
        }
        final OuterOaEmployeeBindEntity bindEntity = byEaAndOutEmpId.get(0);
        DateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = outputFormat.format(bindEntity.getCreateTime());
        return Result.newSuccess(String.format("该人员已创建成功，纷享企业ea=%s，纷享人员id=%s，创建时间=%s", ea, bindEntity.getFsEmpId(), format));
    }

    @Override
    public Result<String> queryEnterpriseBindType(String fsEa) {
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        Result<List<DingCorpMappingVo>> corpResult = dingCorpMappingService.queryByEa(fsEa);
        if(CollectionUtils.isEmpty(corpResult.getData())) {
            return Result.newSuccess("反绑定的纷享企业");
        }

        return Result.newSuccess("应用开通的纷享企业");
    }

    @Override
    public Result<String> queryFsEmployeeStatus(String outEa, String outUserId) {
        if(StringUtils.isAnyEmpty(outEa, outUserId)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        //corpId获取到的企业信息
        Result<List<DingCorpMappingVo>> corpResult = dingCorpMappingService.queryCorpMappingByCorpId(outEa, null);
        if(CollectionUtils.isEmpty(corpResult.getData())) {
            return Result.newSuccess("该企业没有自动创建的纷享企业，如果不是手动删除了绑定关系或者输错了账号，请联系集成平台相关研发人员排查");
        }
        Integer ei = corpResult.getData().get(0).getEi();
        String ea = corpResult.getData().get(0).getEa();
        //获取人员绑定关系
        final List<OuterOaEmployeeBindEntity> byEaAndOutEmpId = outerOaEmployeeBindManager.getByEaAndOutEmpId(ChannelEnum.dingding, ea, outEa, outUserId);
        if(ObjectUtils.isEmpty(byEaAndOutEmpId)) {
            return Result.newSuccess("该人员创建失败，请联系集成平台相关研发人员排查");
        }
        final OuterOaEmployeeBindEntity bindEntity = byEaAndOutEmpId.get(0);
        //查询crm
        com.facishare.open.order.contacts.proxy.api.result.Result<List<EmployeeDto>> employeeDtoResult = fsEmployeeServiceProxy.batchGetEmployeeDto(ei, Lists.newArrayList(Integer.valueOf(bindEntity.getFsEmpId())));
        if(employeeDtoResult.getData().get(0).getStatus() != EmployeeEntityStatus.NORMAL) {
            return Result.newSuccess(String.format("该人员已被停用，纷享企业ea=%s，纷享人员id=%s", ea, bindEntity.getFsEmpId()));
        }
        return Result.newSuccess(String.format("该人员状态正常，纷享企业ea=%s，纷享人员id=%s", ea, bindEntity.getFsEmpId()));
    }

    @Override
    public Result<Void> updateFsDeptOwner(Integer ei, Integer pageSize, String suiteId) {
        Result<List<DingCorpMappingVo>> dingCorpMappingsResult = dingCorpMappingService.queryByEi(ei);
        if(!dingCorpMappingsResult.isSuccess() || CollectionUtils.isEmpty(dingCorpMappingsResult.getData())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        String corpId = dingCorpMappingsResult.getData().get(0).getDingCorpId();
        if(StringUtils.isEmpty(suiteId)) {
            suiteId = ConfigCenter.CRM_SUITE_ID;
        }
        if(pageSize == null || pageSize < 0) {
            pageSize = 100;
        }
        AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(suiteId);
        final String appId = appParams.getAppId();

        //查询企业部门
        int startPageNum = 1;
        boolean isHasNextPage = Boolean.TRUE;
        do {
            Integer page = ((startPageNum ++) - 1) * pageSize;
            Result<List<OuterOaDepartmentBindEntity>> listResult = cloudDeptService.queryBindingDepartments(ei, page, pageSize, appId);
            List<OuterOaDepartmentBindEntity> dataList = listResult.getData();
            if(CollectionUtils.isEmpty(dataList) || dataList.size() < ConfigCenter.DING_EVENT_ACCOUNT) {
                isHasNextPage = Boolean.FALSE;
            }

            if(CollectionUtils.isNotEmpty(dataList)) {
                for(OuterOaDepartmentBindEntity vo : dataList) {
                    //查询钉钉接口
                    long dingDepId = Long.parseLong(vo.getOutDepId());
                    log.info("CloudToolsServiceImpl.updateFsDeptOwner,dingDepId={}", dingDepId);
                    Dept dept = dingManager.queryDeptDetail(corpId, dingDepId, suiteId);
                    if(ObjectUtils.isEmpty(dept) || StringUtils.isEmpty(dept.getDeptOwner())) {
                        continue;
                    }
                    Result<DingMappingEmployeeResult> mappingResult = objectMappingService.queryEmpByDingUserId(ei, dept.getDeptOwner(), appId);
                    if(!mappingResult.isSuccess() || ObjectUtils.isEmpty(mappingResult.getData())) {
                        continue;
                    }
                    //更新
                    Result<Integer> result = cloudDeptService.updateDeptOwner(ei, dingDepId, appId, dept.getDeptOwner(), mappingResult.getData().getEmployeeId());
                    log.info("CloudToolsServiceImpl.updateFsDeptOwner,result={}", result);
                }
            }
        } while (isHasNextPage);

        return Result.newSuccess();
    }

    @Override
    public Result<Void> updateEventStatus(String event, Integer status, Long timestamp) {
        if(event.equalsIgnoreCase("H")) {
            if(timestamp != null) {
                boolean isTrue = Boolean.TRUE;
                do {
                    List<Long> ids = openSyncBizDataDao.selectList2Ids(1000, new Date(timestamp));
                    if(CollectionUtils.isEmpty(ids) || ids.size() < 1000) {
                        isTrue = Boolean.FALSE;
                    }
                    if(CollectionUtils.isNotEmpty(ids)) {
                        int count = openSyncBizDataDao.batchUpdateHighBizDataStatusByIds(status, ids);
                        log.info("high level,batchUpdateHighBizDataStatusByIds timestamp:{},count:{}", timestamp, count);
                    }
                    try {
                        Thread.sleep(200L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                } while (isTrue);
            }
        } else if(event.equalsIgnoreCase("M")) {
            if(timestamp != null) {
                boolean isTrue = Boolean.TRUE;
                do {
                    List<Long> ids = openMediumSyncBizDataDao.selectList2Ids(1000, new Date(timestamp));
                    if(CollectionUtils.isEmpty(ids) || ids.size() < 1000) {
                        isTrue = Boolean.FALSE;
                    }
                    if(CollectionUtils.isNotEmpty(ids)) {
                        int count = openMediumSyncBizDataDao.batchUpdateMediumBizDataStatusByIds(status, ids);
                        log.info("high level,batchUpdateMediumBizDataStatusByIds timestamp:{},count:{}", timestamp, count);
                    }
                    try {
                        Thread.sleep(200L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                } while (isTrue);
            }
        }

        log.info("high level,batchUpdateBizDataStatusByIds end");
        return Result.newSuccess();
    }

    @Override
    public Result<Integer> updateEventById(String event, Long id, Integer status, Long timestamp) {
        int count = 0;
        if(event.equalsIgnoreCase("H")) {
            if(status != null) {
                count = openSyncBizDataDao.batchUpdateHighBizDataStatusByIds(status, Lists.newArrayList(id));
            } else if(timestamp != null) {
                count = openSyncBizDataDao.updateHighBizDataTimeById(new Date(timestamp), id);
            }
        } else if(event.equalsIgnoreCase("M")) {
            if(status != null) {
                count = openMediumSyncBizDataDao.batchUpdateMediumBizDataStatusByIds(status, Lists.newArrayList(id));
            } else if(timestamp != null) {
                count = openMediumSyncBizDataDao.updateMediumBizDataTimeById(new Date(timestamp), id);
            }
        }
        return Result.newSuccess(count);
    }
}
