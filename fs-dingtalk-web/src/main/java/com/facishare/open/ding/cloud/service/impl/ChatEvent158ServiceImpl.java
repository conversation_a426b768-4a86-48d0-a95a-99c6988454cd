package com.facishare.open.ding.cloud.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.ding.api.model.ChatEventModel;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.Model.DingChatRoomLogDTO;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.entity.HighBizDataDo;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.provider.redis.RedisDataSource;
import com.facishare.open.ding.cloud.service.api.DingEventService;
import com.facishare.open.ding.cloud.utils.RedisLockUtils;
import com.facishare.open.ding.common.result.Result;
import com.fxiaoke.cloud.DataPersistor;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.DingChatRoomLog;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/1/14 21:01
 * @Version 1.0
 */
@Service("chatEvent158ServiceImpl")
@Slf4j
// IgnoreI18nFile
public class ChatEvent158ServiceImpl implements DingEventService {

    @Autowired
    private DingManager dingManager;
    @Autowired
    private DingCorpMappingService dingCorpMappingService;
    @Autowired
    private RedisDataSource redisDataSource;
    public static String ORDER_FORMAT = "CHAT_ROOM_%S";
    /**
     * 通话记录埋点serviceName
     */
    private static final String REPORTING_NAME_CALL_RECORDS = "fs-dingtalk-cloud";

    @Override
    public void executeEvent(HighBizDataDo eventData) {
        ChatEventModel chatEventModel = JSONObject.parseObject(eventData.getBizData(), new TypeReference<ChatEventModel>() {
        });
        //有时候钉钉会推送多次开通事件，需要加锁
        if(RedisLockUtils.tryGetDistributedLock(redisDataSource.getRedisClient(), String.format(ORDER_FORMAT, chatEventModel.getOpenConversationId()), chatEventModel.getOpenConversationId(), 3600)){
            if("im_cool_app_install".equals(chatEventModel.getSyncAction())){//安装
                Result<String> messageResult = dingManager.robotSendCardMessage(chatEventModel.getOpenConversationCorpId(), ConfigCenter.CRM_SUITE_ID, chatEventModel);
                log.info("chat eventmodel:{}.messageReuslt:{}",chatEventModel,messageResult);
            }
            Result<List<DingCorpMappingVo>> corpResult = dingCorpMappingService.queryCorpMappingByCorpId(chatEventModel.getOpenConversationCorpId(), null);
            String ea = corpResult.getData().get(0).getEa();
            Integer ei = corpResult.getData().get(0).getEi();
            String name = corpResult.getData().get(0).getEnterpriseName();
            Map<String, Object> dataMap = this.getDataMap(ea,ei, name,chatEventModel);
            log.info("record data starting:{}",chatEventModel.getOpenConversationCorpId());
            DataPersistor.asyncLog(REPORTING_NAME_CALL_RECORDS, dataMap);
            DingChatRoomLogDTO dingChatRoomLogDTO=DingChatRoomLogDTO.builder().app(ConfigHelper.getProcessInfo().getName()).
                    serverIp(ConfigHelper.getProcessInfo().getIp()).traceId(TraceContext.get().getTraceId()).ei(String.valueOf(ei)).ea(ea).
                    corpId(chatEventModel.getOpenConversationCorpId()).roomId(chatEventModel.getOpenConversationId()).name(name).action(chatEventModel.getSyncAction()).createTime(System.currentTimeMillis())
                    .build();
            BizLogClient.send("ding_im_cool_app_log", Pojo2Protobuf.toMessage(dingChatRoomLogDTO, DingChatRoomLog.class).toByteArray());
            log.info("upload log data:{}",dingChatRoomLogDTO);
        }
    }

    /**
     * 包装需要埋点的字段
     *
     * @return
     */
    private Map<String, Object> getDataMap(String enterpriseAccount,Integer ei,String name,
                                           ChatEventModel chatEventModel) {
        HashMap<String, Object> dataMap = Maps.newHashMapWithExpectedSize(32);
        dataMap.put("tenantId", ei);
        dataMap.put("enterpriseAccount",enterpriseAccount);
        dataMap.put("corpId",chatEventModel.getOpenConversationCorpId());
        dataMap.put("roomId",chatEventModel.getOpenConversationId());
        dataMap.put("name",name);
        dataMap.put("action",chatEventModel.getSyncAction());
        dataMap.put("trace", "sendRobotCardMessage");
        dataMap.put("traceName", "酷应用开通卸载的事件");
        return dataMap;
    }

}
