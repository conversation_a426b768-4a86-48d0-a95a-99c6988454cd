//package com.facishare.open.ding.cloud.service.impl.connector;
//
//import com.alibaba.fastjson.JSONObject;
//import com.facishare.open.ding.api.constants.CrmObjectApiName;
//import com.facishare.open.ding.api.enums.SyncDirectionEnum;
//import com.facishare.open.ding.api.model.SyncDataMappingModel;
//import com.facishare.open.ding.api.service.DingCorpMappingService;
//import com.facishare.open.ding.api.service.SyncDataMappingsService;
//import com.facishare.open.ding.api.vo.DingCorpMappingVo;
//import com.facishare.open.ding.cloud.constants.TriggerAction;
//import com.facishare.open.ding.cloud.manager.DataProcessManager;
//import com.facishare.open.ding.cloud.paas.constants.EventTypeEnum;
//import com.facishare.open.ding.cloud.paas.handler.EventDataHandler;
//import com.facishare.open.ding.cloud.paas.models.EventData;
//import com.facishare.open.ding.common.result.Result;
//import com.google.common.collect.Lists;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.collections4.MapUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.Optional;
//
//import static com.facishare.open.ding.cloud.constants.Constant.APP_ID;
//
///**
// * 连接器CRM对象变更处理器.非钉钉官方连接器同步方式处理器
// */
//@Component("dingUnOfficialEventDataHandler")
//@Slf4j
//public class DingUnOfficialEventDataHandler implements EventDataHandler {
//    @Autowired
//    private DingCorpMappingService dingCorpMappingService;
//
//    @Autowired
//    private SyncDataMappingsService syncDataMappingsService;
//
//    @Autowired
//    private DataProcessManager dataProcessManager;
//
//
//    private List<String> supportObjectList = Lists.newArrayList(
//            CrmObjectApiName.LEADS_OBJ_API_NAME
//    );
//
//    @Override
//    public void handle(List<EventData> eventDataList) {
//        for(EventData eventData : eventDataList) {
//            //过滤对象
//            if(!supportObjectList.contains(eventData.getSourceData().getApiName())) continue;
//            log.info("ConnectorEventDataHandler.handle,eventData={}", JSONObject.toJSONString(eventData));
//
//            //获取当前企业开通连接器信息
//            int ei = Integer.valueOf(eventData.getSourceData().getTenantId());
//            Result<List<DingCorpMappingVo>> dingCorpMappingVos = dingCorpMappingService.queryByEi(ei);
//            if(CollectionUtils.isEmpty(dingCorpMappingVos.getData())) continue;
//            DingCorpMappingVo dingCorpMappingVo = dingCorpMappingVos.getData().get(0);
//
//            Result<List<SyncDataMappingModel>> modelResult =
//              syncDataMappingsService.loadSyncDataMapping(ei,dingCorpMappingVo.getDingCorpId(),eventData.getSourceData().getApiName(),null,
//                SyncDirectionEnum.FROM_FXIAOKE, MapUtils.getString(eventData.getSourceData(),"_id"));
//            List<SyncDataMappingModel> data = modelResult.getData();
//
//            String triggerAction;
//            if(modelResult.getData() == null) {
//                triggerAction = TriggerAction.ADD;
//            } else {
//                Optional<SyncDataMappingModel> first = data.stream().filter(t -> t.getCreated() == 2).findFirst();
//                if (first.isPresent()){
//                    triggerAction = TriggerAction.UPDATE;
//                }else {
//                    triggerAction = TriggerAction.ADD;
//                }
//            }
//            if(eventData.getSourceEventType() == EventTypeEnum.INVALID.getType()
//                    || eventData.getSourceEventType() == EventTypeEnum.DELETE_DIRECT.getType()) {
//                triggerAction = TriggerAction.DELETE;
//            }
//            dataProcessManager.process(triggerAction,dingCorpMappingVo.getDingCorpId(), eventData);
//            log.info("dingUnOfficialEventDataHandler.handle,success");
//        }
//    }
//
//}
