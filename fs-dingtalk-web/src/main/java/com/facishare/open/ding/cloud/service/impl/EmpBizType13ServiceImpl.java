package com.facishare.open.ding.cloud.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.marketing.outapi.service.DingAddressBookCallBackService;
import com.facishare.open.ding.api.enums.DingEmpEventEnum;
import com.facishare.open.ding.api.model.AppParams;
import com.facishare.open.ding.api.model.BizEmpModel;
import com.facishare.open.ding.api.result.AppAuthResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.result.UserAppResult;
import com.facishare.open.ding.api.service.*;
import com.facishare.open.ding.api.service.cloud.CloudEmpService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.api.vo.UserMappingVo;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.entity.HighBizDataDo;
import com.facishare.open.ding.cloud.manager.CrmManager;
import com.facishare.open.ding.cloud.manager.DataPushManager;
import com.facishare.open.ding.cloud.service.api.DingEventService;
import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.oa.base.dbproxy.manager.ObjectDataManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.RemoveEmployeeEventType;
import com.facishare.restful.common.StopWatch;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import static com.facishare.open.ding.api.vo.UserMappingVo.*;

/**
 * <AUTHOR>
 * @Date 2021/5/13 0:33 员工变化事件,以应用维度
 * @Version 1.0
 */
@Slf4j
@Service("empBizType13ServiceImpl")
public class EmpBizType13ServiceImpl implements DingEventService {

    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private DingCorpMappingService dingCorpMappingService;
    @Autowired
    private CloudEmpService cloudEmpService;
    @Autowired
    private AppAuthService appAuthService;
    @Autowired
    private UserAppMappingService userAppMappingService;
    @Autowired
    private CrmManager crmManager;
    @Autowired
    private DingAddressBookCallBackService dingAddressBookCallBackService;
    @Autowired
    private DataPushManager dataPushManager;
    @Autowired
    private CrmSyncObjService crmSyncObjService;
    @Autowired
    private ObjectDataManager objectDataManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    private Integer EMP_AUTH_NOT_RANGE = 50002;
    private Integer NOT_QUERY_EMP = 60121;
    // private static ExecutorService executorService = new ThreadPoolExecutor(10, 20, 60L, TimeUnit.SECONDS, new ArrayBlockingQueue(100));

    @Override
    public void executeEvent(HighBizDataDo eventData) {
        StopWatch stopWatch = StopWatch.create("bizType13 service");
        Long suiteId = Long.valueOf(Splitter.on("_").splitToList(eventData.getSubscribeId()).get(0));
        log.info("suiteId:{}", suiteId);
        AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(suiteId);
        final String appId = appParams.getAppId();
        final Long appKey = Long.valueOf(appId);
        Result<List<AppAuthResult>> appResult = appAuthService.conditionAppAuth(eventData.getCorpId(), null, suiteId);
        log.info("appResult:{}", appResult);
        BizEmpModel bizEmpModel = JSONObject.parseObject(eventData.getBizData(), new TypeReference<BizEmpModel>() {
        });
        log.info("eventEmp model:{}", bizEmpModel);
        Result<List<DingCorpMappingVo>> corpResult = dingCorpMappingService.queryCorpMappingByCorpId(eventData.getCorpId(), appId);
        if (CollectionUtils.isEmpty(corpResult.getData()) || CollectionUtils.isEmpty(appResult.getData())) {
            log.warn("bizType update emp fail,dingCorpId:{}", eventData.getCorpId());
            return;
        }
        Integer ei = corpResult.getData().get(0).getEi();
        String ea = corpResult.getData().get(0).getEa();
        Result<DingMappingEmployeeResult> mappingResult = objectMappingService.queryEmpByDingUserId(ei, eventData.getBizId(), appId);
        if (bizEmpModel.getSyncAction().equals(DingEmpEventEnum.USER_LEAVE_ORG.getAction())) {
            //删除员工
            cloudEmpService.batchRemoveEmp(Lists.newArrayList(eventData.getBizId()), ei, eventData.getCorpId(), appKey);
        } else {
            final OuterOaEnterpriseBindEntity bindEntity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, ea, appId);
            //更新或者新增员工
            EmployeeDingVo employeeDingVo = JSONObject.parseObject(eventData.getBizData(), new TypeReference<EmployeeDingVo>() {
            });
            if (ObjectUtils.isEmpty(mappingResult.getData())) {
                if (bizEmpModel.getErrcode().equals(EMP_AUTH_NOT_RANGE)||bizEmpModel.getErrcode().equals(NOT_QUERY_EMP)) {
                    log.info("create emp not auth:{}", eventData.getBizId());
                    return;
                }
                log.info("add emp user:{},appId:{},corpId:{}", employeeDingVo, appId, eventData.getCorpId());
                //新增员工
                employeeDingVo.setMainDept(employeeDingVo.getDepartment().get(0));
                cloudEmpService.cloudCreateEmp(employeeDingVo, ei, appKey, eventData.getCorpId());
                //查询员工fxId
                String roleCode = Optional.ofNullable(ConfigCenter.ROLE_MAP.get(appId)).orElseGet(() -> "00000000000000000000000000000015");
                Result<DingMappingEmployeeResult> dataResult = objectMappingService.queryEmpByDingUserId(ei, employeeDingVo.getUserid(), String.valueOf(appId));
                if (ObjectUtils.isNotEmpty(dataResult.getData())) {
                    if(!ConfigCenter.NOT_SYNC_ROLE.contains(String.valueOf(ei))){//过滤掉不需要同步角色的企业
                        crmManager.createRoleCode(ea, ei, Lists.newArrayList(dataResult.getData().getEmployeeId()), roleCode);
                        if(employeeDingVo.getIsAdmin()){
                            crmManager.addManagerRole(ei,dataResult.getData().getEmployeeId());
                        }
                    }

                    //TODO 如果该企业是开通了钉钉crm连接器，需要同步新增员工
//                    Integer count = crmSyncObjService.queryIsSyncData(eventData.getCorpId());
//                    if(count>0){
//                        //同步到连接器
//                        List<ErpEmployeeArg> erpEmployeeArgs=Lists.newArrayList();
//                        ErpEmployeeArg erpEmployeeArg=new ErpEmployeeArg();
//                        erpEmployeeArg.setErpEmployeeId(employeeDingVo.getUserid());
//                        erpEmployeeArg.setErpEmployeeName(employeeDingVo.getName());
//                        erpEmployeeArg.setFsEmployeeId(dataResult.getData().getEmployeeId());
//                        erpEmployeeArgs.add(erpEmployeeArg);
//                        Result<String> result = dataPushManager.batchSaveEmployeeData(String.valueOf(ei), erpEmployeeArgs);
//                        log.info("save emp result:{},erpEmployeeArgs:{}",result,erpEmployeeArgs);
//                    }
                }
            } else {
                Integer fxEmpId = mappingResult.getData().getEmployeeId();
                if (bizEmpModel.getErrcode().equals(EMP_AUTH_NOT_RANGE)) {
                    //授权范围变更，需要移除角色，删除user_app的数据
                    crmManager.removeRoleCode(ea, ei, Lists.newArrayList(fxEmpId), appKey);

                    objectDataManager.removeEmpData(bindEntity, mappingResult.getData().getDingEmployeeId(), RemoveEmployeeEventType.REMOVE_RANGE);
                } else {
                    //更新员工
                    employeeDingVo.setMainDept(employeeDingVo.getDepartment().get(0));
                    cloudEmpService.cloudUpdateEmp(employeeDingVo, ei, appKey, eventData.getCorpId());
                    //判断user_app表是否有该员工， 没有则授权对应的角色
                    // 钉钉isv和crm 是一对一的关系,所以入参可以没有fsEA
                    Result<List<UserAppResult>> userAppMappingResult = userAppMappingService.queryByDingEmpId(eventData.getCorpId(), appKey, bizEmpModel.getUserId());
                    if (CollectionUtils.isEmpty(userAppMappingResult.getData())) {
                        UserMappingVo userMappingVo = builder().appId(appKey).crmEmpId(fxEmpId).ei(ei).dingCorpId(eventData.getCorpId()).dingEmpId(employeeDingVo.getUserid()).build();
                        Result<Integer> insertCount = userAppMappingService.insertDingEmp(userMappingVo);
                        String roleCode = Optional.ofNullable(ConfigCenter.ROLE_MAP.get(appId)).orElseGet(() -> "00000000000000000000000000000015");
                        if(!ConfigCenter.NOT_SYNC_ROLE.contains(String.valueOf(ei))){
                            Result<String> createResult = crmManager.createRoleCode(ea, ei, Lists.newArrayList(fxEmpId), roleCode);
                            //修改管理员权限
                            if(employeeDingVo.getIsAdmin()){
                                crmManager.addManagerRole(ei,fxEmpId);
                            }
                        }
                        log.info("biztype add user role");

                    }

                }
            }
        }
        //TODO 事件分发给营销通
        if(eventData.getSubscribeId().contains(ConfigCenter.MARKETING_SUITE_ID)){
            log.info("sync emp event to marketing action:{},ea:{},ding_emp",bizEmpModel.getSyncAction(),ea,eventData.getBizId());
            dingAddressBookCallBackService.addressBookEventCallBack(bizEmpModel.getSyncAction(),ea,Lists.newArrayList(eventData.getBizId()));
        }

    }


}
