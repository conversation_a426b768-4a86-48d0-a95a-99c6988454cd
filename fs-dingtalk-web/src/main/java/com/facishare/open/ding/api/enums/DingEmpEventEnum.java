package com.facishare.open.ding.api.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2021/5/11 19:41
 * @Version 1.0
 */
@Getter
public enum DingEmpEventEnum {

    //bizType 13
    USER_ADD_ORG("user_add_org"),
    USER_MODIFY_ORG("user_modify_org"),
    USER_DEPT_CHANGE("user_dept_change"),
    USER_ROLE_CHANGE("user_role_change"),
    USER_ACTIVE_ORG("user_active_org"),
    USER_LEAVE_ORG("user_leave_org"),

    ;

    private  String action;

    DingEmpEventEnum( String action){

        this.action=action;
    }

    public static DingEmpEventEnum getBizTypeAuth(String action){
        for (DingEmpEventEnum e : DingEmpEventEnum.values()){
            if(e.action.equals(action)){
                return e;
            }
        }
        return null;
    }

}
