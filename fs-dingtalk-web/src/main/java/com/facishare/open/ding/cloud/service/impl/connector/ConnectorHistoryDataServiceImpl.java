//package com.facishare.open.ding.cloud.service.impl.connector;
//
//import com.alibaba.fastjson.JSONObject;
//import com.facishare.open.ding.api.model.connector.BaseConnectorObjectModel;
//import com.facishare.open.ding.api.model.connector.CustomerModel;
//import com.facishare.open.ding.api.model.connector.SalesOrderModel;
//import com.facishare.open.ding.api.result.CrmResponseResult;
//import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
//import com.facishare.open.ding.api.result.ErpObjListData;
//import com.facishare.open.ding.api.service.DingMappingEmployeeService;
//import com.facishare.open.ding.api.service.cloud.connector.ConnectorHistoryDataService;
//import com.facishare.open.ding.api.service.cloud.connector.ConnectorObjectDataCacheService;
//import com.facishare.open.ding.api.vo.DingCorpMappingVo;
//import com.facishare.open.ding.api.vo.ObjectDataCacheVo;
//import com.facishare.open.ding.cloud.arg.BatchSendDataArg;
//import com.facishare.open.ding.cloud.constants.ConfigCenter;
//import com.facishare.open.ding.cloud.constants.TriggerAction;
//import com.facishare.open.ding.cloud.constants.TriggerEventId;
//import com.facishare.open.ding.cloud.manager.DingConnectorManager;
//import com.facishare.open.ding.cloud.manager.DingManager;
//import com.facishare.open.ding.cloud.manager.NCrmCloudManager;
//import com.facishare.open.ding.cloud.result.BatchSendDataResult;
//import com.facishare.open.ding.common.result.Result;
//import com.google.common.collect.Lists;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.MapUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.*;
//
//import static com.facishare.open.ding.api.constants.CrmObjectApiName.*;
//
///**
// * 连接器历史数据处理类
// * <AUTHOR>
// * @Date 2021/7/28
// */
//@Service("connectorHistoryDataServiceImpl")
//@Slf4j
//public class ConnectorHistoryDataServiceImpl implements ConnectorHistoryDataService {
//    @Autowired
//    private NCrmCloudManager nCrmCloudManager;
//    @Autowired
//    private ConnectorObjectDataCacheService connectorObjectDataCacheService;
//    @Autowired
//    private DingConnectorManager dingConnectorManager;
//    @Autowired
//    private DingManager dingManager;
//    @Autowired
//    private DingMappingEmployeeService dingMappingEmployeeService;
//
//    private static final int pageSize=100;
//
//
//    @Override
//    public Result<Boolean> processHistoryCustomerData(DingCorpMappingVo dingCorpMappingVo) {
//        int ei = dingCorpMappingVo.getEi();
//
//        int offset = 0;
//        while (true) {
//            CrmResponseResult<ErpObjListData> crmResponseResult = nCrmCloudManager.pageObjectListRest(ei,
//                    ACCOUNT_API_NAME,
//                    0L,
//                    System.currentTimeMillis(),
//                    offset,
//                    pageSize,
//                    null);
//            if (crmResponseResult.isSuccess()) {
//                List<Map<String,Object>> dataList = crmResponseResult.getData().getDataList();
//                int dataSize = dataList.size();
//                offset += dataSize;
//                if (dataSize == 0) {
//                    break;
//                }
//                dataList.forEach((map)->{
//                    CustomerModel customerModel = getCustomerModel(map,ei);
//
//                    ObjectDataCacheVo entity = connectorObjectDataCacheService.queryOneData(dingCorpMappingVo.getDingCorpId(),
//                            dingCorpMappingVo.getAppCode(),
//                            ACCOUNT_API_NAME,
//                            customerModel.getData().getCode());
//                    if(entity==null) {
//                        entity = new ObjectDataCacheVo();
//                        entity.setId(UUID.randomUUID().toString());
//                        entity.setCorpId(dingCorpMappingVo.getDingCorpId());
//                        entity.setEi(ei);
//                        entity.setAppId(dingCorpMappingVo.getAppCode());
//                        entity.setObjectApiName(ACCOUNT_API_NAME);
//                        entity.setDataCode(customerModel.getData().getCode());
//                        entity.setObjectDataId(MapUtils.getString(map,"_id"));
//                        entity.setJsonData(JSONObject.toJSONString(customerModel));
//                        entity.setSyncDirection(0);
//                        entity.setSynced(false);
//                        entity.setSyncFailed(false);
//                        entity.setFailedReason(null);
//                        entity.setDeleted(false);
//
//                        int count = connectorObjectDataCacheService.insert(entity);
//                        log.info("connectorHistoryDataServiceImpl.processHistoryCustomerData,insert db,count={}",count);
//                    } else {
//                        entity.setObjectDataId(MapUtils.getString(map,"_id"));
//                        entity.setJsonData(JSONObject.toJSONString(customerModel));
//                        int count = connectorObjectDataCacheService.update(entity);
//                        log.info("connectorHistoryDataServiceImpl.processHistoryCustomerData,update db,count={}",count);
//                    }
//                });
//            }
//        }
//
//        batchSendData(dingCorpMappingVo.getDingCorpId(),
//                dingCorpMappingVo.getAppCode(),
//                ACCOUNT_API_NAME,
//                TriggerEventId.FS_CUSTOMER_EVENT,
//                TriggerAction.UPDATE);
//
//        return Result.newSuccess(true);
//    }
//
//    @Override
//    public CustomerModel getCustomerModel(Map<String,Object> map,int ei) {
//        log.info("connectorHistoryDataServiceImpl.getCustomerModel,ei={},map={}",ei,map);
//        DingMappingEmployeeResult dingMappingEmployeeResult = dingMappingEmployeeService.findMappingByEmployeeId(ei,1000);
//
//        CustomerModel customerModel = new CustomerModel();
//        customerModel.setOperateUserId(dingMappingEmployeeResult.getDingEmployeeId());
//        customerModel.getData().setName(MapUtils.getString(map,"name"));
//        customerModel.getData().setCode(MapUtils.getString(map,"account_no"));
//
//        List<String> owners = JSONObject.parseArray(JSONObject.toJSONString(map.get("owner")), String.class);
//
//        dingMappingEmployeeResult = dingMappingEmployeeService.findMappingByEmployeeId(ei,Integer.valueOf(owners.get(0)));
//        if(dingMappingEmployeeResult==null) {
//            dingMappingEmployeeResult = dingMappingEmployeeService.findMappingByEmployeeId(ei,1000);
//        }
//
//        customerModel.getData().setEmpCode(dingMappingEmployeeResult.getDingEmployeeId());
//        //customerModel.getData().setEmpCode("manager8812");
//
//        //customerModel.getData().setCustomerCategoryCode(MapUtils.getString(map,"group_id__c"));
//        customerModel.getData().setRemark(MapUtils.getString(map,"remark"));
//
//        updateContactsInfo(ei,customerModel,MapUtils.getString(map,"_id"));
//
//        log.info("connectorHistoryDataServiceImpl.getCustomerModel,customerModel={}",customerModel);
//
//        return customerModel;
//    }
//
//
//    private void batchSendData(String corpId,Long appId,String objectApiName,String triggerId,String action) {
//        List<ObjectDataCacheVo> entityList = connectorObjectDataCacheService.queryDataList(corpId,
//                appId,
//                objectApiName);
//
//        //corpId = "ding5bc08f4c8f18f5f64ac5d6980864d335";
//        String accessToken = dingManager.getAccessToken(corpId, ConfigCenter.CRM_SUITE_ID).getData();
//
//        if(entityList.size()>0) {
//            List<List<ObjectDataCacheVo>> pagedEntityList = new LinkedList<>();
//            int pageSize = 100;
//            for(int i=0;i<entityList.size();i+=pageSize) {
//                List<ObjectDataCacheVo> list = new ArrayList<>();
//                int toIndex = Math.min(pageSize,entityList.size()-i);
//                list.addAll(entityList.subList(i,i + toIndex));
//                pagedEntityList.add(list);
//            }
//            pagedEntityList.forEach(dataList -> {
//                BatchSendDataArg arg = new BatchSendDataArg();
//                arg.setAppId(appId);
//
//                dataList.forEach(entity -> {
//                    BatchSendDataArg.TriggerData triggerData = new BatchSendDataArg.TriggerData();
//                    triggerData.setTriggerId(triggerId);
//                    triggerData.setAction(action);
//                    triggerData.setDataGmtCreate(System.currentTimeMillis());
//                    triggerData.setDataGmtModified(System.currentTimeMillis());
//                    triggerData.setJsonData(entity.getJsonData());
//
//                    BaseConnectorObjectModel baseConnectorObjectModel = JSONObject.parseObject(entity.getJsonData(),
//                            BaseConnectorObjectModel.class);
//                    if(!baseConnectorObjectModel.isError()) {
//                        arg.getTriggerDataList().add(triggerData);
//                    }
//                });
//
//                BatchSendDataResult batchSendDataResult = dingConnectorManager.batchSendData(accessToken,arg);
//                log.info("connectorHistoryDataServiceImpl.batchSendData,batchSendDataResult={}",batchSendDataResult);
//
//                if(batchSendDataResult.getList()!=null && batchSendDataResult.getList().size()>0) {
//                    batchSendDataResult.getList().forEach(dataResult -> {
//                        log.info("connectorHistoryDataServiceImpl.batchSendData,dataResult={}",dataResult);
//                        ObjectDataCacheVo entity = getObjectDataCacheEntity(dataList,dataResult.getBizPrimaryKey());
//                        if(entity!=null) {
//                            entity.setSynced(true);
//                            entity.setSyncFailed(!dataResult.isSuccess());
//                            if(!dataResult.isSuccess()) {
//                                entity.setFailedReason("errCode="+dataResult.getSubErrCode()+",errMsg="+dataResult.getSubErrMsg());
//                            } else {
//                                entity.setFailedReason(null);
//                            }
//                            int count = connectorObjectDataCacheService.update(entity);
//                            log.info("connectorHistoryDataServiceImpl.batchSendData,count={}",count);
//                        }
//                    });
//                }
//            });
//        }
//    }
//
//    private ObjectDataCacheVo getObjectDataCacheEntity(List<ObjectDataCacheVo> list, String dataCode) {
//        for(ObjectDataCacheVo entity : list) {
//            if(StringUtils.equalsIgnoreCase(entity.getDataCode(),dataCode)) {
//                return entity;
//            }
//        }
//        return null;
//    }
//
//    public void updateContactsInfo(int ei,CustomerModel customerModel,String accountId) {
//        Map<String,Object> accountIdMap = new HashMap<>();
//        accountIdMap.put("field_name", "account_id");
//        accountIdMap.put("field_values", accountId);
//        accountIdMap.put("operator", "EQ");
//
//        List<Map<String,Object>> filter = Lists.newArrayList(accountIdMap);
//        CrmResponseResult<ErpObjListData> contactsResult = nCrmCloudManager.pageObjectListRest(ei,
//                CONTACTS_API_NAME,
//                0L,
//                System.currentTimeMillis(),
//                0,
//                pageSize,
//                filter);
//        log.info("connectorHistoryDataServiceImpl.updateContactsInfo,contactsResult={}",contactsResult);
//        if(contactsResult.isSuccess()) {
//            List<Map<String,Object>> contactsDataList = contactsResult.getData().getDataList();
//            contactsDataList.forEach((contactMap)->{
//                CustomerModel.ContactPerson contactPerson = new CustomerModel.ContactPerson();
//                contactPerson.setName(MapUtils.getString(contactMap,"name"));
//
//                CustomerModel.Address address = new CustomerModel.Address();
//                address.setDetailAddress(MapUtils.getString(contactMap,"add"));
//                contactPerson.setAddress(address);
//
//                CustomerModel.ContactType mobileContactType = new CustomerModel.ContactType();
//                mobileContactType.setType("mobile");
//                mobileContactType.setValue(getFirstMobile(contactMap));
//
//                CustomerModel.ContactType telContactType = new CustomerModel.ContactType();
//                telContactType.setType("tel");
//                telContactType.setValue(getFirstTel(contactMap));
//
//                CustomerModel.ContactType emailContactType = new CustomerModel.ContactType();
//                emailContactType.setType("email");
//                emailContactType.setValue(MapUtils.getString(contactMap,"email"));
//
//                contactPerson.getContactTypeList().addAll(Lists.newArrayList(mobileContactType,telContactType,emailContactType));
//
//                customerModel.getData().getContactPersonList().add(contactPerson);
//            });
//        }
//    }
//
//    private String getFirstMobile(Map<String,Object> contactMap) {
//        List<String> fieldList = Lists.newArrayList("mobile","mobile1","mobile2","mobile3","mobile4","mobile5");
//        return getFirstFieldValue(contactMap,fieldList);
//    }
//
//    private String getFirstTel(Map<String,Object> contactMap) {
//        List<String> fieldList = Lists.newArrayList("tel","tel1","tel2","tel3","tel4","tel5");
//        return getFirstFieldValue(contactMap,fieldList);
//    }
//
//    private String getFirstFieldValue(Map<String,Object> map,List<String> fieldList) {
//        String value = null;
//        for(String filedName : fieldList) {
//            value = MapUtils.getString(map,filedName);
//            if(StringUtils.isNotEmpty(value)) break;
//        }
//        return value;
//    }
//
//    @Override
//    public Result<Boolean> processHistorySalesOrderData(DingCorpMappingVo dingCorpMappingVo) {
//        int ei = dingCorpMappingVo.getEi();
//
//        int offset = 0;
//        while (true) {
//            CrmResponseResult<ErpObjListData> crmResponseResult = nCrmCloudManager.pageObjectListRest(ei,
//                    SALES_ORDER_API_NAME,
//                    0L,
//                    System.currentTimeMillis(),
//                    offset,
//                    pageSize,
//                    null);
//            if (crmResponseResult.isSuccess()) {
//                List<Map<String,Object>> dataList = crmResponseResult.getData().getDataList();
//                int dataSize = dataList.size();
//                offset += dataSize;
//                if (dataSize == 0) {
//                    break;
//                }
//                dataList.forEach((map)->{
//                    SalesOrderModel salesOrderModel = getSalesOrderModel(map,dingCorpMappingVo.getDingCorpId(),ei,dingCorpMappingVo.getAppCode());
//
//                    ObjectDataCacheVo entity = connectorObjectDataCacheService.queryOneData(dingCorpMappingVo.getDingCorpId(),
//                            dingCorpMappingVo.getAppCode(),
//                            SALES_ORDER_API_NAME,
//                            salesOrderModel.getData().getCode());
//                    if(entity==null) {
//                        entity = new ObjectDataCacheVo();
//                        entity.setId(UUID.randomUUID().toString());
//                        entity.setCorpId(dingCorpMappingVo.getDingCorpId());
//                        entity.setEi(ei);
//                        entity.setAppId(dingCorpMappingVo.getAppCode());
//                        entity.setObjectApiName(SALES_ORDER_API_NAME);
//                        entity.setDataCode(salesOrderModel.getData().getCode());
//                        entity.setObjectDataId(MapUtils.getString(map,"_id"));
//                        entity.setJsonData(JSONObject.toJSONString(salesOrderModel));
//                        entity.setSyncDirection(0);
//                        entity.setSynced(false);
//                        entity.setSyncFailed(false);
//                        entity.setFailedReason(null);
//                        entity.setDeleted(false);
//
//                        int count = connectorObjectDataCacheService.insert(entity);
//                        log.info("connectorHistoryDataServiceImpl.processHistorySalesOrderData,insert db,count={}",count);
//                    } else {
//                        entity.setObjectDataId(MapUtils.getString(map,"_id"));
//                        entity.setJsonData(JSONObject.toJSONString(salesOrderModel));
//                        int count = connectorObjectDataCacheService.update(entity);
//                        log.info("connectorHistoryDataServiceImpl.processHistorySalesOrderData,update db,count={}",count);
//                    }
//                });
//            }
//            break;
//        }
//
//        batchSendData(dingCorpMappingVo.getDingCorpId(),
//                dingCorpMappingVo.getAppCode(),
//                SALES_ORDER_API_NAME,
//                TriggerEventId.FS_SALES_ORDER_EVENT,
//                TriggerAction.UPDATE);
//
//        return Result.newSuccess(true);
//    }
//
//    @Override
//    public SalesOrderModel getSalesOrderModel(Map<String,Object> map,String corpId,int ei,long appId) {
//        DingMappingEmployeeResult dingMappingEmployeeResult = dingMappingEmployeeService.findMappingByEmployeeId(ei,1000);
//
//        SalesOrderModel salesOrderModel = new SalesOrderModel();
//        salesOrderModel.setOperateUserId(dingMappingEmployeeResult.getDingEmployeeId());
//
//        ObjectDataCacheVo objectDataCacheVo = connectorObjectDataCacheService.queryOneDataByObjectDataId(corpId,appId,
//                ACCOUNT_API_NAME,MapUtils.getString(map,"account_id"));
//        log.info("connectorHistoryDataServiceImpl.getSalesOrderModel,objectDataCacheVo={}",objectDataCacheVo);
//        if(objectDataCacheVo==null) {
//            log.info("connectorHistoryDataServiceImpl.getSalesOrderModel,canot find product data");
//            salesOrderModel.setError(true);
//        } else {
//            salesOrderModel.getData().setCustomerCode(objectDataCacheVo.getDataCode());
//        }
//        salesOrderModel.getData().setCode(MapUtils.getString(map,"name"));
//        salesOrderModel.getData().setBillDate(MapUtils.getLong(map,"order_time"));
//
//        List<String> owners = JSONObject.parseArray(JSONObject.toJSONString(map.get("owner")), String.class);
//
//        dingMappingEmployeeResult = dingMappingEmployeeService.findMappingByEmployeeId(ei,Integer.valueOf(owners.get(0)));
//        if(dingMappingEmployeeResult==null) {
//            dingMappingEmployeeResult = dingMappingEmployeeService.findMappingByEmployeeId(ei,1000);
//        }
//
//        salesOrderModel.getData().setEmpCode(dingMappingEmployeeResult.getDingEmployeeId());
//        //salesOrderModel.getData().setEmpCode("manager8812");
//
//        salesOrderModel.getData().setRemark(MapUtils.getString(map,"remark"));
//
//        updateSalesOrderProductInfo(corpId,ei,appId,salesOrderModel,MapUtils.getString(map,"_id"));
//        return salesOrderModel;
//    }
//
//    private void updateSalesOrderProductInfo(String corpId,int ei,long appId,SalesOrderModel salesOrderModel,String order_id) {
//        Map<String,Object> filterMap = new HashMap<>();
//        filterMap.put("field_name", "order_id");
//        filterMap.put("field_values", order_id);
//        filterMap.put("operator", "EQ");
//        List<Map<String,Object>> filter = Lists.newArrayList(filterMap);
//        CrmResponseResult<ErpObjListData> salesOrderProductResult = nCrmCloudManager.pageObjectListRest(ei,
//                SALES_ORDER_PRODUCT_API_NAME,
//                0L,
//                System.currentTimeMillis(),
//                0,
//                pageSize,
//                filter);
//        if(salesOrderProductResult.isSuccess()) {
//            List<Map<String,Object>> salesOrderProductDataList = salesOrderProductResult.getData().getDataList();
//            salesOrderProductDataList.forEach((map)->{
//                SalesOrderModel.ProductModel productModel = new SalesOrderModel.ProductModel();
//                String objectDataId = MapUtils.getString(map,"product_id");
//                ObjectDataCacheVo objectDataCacheVo = connectorObjectDataCacheService.queryOneDataByObjectDataId(corpId,appId,
//                        PRODUCT_API_NAME,objectDataId);
//                log.info("connectorHistoryDataServiceImpl.updateSalesOrderProductInfo,objectDataCacheVo={}",objectDataCacheVo);
//                if(objectDataCacheVo==null) {
//                    log.info("connectorHistoryDataServiceImpl.updateSalesOrderProductInfo,canot find product data");
//                    salesOrderModel.setError(true);
//                    //productModel.setProductCode("SP00001");
//                } else {
//                    productModel.setProductCode(objectDataCacheVo.getDataCode());
//                }
//                productModel.setQuantity(MapUtils.getString(map,"quantity"));
//                productModel.setPrice(MapUtils.getString(map,"sales_price"));
//                Boolean is_multiple_unit__v = MapUtils.getBoolean(map,"is_multiple_unit__v");
//                if(is_multiple_unit__v!=null && is_multiple_unit__v) {
//                    productModel.setUnitCode(MapUtils.getString(map,"actual_unit"));
//                } else {
//                    productModel.setUnitCode(MapUtils.getString(map,"unit"));
//                }
//                productModel.setDeliveryDate(salesOrderModel.getData().getBillDate());
//                salesOrderModel.getData().getProductList().add(productModel);
//            });
//        }
//    }
//}
