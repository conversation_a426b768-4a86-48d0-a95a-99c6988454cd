package com.facishare.open.ding.web.constants;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.ding.api.model.AppParams;
import com.facishare.open.ding.api.model.RegisterObjMapping;
import com.facishare.open.ding.api.model.RenewOrderUrl;
import com.facishare.open.ding.web.utils.DealPagesEiUtils;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/4/27 20:37
 * @Version 1.0
 */
@Slf4j
public class ConfigCenter {
    public static String TOKEN="";
    public static String ENCODING_AES_KEY="";
    public static String SUITE_KEY="";
    public static String SUITE_SECRET="";
    public static String CRM_SUITE_ID="16740006";
    public static Map<String, AppParams> APP_PARAMS_MAP= Maps.newHashMap();
    public static Long APP_CRM_ID=70480L;
    public static String h5Url="";
    //自定义字符串
    public static String NONCE_STR="Re6y5aksSpZaCRUwC";
    //数据源id与对应的对象
    public static Map<String, RegisterObjMapping> COMPONENT_MAP= Maps.newHashMap();
    //推送ERP数据平台的地址
    public static String ERP_PUSH_DATA_URL="https://www.ceshi112.com/erp/syncdata";
    public static Map<String, List<String>> REDIRECT_URL_MAP =Maps.newHashMap();
    //过期续费的链接
    public static Map<String, RenewOrderUrl> COMMON_RENEW_ORDER_URL=Maps.newHashMap();
    public static Set<String> NEW_H5_GRAY_TENANTS = Sets.newHashSet();
    public static String APPROVAL_INSTANCE_OBJ = "ApprovalInstanceObj";
    /**
     * Xor加密密钥
     */
    public static String XOR_SECRET_KEY= "";
    //密钥
    public static String BASE64_SECRET = "";

    public static String CRM_RATE_LIMIT = "{\"defaultRateLimit\":20,\"84883\":2}";

    /**
     * 接收告警人
     */
    public static Set<String> NOTIFICATION_MEMBERS = Sets.newHashSet();
    /**
     * 接收企信告警人
     */
    public static Set<String> ENTERPRISE_OPEN_NOTIFICATION_MEMBERS = Sets.newHashSet();
    /**
     * 接收告警企业
     */
    public static String NOTIFICATION_EA = "ddqybhzyl";

    public static String USE_TOOLS_ACCOUNT = "{\"84883\":[1021,1000]}";;//"{\"84883\":[\"1021\",\"1000\"]}";

    public static Set<String> TODO_GRAY_EA = Sets.newHashSet();

    public static String CRM_APPROVAL_INSTANCE_URL;

    public static Set<String> BPM_TODO_EA = Sets.newHashSet();

    /**
     * 新基座名单
     */
    public static Set<String> pagesEiSet = new HashSet<>();
    public static TreeMap<Integer, Integer> rangeMap = new TreeMap<>();

    static {
        ConfigFactory.getInstance().getConfig("fs-open-dingtalk-all", config -> {
            BASE64_SECRET = config.get("BASE64_SECRET", BASE64_SECRET);
            TOKEN = config.get("TOKEN", TOKEN);
            ENCODING_AES_KEY = config.get("ENCODING_AES_KEY", ENCODING_AES_KEY);
            SUITE_KEY = config.get("SUITE_KEY", SUITE_KEY);
            h5Url = config.get("SUITE_KEY", h5Url);
            SUITE_SECRET=config.get("SUITE_SECRET",SUITE_SECRET);
            APP_CRM_ID=config.getLong("APP_CRM_ID",APP_CRM_ID);
            CRM_SUITE_ID=config.get("CRM_SUITE_ID",CRM_SUITE_ID);
            List<AppParams> configParamsMap = JSONArray.parseArray(config.get("APP_PARAMS_MAP"), AppParams.class);
            APP_PARAMS_MAP=configParamsMap.stream().collect(Collectors.toMap(AppParams::getSuiteId, Function.identity(),(key1, key2) -> key2));
            REDIRECT_URL_MAP=JSONObject.parseObject(config.get("REDIRECT_URL_MAP"),new TypeReference<Map>(){});
            List<RegisterObjMapping> registerObjMappings = JSONArray.parseArray(config.get("COMPONENT_MAP"), RegisterObjMapping.class);
            COMPONENT_MAP=registerObjMappings.stream().collect(Collectors.toMap(RegisterObjMapping::getComponentId, Function.identity(),(key1, key2) -> key2));
            ERP_PUSH_DATA_URL = config.get("ERP_PUSH_DATA_URL", ERP_PUSH_DATA_URL);
            COMMON_RENEW_ORDER_URL = JSONObject.parseObject(config.get("COMMON_RENEW_ORDER_URL"),new TypeReference<Map<String,RenewOrderUrl>>(){});
            NEW_H5_GRAY_TENANTS =  ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("NEW_H5_GRAY_TENANTS", "")));
            XOR_SECRET_KEY = config.get("XOR_SECRET_KEY", XOR_SECRET_KEY);
            CRM_RATE_LIMIT = config.get("CRM_RATE_LIMIT", CRM_RATE_LIMIT);
            NOTIFICATION_EA = config.get("NOTIFICATION_EA", NOTIFICATION_EA);
            NOTIFICATION_MEMBERS =  ImmutableSet.copyOf(Splitter.on(",").split(config.get("NOTIFICATION_MEMBERS", "")));
            ENTERPRISE_OPEN_NOTIFICATION_MEMBERS =  ImmutableSet.copyOf(Splitter.on(",").split(config.get("ENTERPRISE_OPEN_NOTIFICATION_MEMBERS", "")));
            USE_TOOLS_ACCOUNT = config.get("USE_TOOLS_ACCOUNT", USE_TOOLS_ACCOUNT);
            TODO_GRAY_EA =  ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("TODO_GRAY_EA", "")));
            CRM_APPROVAL_INSTANCE_URL = config.get("CRM_APPROVAL_INSTANCE_URL", CRM_APPROVAL_INSTANCE_URL);
            BPM_TODO_EA = ImmutableSet.copyOf(Splitter.on(";").split(config.get("BPM_TODO_EA", "")));
            addOrUpdatePagesEis(config.get("NEW_PAGES_EI", ""));
        });

        //解密
//        TOKEN = SecurityUtil.decryptStr(TOKEN);
//        ENCODING_AES_KEY = SecurityUtil.decryptStr(ENCODING_AES_KEY);
//        SUITE_KEY = SecurityUtil.decryptStr(SUITE_KEY);
//        SUITE_SECRET = SecurityUtil.decryptStr(SUITE_SECRET);
    }

    public static void addOrUpdatePagesEis(String config) {
        //","分隔
        Set<String> eiConfigSet = ImmutableSet.copyOf(Splitter.on(",").split(config));

        for(String stringFile : eiConfigSet) {
            if(stringFile.contains(";")) {
                //";"分隔
                Set<String> pagesEiSet2 = ImmutableSet.copyOf(Splitter.on(";").split(stringFile));
                for(String stringFile2 : pagesEiSet2) {
                    if(stringFile2.contains("-")) {
                        String[] bounds = stringFile2.split("-");
                        int startRange = Integer.parseInt(bounds[0]);
                        int endRange = Integer.parseInt(bounds[1]);
                        rangeMap.put(startRange, endRange);
                    } else {
                        //普通
                        pagesEiSet.add(stringFile2);
                    }
                }
            } else {
                pagesEiSet.add(stringFile);
            }
        }
        log.info("ConfigCenter.addOrUpdatePagesEis.pagesEiSet={},rangeMap={}", pagesEiSet, rangeMap);
    }
}
