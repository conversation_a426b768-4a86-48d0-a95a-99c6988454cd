//package com.facishare.open.ding.cloud.manager;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.facishare.open.ding.api.constants.CrmObjectApiName;
//import com.facishare.open.ding.api.enums.DingObjTypeEnum;
//import com.facishare.open.ding.api.enums.SyncDirectionEnum;
//import com.facishare.open.ding.api.model.ErpPushDataObj;
//import com.facishare.open.ding.api.model.StandardData;
//import com.facishare.open.ding.api.model.SyncDataMappingModel;
//import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
//import com.facishare.open.ding.api.service.DataStorageService;
//import com.facishare.open.ding.api.service.DingMappingEmployeeService;
//import com.facishare.open.ding.api.service.SyncDataMappingsService;
//import com.facishare.open.ding.api.service.cloud.CloudAccountService;
//import com.facishare.open.ding.api.utils.MD5Util;
//import com.facishare.open.ding.api.vo.AccountVo;
//import com.facishare.open.ding.cloud.constants.TriggerAction;
//import com.facishare.open.ding.cloud.paas.models.EventData;
//import com.facishare.open.ding.common.result.Result;
//import com.fxiaoke.crmrestapi.common.data.ObjectData;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.util.StringUtils;
//
//import java.util.List;
//import java.util.Optional;
//import java.util.UUID;
//
//@Slf4j
//@Component
//public class DataProcessManager {
//
//    @Autowired
//    private DingMappingEmployeeService dingMappingEmployeeService;
//    @Autowired
//    private SyncDataMappingsService syncDataMappingsService;
//    @Autowired
//    private DataStorageService dataStorageService;
//    @Autowired
//    private CloudAccountService cloudAccountService;
//
//
//    public void process(String triggerAction, String cropId, EventData eventData) {
//        switch (eventData.getSourceData().getApiName()) {
//            case CrmObjectApiName.LEADS_OBJ_API_NAME:
//                processLeadObj(triggerAction, cropId, eventData);
//        }
//    }
//
//    //线索对象处理。多个对象后再抽象处理
//    public void processLeadObj(String triggerAction, String cropId, EventData eventData) {
//        if (triggerAction != TriggerAction.ADD) {
//            log.warn("暂时不支持的操作");
//            return;
//        }
//        int ei = Integer.valueOf(eventData.getSourceData().getTenantId());
//        ObjectData masterVal = new ObjectData();
//
//        com.facishare.open.ding.cloud.paas.models.ObjectData sourceData = eventData.getSourceData();
//
//        if (StringUtils.isEmpty(sourceData.get("promotion_channel"))) {
//            log.info("推广渠道为空。数据被过滤:{}", JSONObject.toJSONString(eventData));
//            return;
//        }
//
//        masterVal.put("customer_follow_up_status", "option_new_acquisition");
//        masterVal.put("creator_userid", sourceData.get("created_by"));
//        masterVal.put("customer_name", sourceData.get("name"));
//        masterVal.put("address", sourceData.get("address"));
//        masterVal.put("customer_phone", sourceData.get("mobile"));
//        masterVal.put("email", sourceData.get("email"));
//        masterVal.put("remark", sourceData.get("remark"));
//        masterVal.put("tel", sourceData.get("tel"));
//        masterVal.put("company", sourceData.get("company"));
//        masterVal.put("account_name", sourceData.get("account_id"));
//        StandardData standardData = new StandardData();
//        standardData.setObjAPIName(DingObjTypeEnum.getEnumByCrmApiName(eventData.getSourceData().getApiName())
//                                                  .getDingObjApiName());
//        standardData.setMasterFieldVal(masterVal);
//        log.debug("standarData:{}", JSONObject.toJSONString(standardData));
//        //处理数据格式
//        Object creator_userid = masterVal.get("creator_userid");
//        if (!StringUtils.isEmpty(creator_userid)) {
//            if (creator_userid instanceof JSONArray) {
//                JSONArray creatorUserid = (JSONArray) creator_userid;
//                DingMappingEmployeeResult dingMappingEmployeeResult =
//                  dingMappingEmployeeService.findMappingByEmployeeId(ei, creatorUserid.getInteger(0));
//                if (dingMappingEmployeeResult != null) {
//                    masterVal.put("creator_userid", dingMappingEmployeeResult.getDingEmployeeId());
//                } else {
//                    log.info("创建人员id转换失败:{}", JSONObject.toJSONString(eventData));
//                    return;
//                }
//            }
//            if (creator_userid instanceof List) {
//                List<String> creatorUserid = (List) creator_userid;
//                if (creatorUserid.size() > 0) {
//                    DingMappingEmployeeResult dingMappingEmployeeResult =
//                      dingMappingEmployeeService.findMappingByEmployeeId(ei, Integer.valueOf(creatorUserid.get(0)));
//                    if (dingMappingEmployeeResult != null) {
//                        masterVal.put("creator_userid", dingMappingEmployeeResult.getDingEmployeeId());
//                    } else {
//                        log.info("创建人员id转换失败:{}", JSONObject.toJSONString(eventData));
//                        return;
//                    }
//                }
//
//            }
//
//        }
//
//        Object account_id = masterVal.get("account_name");
//        if (!StringUtils.isEmpty(account_id)) {
//            Result<AccountVo> accountVoResult = cloudAccountService.queryCustomerById(ei, String.valueOf(account_id));
//            if (accountVoResult.isSuccess() && accountVoResult.getData() != null) {
//                masterVal.put("account_name", accountVoResult.getData().getName());
//            } else {
//                masterVal.remove("account_name");
//            }
///*            Result<List<SyncDataMappingModel>> mappingModelResult =
//              syncDataMappingsService.loadSyncDataMapping(ei, cropId, CrmObjectApiName.ACCOUNT_API_NAME, null, SyncDirectionEnum.TO_FXIAOKE, account_id
//                .toString());
//            //人员部门库和syncdata库正式环境不是一个库....所以只能调用dobbo获取数据
//            if (mappingModelResult!=null&&mappingModelResult.isSuccess()&&mappingModelResult.getData()!=null){
//                Optional<SyncDataMappingModel> first =
//                  mappingModelResult.getData().stream().filter(t -> t.getCreated() == 2).findFirst();
//                if (first.isPresent()){
//                    masterVal.put("account_name",first.get().getDingDataId());
//                }
//            }*/
//        }
//
//        Result<List<SyncDataMappingModel>> mappingModelResult =
//          syncDataMappingsService.loadSyncDataMapping(ei, cropId, CrmObjectApiName.LEADS_OBJ_API_NAME, null, SyncDirectionEnum.FROM_FXIAOKE, eventData
//            .getSourceData()
//            .getId());
//        SyncDataMappingModel syncDataMappingModel = null;
//        if (mappingModelResult.getData() != null) {
//            Optional<SyncDataMappingModel> first = mappingModelResult.getData()
//                                                                     .stream()
//                                                                     .filter(t -> t.getDirector()
//                                                                       == SyncDirectionEnum.FROM_FXIAOKE.getType())
//                                                                     .findFirst();
//            if (first.isPresent()) {
//                syncDataMappingModel = first.get();
//            } else {
//                //写中间表
//                if (syncDataMappingModel == null) {
//                    syncDataMappingModel = new SyncDataMappingModel();
//                    syncDataMappingModel.setVersion(1);
//                    syncDataMappingModel.setCreated(1);//CREATED_FAILED
//                    syncDataMappingModel.setEi(ei);
//                    syncDataMappingModel.setStatus(2);
//
//                    syncDataMappingModel.setCropId(cropId);
//                    syncDataMappingModel.setCreateTime(System.currentTimeMillis());
//                }
//
//                syncDataMappingModel.setCrmObjectApiName(eventData.getSourceData().getApiName());
//                syncDataMappingModel.setCrmDataId(eventData.getSourceData().getId());
//                syncDataMappingModel.setDingObjectApiName(standardData.getObjAPIName());
//                syncDataMappingModel.setDingDataId(UUID.randomUUID().toString());
//
//                syncDataMappingModel.setMdStr(MD5Util.getMD5(JSONObject.toJSONString(standardData)));
//                syncDataMappingModel.setDirector(SyncDirectionEnum.FROM_FXIAOKE.getType());
//                syncDataMappingModel.setUpdateTime(System.currentTimeMillis());
//                syncDataMappingModel.setRemark("同步中");
//                Result<Boolean> saveResult = syncDataMappingsService.updateSyncDataMapping(syncDataMappingModel);
//                if (saveResult.getData()) {
//                    log.debug("中间表写入成功");
//                } else {
//                    log.info("写中间表失败：{}",JSONObject.toJSONString(syncDataMappingModel));
//                    return;
//                }
//                mappingModelResult =
//                  syncDataMappingsService.loadSyncDataMapping(ei, cropId, CrmObjectApiName.LEADS_OBJ_API_NAME, null, SyncDirectionEnum.FROM_FXIAOKE, eventData
//                    .getSourceData()
//                    .getId());
//                if (mappingModelResult.getData() != null) {
//                    first = mappingModelResult.getData()
//                                              .stream()
//                                              .filter(t -> t.getDirector() == SyncDirectionEnum.FROM_FXIAOKE.getType())
//                                              .findFirst();
//                    if (first.isPresent()) {
//                        syncDataMappingModel = first.get();
//                    }
//                }
//            }
//        }
//
//        //写钉钉接口
//        ErpPushDataObj pushDataObj = new ErpPushDataObj();
//        pushDataObj.setObjAPIName(standardData.getObjAPIName());
//        pushDataObj.setMasterFieldVal(standardData.getMasterFieldVal());
//        Result<String> result = dataStorageService.createPersonCustomer(pushDataObj, ei);
//
//        if (result.isSuccess()) {
//            syncDataMappingModel.setDingDataId(result.getData());
//            syncDataMappingModel.setCreated(2);//已经创建
//            syncDataMappingModel.setStatus(1);//SUCCESS
//            syncDataMappingModel.setRemark("同步成功");
//        } else {
//            syncDataMappingModel.setRemark(result.getErrorMessage());
//        }
//        Result<Boolean> saveResult = syncDataMappingsService.updateSyncDataMapping(syncDataMappingModel);
//        if (saveResult.getData()) {
//            log.debug("中间表写入成功");
//        } else {
//            log.info("写中间表失败：{}",JSONObject.toJSONString(syncDataMappingModel));
//            return;
//        }
//
//    }
//
//}
