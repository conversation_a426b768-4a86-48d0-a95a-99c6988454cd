package com.facishare.open.ding.api.enums;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/7/13 15:09
 * <AUTHOR> yin<PERSON>@fxiaoke.com
 * @version 1.0 
 */
public enum BizDataTypeEnum {

    /** 高级 **/
    HIGH_LEVEL(1),

    /** 中低级 **/
    MEDIUM_LEVEL(2),

    ;

    private int type;

    BizDataTypeEnum(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public static boolean isInvalid(Integer type) {
        if (type != null) {
            for (BizDataTypeEnum syncTypeEnum : BizDataTypeEnum.values()) {
                if (syncTypeEnum.getType() == type) {
                    return false;
                }
            }
        }
        return true;
    }

}
