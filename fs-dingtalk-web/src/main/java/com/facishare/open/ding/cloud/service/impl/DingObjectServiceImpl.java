package com.facishare.open.ding.cloud.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.ding.api.model.DingBatchData;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.service.cloud.DingObjectService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.manager.DataPushManager;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.common.result.Result;
import com.jayway.jsonpath.JsonPath;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.asm.Advice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/2/10 11:04
 * @Version 1.0
 */
@Service("dingObjectServiceImpl")
@Slf4j
public class DingObjectServiceImpl implements DingObjectService {
    @Autowired
    private DingManager dingManager;
    @Autowired
    private DingCorpMappingService corpMappingService;
    @Autowired
    private DataPushManager dataPushManager;
    @Override
    public Result<String> queryCustomerData(String dingCorpId, String suiteId, String dingDataId) {
        String data = dingManager.queryCustomerDataById(dingCorpId, suiteId, dingDataId);
        return Result.newSuccess(data);
    }

    @Override
    public Result<String> queryConcatData(String dingCorpId, String suiteId, String dingDataId) {
        String data = dingManager.queryContactDataById(dingCorpId, suiteId, dingDataId);
        return Result.newSuccess(data);
    }

    @Override
    public Result<String> syncCustomerDataInTime(Integer tenantId, String suiteId, String dingDataId) {
        Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryByEi(tenantId);
        String corpId=corpResult.getData().get(0).getDingCorpId();
        String customerResult = dingManager.queryCustomerDataById(corpId, suiteId, dingDataId);
        log.info("syncCustomerDataInTime data:{}",customerResult);
        Map<String,Object> map= JsonPath.read(customerResult,"$.result_list[0]");
        DingBatchData dingObjData = JSONObject.parseObject(JSONObject.toJSONString(map), new TypeReference<DingBatchData>() {
        });
        Map dataMap = JSONObject.parseObject(dingObjData.getData(), Map.class);
        String customerName=dataMap.get("customer_name").toString();
        dataPushManager.syncCustomerDataInTime(dingObjData.getInstance_id(),customerName,String.valueOf(tenantId));
        return null;
    }

    @Override
    public Result<String> syncConcatDataInTime(Integer tenantId, String suiteId, String dingDataId) {
        return null;
    }
}
