//package com.facishare.open.ding.cloud.service.impl.connector;
//
//import com.alibaba.fastjson.JSONObject;
//import com.facishare.open.ding.api.constants.CrmObjectApiName;
//import com.facishare.open.ding.api.model.connector.CustomerModel;
//import com.facishare.open.ding.api.model.connector.OpportunityModel;
//import com.facishare.open.ding.api.model.connector.SalesOrderModel;
//import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
//import com.facishare.open.ding.api.service.DingCorpMappingService;
//import com.facishare.open.ding.api.service.DingMappingEmployeeService;
//import com.facishare.open.ding.api.service.cloud.connector.ConnectorHistoryDataService;
//import com.facishare.open.ding.api.service.cloud.connector.ConnectorObjectDataCacheService;
//import com.facishare.open.ding.api.vo.DingCorpMappingVo;
//import com.facishare.open.ding.api.vo.ObjectDataCacheVo;
//import com.facishare.open.ding.cloud.arg.BatchSendDataArg;
//import com.facishare.open.ding.cloud.constants.ConfigCenter;
//import com.facishare.open.ding.cloud.constants.TriggerAction;
//import com.facishare.open.ding.cloud.constants.TriggerEventId;
//import com.facishare.open.ding.cloud.manager.DingConnectorManager;
//import com.facishare.open.ding.cloud.manager.DingManager;
//import com.facishare.open.ding.cloud.paas.constants.EventTypeEnum;
//import com.facishare.open.ding.cloud.paas.handler.EventDataHandler;
//import com.facishare.open.ding.cloud.paas.models.EventData;
//import com.facishare.open.ding.cloud.result.BatchSendDataResult;
//import com.google.common.collect.Lists;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.MapUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.Map;
//import java.util.UUID;
//
//import static com.facishare.open.ding.cloud.constants.Constant.APP_ID;
//
///**
// * 连接器CRM对象变更处理器
// * <AUTHOR>
// * @date 2021/07/29
// */
//@Component
//@Slf4j
//public class ConnectorEventDataHandler implements EventDataHandler {
//    @Autowired
//    private DingCorpMappingService dingCorpMappingService;
//
//    @Autowired
//    private DingConnectorManager dingConnectorManager;
//
//    @Autowired
//    private DingManager dingManager;
//
//    @Autowired
//    private ConnectorHistoryDataService connectorHistoryDataService;
//
//    @Autowired
//    private ConnectorObjectDataCacheService connectorObjectDataCacheService;
//
//    @Autowired
//    private DingMappingEmployeeService dingMappingEmployeeService;
//
//    private List<String> supportObjectList = Lists.newArrayList(CrmObjectApiName.ACCOUNT_API_NAME,
//            CrmObjectApiName.SALES_ORDER_API_NAME,
//            CrmObjectApiName.OPPORTUNITY_API_NAME);
//
//    @Override
//    public void handle(List<EventData> eventDataList) {
//        for(EventData eventData : eventDataList) {
//            if(!supportObjectList.contains(eventData.getSourceData().getApiName())) continue;
//            log.info("ConnectorEventDataHandler.handle,eventData={}",eventData);
//
//            int ei = Integer.valueOf(eventData.getSourceData().getTenantId());
//
//            DingCorpMappingVo dingCorpMappingVo = dingCorpMappingService.queryByConnector(1,ei,APP_ID);
//            if(dingCorpMappingVo==null) continue;
//
//            //过滤从ERP->CRM方向的客户和订单数据，避免重复触发CRM->ERP方向的同步
//            if(canFilterData(ei,
//                    dingCorpMappingVo.getDingCorpId(),
//                    eventData.getSourceData().getApiName(),
//                    MapUtils.getString(eventData.getSourceData(),"_id"))) return;
//
//            String accessToken = dingManager.getAccessToken(dingCorpMappingVo.getDingCorpId(), ConfigCenter.CRM_SUITE_ID).getData();
//            if(StringUtils.isEmpty(accessToken)) {
//                return;
//            }
//
//            String jsonData = null;
//            String triggerId = null;
//            String dataCode = null;
//            if(CrmObjectApiName.ACCOUNT_API_NAME.equalsIgnoreCase(eventData.getSourceData().getApiName())) {
//                log.info("ConnectorEventDataHandler.handle,CustomerModel");
//                CustomerModel model = connectorHistoryDataService.getCustomerModel(eventData.getSourceData(),
//                        dingCorpMappingVo.getEi());
//                jsonData = JSONObject.toJSONString(model);
//                triggerId = TriggerEventId.FS_CUSTOMER_EVENT;
//                dataCode = model.getData().getCode();
//
//                log.info("ConnectorEventDataHandler.handle,CustomerModel={}",model);
//            } else if(CrmObjectApiName.SALES_ORDER_API_NAME.equalsIgnoreCase(eventData.getSourceData().getApiName())) {
//                log.info("ConnectorEventDataHandler.handle,SalesOrderModel");
//                SalesOrderModel model = connectorHistoryDataService.getSalesOrderModel(eventData.getSourceData(),
//                        dingCorpMappingVo.getDingCorpId(),
//                        dingCorpMappingVo.getEi(),
//                        dingCorpMappingVo.getAppCode());
//                jsonData = JSONObject.toJSONString(model);
//                triggerId = TriggerEventId.FS_SALES_ORDER_EVENT;
//                dataCode = model.getData().getCode();
//
//                log.info("ConnectorEventDataHandler.handle,SalesOrderModel={}",model);
//            } else if(CrmObjectApiName.OPPORTUNITY_API_NAME.equalsIgnoreCase(eventData.getSourceData().getApiName())) {
//                log.info("ConnectorEventDataHandler.handle,OpportunityModel");
//                OpportunityModel model = getOpportunityModel(eventData.getSourceData(),
//                        dingCorpMappingVo.getDingCorpId(),
//                        dingCorpMappingVo.getEi(),
//                        dingCorpMappingVo.getAppCode());
//                jsonData = JSONObject.toJSONString(model);
//                triggerId = TriggerEventId.FS_OPPORTUNITY_EVENT;
//                dataCode = model.getData().getCode();
//
//                log.info("ConnectorEventDataHandler.handle,OpportunityModel={}",model);
//            }
//            if(StringUtils.isEmpty(jsonData) || StringUtils.isEmpty(triggerId) || StringUtils.isEmpty(dataCode)) continue;
//
//            ObjectDataCacheVo objectDataCacheEntity = connectorObjectDataCacheService.queryOneData(dingCorpMappingVo.getDingCorpId(),
//                    dingCorpMappingVo.getAppCode(),
//                    eventData.getSourceData().getApiName(),
//                    dataCode);
//
//            log.info("ConnectorEventDataHandler.handle,objectDataCacheEntity={}",objectDataCacheEntity);
//
//            String triggerAction = null;
//            if(objectDataCacheEntity == null) {
//                objectDataCacheEntity = new ObjectDataCacheVo();
//                objectDataCacheEntity.setId(UUID.randomUUID().toString());
//                objectDataCacheEntity.setCorpId(dingCorpMappingVo.getDingCorpId());
//                objectDataCacheEntity.setEi(dingCorpMappingVo.getEi());
//                objectDataCacheEntity.setAppId(dingCorpMappingVo.getAppCode());
//                objectDataCacheEntity.setObjectApiName(eventData.getSourceData().getApiName());
//                objectDataCacheEntity.setDataCode(dataCode);
//                objectDataCacheEntity.setObjectDataId(eventData.getSourceData().getId());
//                objectDataCacheEntity.setJsonData(jsonData);
//                objectDataCacheEntity.setSyncDirection(0);
//                objectDataCacheEntity.setSynced(false);
//                objectDataCacheEntity.setSyncFailed(false);
//                objectDataCacheEntity.setFailedReason(null);
//                objectDataCacheEntity.setDeleted(false);
//                connectorObjectDataCacheService.insert(objectDataCacheEntity);
//                triggerAction = TriggerAction.ADD;
//            } else {
//                objectDataCacheEntity.setJsonData(jsonData);
//                triggerAction = TriggerAction.UPDATE;
//            }
//
//            if(eventData.getSourceEventType() == EventTypeEnum.INVALID.getType()
//                    || eventData.getSourceEventType() == EventTypeEnum.DELETE_DIRECT.getType()) {
//                triggerAction = TriggerAction.DELETE;
//                objectDataCacheEntity.setDeleted(true);
//            }
//
//            BatchSendDataArg batchSendDataArg = new BatchSendDataArg();
//            batchSendDataArg.setAppId(dingCorpMappingVo.getAppCode());
//
//            BatchSendDataArg.TriggerData triggerData = new BatchSendDataArg.TriggerData();
//            triggerData.setTriggerId(triggerId);
//            triggerData.setAction(triggerAction);
//            triggerData.setJsonData(jsonData);
//            triggerData.setDataGmtCreate(eventData.getSourceData().getLong("create_time"));
//            triggerData.setDataGmtModified(eventData.getSourceData().getLong("last_modified_time"));
//
//            batchSendDataArg.getTriggerDataList().add(triggerData);
//
//            BatchSendDataResult batchSendDataResult = dingConnectorManager.batchSendData(accessToken,batchSendDataArg);
//            log.info("ConnectorEventDataHandler.handle,batchSendDataResult={}",batchSendDataResult);
//
//            objectDataCacheEntity.setSynced(true);
//            BatchSendDataResult.DataResult dataResult = batchSendDataResult.getList().get(0);
//            objectDataCacheEntity.setSyncFailed(dataResult.isSuccess());
//            if(!dataResult.isSuccess()) {
//                objectDataCacheEntity.setFailedReason("errCode=" + dataResult.getSubErrCode() + ",errMsg=" + dataResult.getSubErrMsg());
//            }
//
//            connectorObjectDataCacheService.update(objectDataCacheEntity);
//            log.info("ConnectorEventDataHandler.handle,success");
//        }
//    }
//
//    private boolean canFilterData(int ei,String corpId,String objectApiName,String objectDataId) {
//        ObjectDataCacheVo objectDataCacheVo = connectorObjectDataCacheService.queryOneDataByObjectDataId(corpId,
//                APP_ID,
//                objectApiName,
//                objectDataId);
//        if(objectDataCacheVo==null) return false;
//        if(objectDataCacheVo.getSyncDirection()==0) return false;
//        long offset = System.currentTimeMillis() - objectDataCacheVo.getUpdateTime().getTime();
//        //如果同步时间差大于20s，则默认放行，如果小于20s，刚拦截
//        if(offset> 20 * 1000) return false;
//        log.info("ConnectorEventDataHandler.canFilterData,数据刚从erp同步到CRM，不需要再次同步回erp,ei={},objectApiName={},objectDataId={}",ei,objectApiName,objectDataId);
//        return true;
//    }
//
//    private OpportunityModel getOpportunityModel(Map<String,Object> map, String corpId, int ei, long appId) {
//        DingMappingEmployeeResult dingMappingEmployeeResult = dingMappingEmployeeService.findMappingByEmployeeId(ei,1000);
//
//        OpportunityModel model = new OpportunityModel();
//        model.setOperateUserId(dingMappingEmployeeResult.getDingEmployeeId());
//
//        ObjectDataCacheVo objectDataCacheVo = connectorObjectDataCacheService.queryOneDataByObjectDataId(corpId,
//                appId,
//                CrmObjectApiName.ACCOUNT_API_NAME,
//                MapUtils.getString(map,"account_id"));
//        log.info("ConnectorEventDataHandler.getOpportunityModel,objectDataCacheVo={}",objectDataCacheVo);
//        if(objectDataCacheVo==null) {
//            model.setError(true);
//        } else {
//            model.getData().setRelatedCustomerId(objectDataCacheVo.getDataCode());
//        }
//        model.getData().setCode(MapUtils.getString(map,"name"));
//        model.getData().setName(MapUtils.getString(map,"name"));
//        model.getData().setExpectedExpiredAt(MapUtils.getLong(map,"close_date"));
//        model.getData().setExpectedAmount(MapUtils.getDouble(map,"amount"));
//
//        List<String> owners = JSONObject.parseArray(JSONObject.toJSONString(map.get("owner")), String.class);
//
//        dingMappingEmployeeResult = dingMappingEmployeeService.findMappingByEmployeeId(ei,Integer.valueOf(owners.get(0)));
//        if(dingMappingEmployeeResult==null) {
//            dingMappingEmployeeResult = dingMappingEmployeeService.findMappingByEmployeeId(ei,1000);
//        }
//
//        model.getData().setOwnerEmpCode(dingMappingEmployeeResult.getDingEmployeeId());
//        model.getData().setRemark(MapUtils.getString(map,"remark"));
//
//        return model;
//    }
//}
