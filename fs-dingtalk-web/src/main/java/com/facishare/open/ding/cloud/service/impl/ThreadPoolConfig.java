package com.facishare.open.ding.cloud.service.impl;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2022/1/24 10:22
 * @Version 1.0
 */
@Configuration
public class ThreadPoolConfig  {
    @Bean
    public ExecutorService emailTaskPool() {
        return new ThreadPoolExecutor(2, 4, 60L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>());
    }
}
