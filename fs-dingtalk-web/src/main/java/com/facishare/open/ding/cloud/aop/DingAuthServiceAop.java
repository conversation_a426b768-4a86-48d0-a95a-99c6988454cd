package com.facishare.open.ding.cloud.aop;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.open.ding.api.exception.SyncDataException;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.utils.ErasePasswordUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collection;
import java.util.Objects;


@Aspect
@Slf4j
@Component
public class DingAuthServiceAop {

    @Resource
    @Qualifier("dingAuthServiceImplForSelfBuiltApp")
    private DingAuthService dingAuthServiceImplForSelfBuiltApp;

    @Around("execution(* com.facishare.open.ding.cloud.service.impl.DingAuthServiceImpl.*(..))")
    public Result interceptor(ProceedingJoinPoint point) throws Throwable {
        Result result = null;
        // 获取类的完整路径和方法名称
        String fullClassName = point.getTarget().getClass().getName();
        String methodName = point.getSignature().getName();
        //计时器
        StopWatch stopWatch = new StopWatch();
        String args = ErasePasswordUtils.erase(ObjectUtils.isNotEmpty(point.getArgs()) ? Arrays.toString(point.getArgs()) : null);
        stopWatch.start();
        //处理结果直接返回
        try {
            //需要请求转发的方法
            result = (Result) point.proceed();
            if (!result.isSuccess() || Objects.isNull(result.getData())
                    || (result.getData() instanceof Collection && CollectionUtils.isEmpty((Collection<?>) result.getData()))){
                log.info("LogAop.interceptor,methodName={}.", methodName);
                MethodSignature signature = (MethodSignature) point.getSignature();
                Method method = DingAuthService.class.getMethod(methodName, signature.getParameterTypes());
                Object invoke = method.invoke(dingAuthServiceImplForSelfBuiltApp, point.getArgs());
                log.info("LogAop.interceptor,invoke={}.", invoke);
                result = (Result) invoke;
            }
            stopWatch.stop();
            String result2 = ErasePasswordUtils.erase(ObjectUtils.isNotEmpty(result) ? String.valueOf(result) : null);
            log.info(" AOP trace fullClassName:{} ,totalTime:{}ms,methodName:{} arg{}, result:{}",  fullClassName, stopWatch.getTime(), methodName,args,result2);
        }catch (Exception e){

            stopWatch.stop();
            if (e instanceof SyncDataException){
                log.warn(" classname:{},totalTime:{}ms, methedName:{}, args:{}, result:{},exception:{}",
                        fullClassName, stopWatch.getTime(),methodName, args,result,e.getMessage());
            }else {
                log.error(" classname:{},totalTime:{}ms, methedName:{}, args:{}, result:{},exception:{}",
                        fullClassName, stopWatch.getTime(),methodName, args,result,e.getMessage());
            }
            throw e;
        }
        return result;
    }
}
