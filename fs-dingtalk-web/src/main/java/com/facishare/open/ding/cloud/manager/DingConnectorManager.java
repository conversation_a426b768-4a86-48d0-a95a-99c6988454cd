package com.facishare.open.ding.cloud.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.cloud.arg.BatchPollDataArg;
import com.facishare.open.ding.cloud.arg.BatchSendDataArg;
import com.facishare.open.ding.cloud.result.BatchPollDataResult;
import com.facishare.open.ding.cloud.result.BatchSendDataResult;
import com.facishare.open.ding.cloud.result.PollDataResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 钉钉连接器相关网络接口封装
 * <AUTHOR>
 * @date 2021/07/23
 */
@Service
@Slf4j
public class DingConnectorManager {
    private static String SEND_EVENT_URL = "https://oapi.dingtalk.com/topapi/connector/trigger/send_v2";
    private static String BATCH_SEND_DATA_URL = "https://api.dingtalk.com/v1.0/connector/triggers/data/sync";
    private static String BATCH_POLL_DATA_URL = "https://api.dingtalk.com/v1.0/connector/data";
    private static String POLL_DATA_URL = "https://api.dingtalk.com/v1.0/connector/data";


    @Autowired
    private HttpCloudManager httpCloudManager;

//    public SendEventResult sendEvent(String accessToken, String jsonData, boolean test) {
//        String url = SEND_EVENT_URL+"?access_token="+accessToken;
//
//        List<Map<String,String>> trigger_msg_data_list = new ArrayList<>();
//
//        Map<String,String> msgData = new HashMap<>();
//        msgData.put("trigger_id","FS_CUSTOMER_EVENT");
//        msgData.put("dd_event_time", DateTimeUtils.format(new Date()));
//        msgData.put("json_data",jsonData);
//
//        trigger_msg_data_list.add(msgData);
//
//        JSONObject trigger_msg_request = new JSONObject();
//        trigger_msg_request.put("trigger_msg_data_list",trigger_msg_data_list);
//        trigger_msg_request.put("test",test);
//
//        JSONObject requestParam = new JSONObject();
//        requestParam.put("trigger_msg_request",trigger_msg_request);
//
//        String jsonResult = httpManager.postUrl(url,requestParam,DingManager.createHeader());
//        SendEventResult result = JSONObject.parseObject(jsonResult, SendEventResult.class);
//        return result;
//    }

    public BatchSendDataResult batchSendData(String accessToken, BatchSendDataArg batchSendDataArg) {
        String url = BATCH_SEND_DATA_URL;

        Map<String,String> headerMap = DingManager.createHeader();
        headerMap.put("x-acs-dingtalk-access-token",accessToken);

        String jsonResult = httpCloudManager.postUrl(url,batchSendDataArg,headerMap);
        BatchSendDataResult result = JSONObject.parseObject(jsonResult, BatchSendDataResult.class);
        return result;
    }

    public BatchPollDataResult batchPollData(String accessToken, BatchPollDataArg batchPollDataArg) {
        if(StringUtils.isEmpty(batchPollDataArg.getDataModelId())) return null;

        StringBuilder sb = new StringBuilder();
        sb.append("dataModelId="+batchPollDataArg.getDataModelId());
        if(StringUtils.isNotEmpty(batchPollDataArg.getAppId())) {
            sb.append("&appId="+batchPollDataArg.getAppId());
        }
        if(StringUtils.isNotEmpty(batchPollDataArg.getNextToken())) {
            sb.append("&nextToken="+batchPollDataArg.getNextToken());
        }
        if(StringUtils.isNotEmpty(batchPollDataArg.getNextToken())) {
            sb.append("&nextToken="+batchPollDataArg.getNextToken());
        }
        if(batchPollDataArg.getMaxResults()!=null) {
            sb.append("&maxResults="+batchPollDataArg.getMaxResults());
        }
        if(batchPollDataArg.getMinDatetime()!=null && batchPollDataArg.getMaxDatetime()!=null) {
            sb.append("&datetimeFilterField=dataGmtModified");
            sb.append("&minDatetime="+batchPollDataArg.getMinDatetime());
            sb.append("&maxDatetime="+batchPollDataArg.getMaxDatetime());
        }

        String url = BATCH_POLL_DATA_URL + "?" + sb.toString();

        Map<String,String> headerMap = DingManager.createHeader();
        headerMap.put("x-acs-dingtalk-access-token",accessToken);

        String jsonResult = httpCloudManager.getUrl(url,headerMap);
        BatchPollDataResult result = JSONObject.parseObject(jsonResult, BatchPollDataResult.class);
        return result;
    }

    public PollDataResult pollData(String accessToken, String dataModelId,String primaryKey,String appId) {
        if(StringUtils.isEmpty(dataModelId)) return null;

        String url = POLL_DATA_URL +"/" + dataModelId +"/?primaryKey=" + primaryKey + "&appId="+appId;

        Map<String,String> headerMap = DingManager.createHeader();
        headerMap.put("x-acs-dingtalk-access-token",accessToken);

        String jsonResult = httpCloudManager.getUrl(url,headerMap);
        PollDataResult result = JSONObject.parseObject(jsonResult, PollDataResult.class);
        return result;
    }
}
