package com.facishare.open.ding.cloud.template.outer.event.order;

import com.facishare.enterprise.event.EnterpriseAddEvent;
import com.facishare.open.ding.api.model.OrderModel;
import com.facishare.open.ding.cloud.result.CreateFsCustomerResult;
import com.facishare.open.ding.cloud.arg.WaitingTenantCreateArg;
import com.facishare.open.ding.cloud.result.SaveOrderResult;
import com.facishare.open.ding.cloud.service.impl.OrderEvent17ServiceImpl;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.event.order.OpenEnterpriseHandlerTemplate;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class DingTalkOpenEnterpriseHandlerTemplate extends OpenEnterpriseHandlerTemplate {
    @Resource
    private OrderEvent17ServiceImpl orderEvent17Service;

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    
    @Override
    public void saveOrder(MethodContext context) {
        log.info("DingTalkOpenEnterpriseHandlerTemplate.saveOrder,context={}",context);
        OrderModel orderModel = context.getData();
        Result<SaveOrderResult> result = orderEvent17Service.saveOrder(orderModel);

        context.setData(result.getData());

        log.info("DingTalkOpenEnterpriseHandlerTemplate.saveOrder,result={}",result);

        log.info("DingTalkOpenEnterpriseHandlerTemplate.saveOrder,context.end={}",context);
    }

    @Override
    public void initEnterpriseAndAdminMapping(MethodContext context) {
        log.info("DingTalkOpenEnterpriseHandlerTemplate.initEnterpriseAndAdminMapping,context={}",context);
        //暂时不实现
    }

    @Override
    public void createFsCustomerAndUpdateEnterpriseAndAdminMapping(MethodContext context) {
        log.info("DingTalkOpenEnterpriseHandlerTemplate.createFsCustomerAndUpdateEnterpriseAndAdminMapping,context={}",context);

        SaveOrderResult saveOrderResult = context.getData();
        OrderModel orderModel = saveOrderResult.getOrderModel();

        Result<CreateFsCustomerResult> result = orderEvent17Service.createFsCustomer(orderModel);
        log.info("DingTalkOpenEnterpriseHandlerTemplate.createFsCustomerAndUpdateEnterpriseAndAdminMapping,result={}",result);

        if(result.isSuccess()) {
            Map<String,Object> contextMap = new HashMap<>();
            contextMap.put("order",saveOrderResult);
            contextMap.put("customer",result.getData());
            context.setData(contextMap);
            context.setResult(TemplateResult.newSuccess());
        } else {
            context.setResult(TemplateResult.newError(result.getErrorCode(),result.getErrorMessage()));
        }
        log.info("DingTalkOpenEnterpriseHandlerTemplate.createFsCustomerAndUpdateEnterpriseAndAdminMapping,context.end={}",context);
    }

    @Override
    public void createFsOrder(MethodContext context) {
        log.info("DingTalkOpenEnterpriseHandlerTemplate.createFsOrder,context={}",context);
        Map<String,Object> contextMap = context.getData();
        SaveOrderResult order = (SaveOrderResult) contextMap.get("order");
        CreateFsCustomerResult customer = (CreateFsCustomerResult) contextMap.get("customer");

        Result<String> result = orderEvent17Service.createFsOrder(order,customer.getEa(),customer.getEn());
        log.info("DingTalkOpenEnterpriseHandlerTemplate.createFsOrder,result={}",result);
        if(result.isSuccess()) {
            context.setResult(TemplateResult.newSuccess());
        } else {
            context.setResult(TemplateResult.newError(result.getErrorCode(),result.getErrorMessage()));
        }
        log.info("DingTalkOpenEnterpriseHandlerTemplate.createFsOrder,context.end={}",context);
    }

    @Override
    public boolean isEnterpriseBind(String ea) {
        log.info("DingTalkOpenEnterpriseHandlerTemplate.isEnterpriseBind,ea={}",ea);
        return orderEvent17Service.isEnterpriseBind(ea).getData();
    }

//    @Override
//    public TemplateResult onEnterpriseOpened(Object data) {
//        return super.onEnterpriseOpened(data);
//    }

    @Override
    public void updateEnterpriseAndAdminMapping(MethodContext context) {
        log.info("DingTalkOpenEnterpriseHandlerTemplate.updateEnterpriseAndAdminMapping,context={}",context);
        EnterpriseAddEvent enterpriseAddEvent = context.getData();

        Result<WaitingTenantCreateArg> result = orderEvent17Service.updateEnterpriseAndAdminMapping(enterpriseAddEvent.getEnterpriseAccount(),
                enterpriseAddEvent.getEnterpriseId());
        log.info("DingTalkOpenEnterpriseHandlerTemplate.updateEnterpriseAndAdminMapping,result={}",result);

        if(result.isSuccess()) {
            Map<String,Object> contextMap = new HashMap<>();
            contextMap.put("tenant",result.getData());
            context.setData(contextMap);

            context.setResult(TemplateResult.newSuccess());
        } else {
            context.setResult(TemplateResult.newError(result.getErrorCode(),result.getErrorMessage()));
        }

        log.info("DingTalkOpenEnterpriseHandlerTemplate.updateEnterpriseAndAdminMapping,context.end={}",context);
    }

    @Override
    public void sendWelcomeMsg(MethodContext context) {
        log.info("DingTalkOpenEnterpriseHandlerTemplate.sendWelcomeMsg,context={}",context);
        Map<String,Object> contextMap = context.getData();
        WaitingTenantCreateArg tenantCreateArg = (WaitingTenantCreateArg) contextMap.get("tenant");

        Result<Void> result = orderEvent17Service.sendWelcomeMsg(tenantCreateArg.getEa());
        log.info("DingTalkOpenEnterpriseHandlerTemplate.sendWelcomeMsg,result={}",result);
    }

    @Override
    public void initEnterpriseContacts(MethodContext context) {
        log.info("DingTalkOpenEnterpriseHandlerTemplate.initEnterpriseContacts,context={}",context);
        Map<String,Object> contextMap = context.getData();
        WaitingTenantCreateArg tenantCreateArg = (WaitingTenantCreateArg) contextMap.get("tenant");

        Result<Void> result = orderEvent17Service.initEnterpriseContacts(tenantCreateArg.getEa(),
                tenantCreateArg.getDingCorpId(),
                tenantCreateArg.getOrderModel().getSuiteId());
        log.info("DingTalkOpenEnterpriseHandlerTemplate.initEnterpriseContacts,result={}",result);
    }
}
