//package com.facishare.open.ding.cloud.service.impl.connector;
//
//import com.alibaba.fastjson.JSONObject;
//import com.facishare.open.ding.api.arg.CrmProductCategoryArg;
//import com.facishare.open.ding.api.constants.CrmObjectApiName;
//import com.facishare.open.ding.api.enums.ConnectorErrorCodeEnum;
//import com.facishare.open.ding.api.model.connector.*;
//import com.facishare.open.ding.api.result.*;
//import com.facishare.open.ding.api.service.DingCorpMappingService;
//import com.facishare.open.ding.api.service.DingMappingEmployeeService;
//import com.facishare.open.ding.api.service.cloud.connector.ConnectorObjectDataCacheService;
//import com.facishare.open.ding.api.service.cloud.connector.ConnectorSyncObjectService;
//import com.facishare.open.ding.api.vo.DingCorpMappingVo;
//import com.facishare.open.ding.api.vo.ObjectDataCacheVo;
//import com.facishare.open.ding.cloud.manager.NCrmCloudManager;
//import com.facishare.open.ding.cloud.paas.support.CrmObjectSupportManager;
//import com.facishare.open.ding.common.result.Result;
//import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
//import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
//import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
//import com.google.common.collect.Lists;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.MapUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.*;
//
//@Service("connectorSyncObjectServiceImpl")
//@Slf4j
//public class ConnectorSyncObjectServiceImpl implements ConnectorSyncObjectService {
//    @Autowired
//    private NCrmCloudManager nCrmCloudManager;
//    @Autowired
//    private ConnectorObjectDataCacheService connectorObjectDataCacheService;
//    @Autowired
//    private CrmObjectSupportManager crmObjectSupportManager;
//    @Autowired
//    private DingCorpMappingService dingCorpMappingService;
//    @Autowired
//    private DingMappingEmployeeService dingMappingEmployeeService;
//
//    private static final String DING_PRODUCT_CATEGORY_CODE = "ding_product_category_code";
//    private static final String DING_PRODUCT_CATEGORY_NAME = "钉钉默认产品分类";
//
//    private ConnectorResult syncObject(String corpId,int ei,Long appId,String jsonData,String objectApiName,String code,Map<String,Object> dataMap) {
//        log.info("ConnectorSyncObjectServiceImpl.syncObject,corpId={},ei={},appId={},jsonData={},objectApiName={},code={},dataMap={}",
//                corpId,ei,appId,jsonData,objectApiName,code,dataMap);
//        if(CrmObjectApiName.CUSTOMER_CATEGORY_API_NAME.equalsIgnoreCase(objectApiName)
//                || CrmObjectApiName.PRODUCT_BRAND_API_NAME.equalsIgnoreCase(objectApiName)) {
//            if(!crmObjectSupportManager.isObjCreate(ei,objectApiName)) {
//                if(!crmObjectSupportManager.createDefineObject(ei,objectApiName)) {
//                    log.info("ConnectorSyncObjectServiceImpl.syncObject, create define object = {} failed,corpId={}",objectApiName,corpId);
//                    return new ConnectorResult(ConnectorErrorCodeEnum.BUSINESS_ERROR,"初始化预置对象"+objectApiName+"失败");
//                }
//            }
//        }
//
//        ObjectDataCacheVo entity = save2db(corpId,
//                ei,
//                appId,
//                objectApiName,
//                code,
//                jsonData,
//                1);
//
//        if(StringUtils.isEmpty(entity.getObjectDataId())) {
//            CrmResponseResult<String> crmResponseResult = nCrmCloudManager.createV2ObjectRest(ei,
//                    objectApiName,
//                    -10000,dataMap);
//
//            log.info("ConnectorSyncObjectServiceImpl.syncObject,create obj, crmResponseResult={}",crmResponseResult);
//            if(!crmResponseResult.isSuccess()) {
//                entity.setSynced(true);
//                entity.setSyncFailed(true);
//                entity.setFailedReason("errCode="+crmResponseResult.getErrorCode()+",errMsg="+crmResponseResult.getErrorMessage());
//                connectorObjectDataCacheService.update(entity);
//                log.info("ConnectorSyncObjectServiceImpl.syncObject,crmResponseResult={}",crmResponseResult);
//                return new ConnectorResult(ConnectorErrorCodeEnum.BUSINESS_ERROR,crmResponseResult.getErrorMessage());
//            }
//
//            String object_data_id = crmResponseResult.getData();
//            entity.setObjectDataId(object_data_id);
//            entity.setSynced(true);
//            entity.setSyncFailed(false);
//            entity.setFailedReason(null);
//        } else {
//            Map<String,Object> objectDataMap = (Map<String,Object>)dataMap.get("object_data");
//            objectDataMap.put("_id",entity.getObjectDataId());
//
//            CrmResponseResult<Void> crmResponseResult = nCrmCloudManager.updateV2Object(ei,
//                    objectApiName,
//                    -10000,dataMap);
//            log.info("ConnectorSyncObjectServiceImpl.syncObject,update obj, crmResponseResult={}",crmResponseResult);
//            if(!crmResponseResult.isSuccess()) {
//                entity.setSynced(true);
//                entity.setSyncFailed(true);
//                entity.setFailedReason("errCode="+crmResponseResult.getErrorCode()+",errMsg="+crmResponseResult.getErrorMessage());
//                connectorObjectDataCacheService.update(entity);
//                return new ConnectorResult(ConnectorErrorCodeEnum.BUSINESS_ERROR,crmResponseResult.getErrorMessage());
//            }
//            entity.setSynced(true);
//            entity.setSyncFailed(false);
//            entity.setFailedReason(null);
//        }
//
//        connectorObjectDataCacheService.update(entity);
//        log.info("ConnectorSyncObjectServiceImpl.syncObject,sync obj success,objectApiName={},code={}",objectApiName,code);
//        return new ConnectorResult();
//    }
//
//    @Override
//    public ConnectorResult syncCustomer(String corpId, int ei, long appId, String jsonData) {
//        log.info("ConnectorSyncObjectServiceImpl.syncCustomer,corpId={},ei={},appId={},jsonData={}",
//                corpId,ei,appId,jsonData);
//        CustomerModel model = JSONObject.parseObject(jsonData,CustomerModel.class);
//
//        Map<String,Object> objectDataMap = new HashMap<>();
//        objectDataMap.put("name",model.getData().getName());
//        objectDataMap.put("account_no",model.getData().getCode());
//
//        DingMappingEmployeeResult employeeResult = dingMappingEmployeeService.findMappingByDingUserId(ei,
//                model.getData().getEmpCode());
//        String ownerId = "-10000";
//        if(employeeResult!=null) {
//            ownerId = employeeResult.getEmployeeId()+"";
//        }
//
//        objectDataMap.put("owner", Lists.newArrayList(ownerId));
//        if(model.getData().getAddress()!=null) {
//            objectDataMap.put("address",model.getData().getAddress().getFullAddress());
//        }
//
//        objectDataMap.put("remark",model.getData().getRemark());
//
//        Map<String,Object> dataMap = new HashMap<>();
//        dataMap.put("object_data",objectDataMap);
//
//        ConnectorResult result = syncObject(corpId,
//                ei,
//                appId,
//                jsonData,
//                CrmObjectApiName.ACCOUNT_API_NAME,
//                model.getData().getCode(),dataMap);
//        if(result.getErrorCodeEnum()!=ConnectorErrorCodeEnum.SUCCESS) return result;
//
//        ObjectDataCacheVo objectDataCacheVo = connectorObjectDataCacheService.queryOneData(corpId,
//                appId,
//                CrmObjectApiName.ACCOUNT_API_NAME,
//                model.getData().getCode());
//
//        return syncContact(ei,objectDataCacheVo.getObjectDataId(),ownerId,model.getData().getContactPersonList());
//    }
//
//    private ConnectorResult syncContact(int ei, String customerId,String ownerId, List<CustomerModel.ContactPerson> contactPersonList) {
//        CrmResponseResult<ErpObjListData> contactListResult = nCrmCloudManager.queryContactByAccountId(ei, customerId);
//        List<Map<String,Object>> contactList = contactListResult.getData().getDataList();
//        for(CustomerModel.ContactPerson contactPerson : contactPersonList) {
//            Map<String,Object> contactMap = new HashMap<>();
//            contactMap.put("name",contactPerson.getName());
//            contactMap.put("account_id",customerId);
//            contactMap.put("owner",Lists.newArrayList(ownerId));
//            contactMap.put("add",contactPerson.getAddress().getFullAddress());
//            if(contactPerson.getContactTypeList()!=null) {
//                for(CustomerModel.ContactType contactType : contactPerson.getContactTypeList()) {
//                    if(StringUtils.equalsIgnoreCase(contactType.getType(),"mobile")) {
//                        contactMap.put("mobile",contactType.getValue());
//                    }
//                    if(StringUtils.equalsIgnoreCase(contactType.getType(),"phone")) {
//                        contactMap.put("tel",contactType.getValue());
//                    }
//                    if(StringUtils.equalsIgnoreCase(contactType.getType(),"email")) {
//                        contactMap.put("email",contactType.getValue());
//                    }
//                }
//            }
//
//            Map<String,Object> contactDataMap = new HashMap<>();
//            contactDataMap.put("object_data",contactMap);
//            String contactId = isContactExist(contactPerson.getName(),contactList);
//
//            log.info("ConnectorSyncObjectServiceImpl.syncContact,contactDataMap={}", contactDataMap);
//            if(StringUtils.isEmpty(contactId)) {
//                CrmResponseResult<String> crmResponseResult = nCrmCloudManager.createV2ObjectRest(ei,
//                        CrmObjectApiName.CONTACTS_API_NAME,
//                        -10000,contactDataMap);
//                log.info("ConnectorSyncObjectServiceImpl.createContact,create,crmResponseResult={}",crmResponseResult);
//                if(!crmResponseResult.isSuccess())
//                    return new ConnectorResult(ConnectorErrorCodeEnum.BUSINESS_ERROR,crmResponseResult.getErrorMessage());
//            } else {
//                contactMap.put("_id",contactId);
//                CrmResponseResult<Void> crmResponseResult = nCrmCloudManager.updateV2Object(ei,
//                        CrmObjectApiName.CONTACTS_API_NAME,
//                        -10000, contactDataMap);
//                log.info("ConnectorSyncObjectServiceImpl.createContact,update,crmResponseResult={}",crmResponseResult);
//                if(!crmResponseResult.isSuccess())
//                    return new ConnectorResult(ConnectorErrorCodeEnum.BUSINESS_ERROR,crmResponseResult.getErrorMessage());
//            }
//        }
//        return new ConnectorResult();
//    }
//
//    private String isContactExist(String name,List<Map<String,Object>> contactList) {
//        for(Map<String,Object> contactMap : contactList) {
//            if(StringUtils.equalsIgnoreCase(name,contactMap.get("name").toString())) return contactMap.get("_id").toString();
//        }
//        return null;
//    }
//
//    @Override
//    public ConnectorResult syncCustomerCategory(String corpId,int ei,long appId,String jsonData) {
//        CustomerCategoryModel model = JSONObject.parseObject(jsonData,CustomerCategoryModel.class);
//
//
//        Map<String,Object> objectDataMap = new HashMap<>();
//        objectDataMap.put("owner", Lists.newArrayList("-10000"));
//        objectDataMap.put("name",model.getData().getName());
//        objectDataMap.put("category_code",model.getData().getCode());
//        objectDataMap.put("parent_category_code",model.getData().getParentCode());
//        objectDataMap.put("remark",model.getData().getRemark());
//
//        Map<String,Object> dataMap = new HashMap<>();
//        dataMap.put("object_data",objectDataMap);
//
//        return syncObject(corpId,
//                ei,
//                appId,
//                jsonData,
//                CrmObjectApiName.CUSTOMER_CATEGORY_API_NAME,
//                model.getData().getCode(),dataMap);
//    }
//
//    @Override
//    public ConnectorResult syncProduct(String corpId,int ei,long appId,String jsonData) {
//        log.info("ConnectorSyncObjectServiceImpl.syncProduct,corpId={},ei={},appId={},jsonData={}",
//                corpId,ei,appId,jsonData);
//        ProductModel productModel = JSONObject.parseObject(jsonData,ProductModel.class);
//        Map<String,Object> objectDataMap = new HashMap<>();
//        objectDataMap.put("owner", Lists.newArrayList("-10000"));
//        objectDataMap.put("product_status","1");
//        objectDataMap.put("name",productModel.getData().getName());
//        objectDataMap.put("product_code",productModel.getData().getCode());
//
//        String category = null;
//        Result<DingCorpMappingVo> dingCorpMappingVoResult = dingCorpMappingService.queryMappingByAppId(corpId,appId);
//        DingCorpMappingVo dingCorpMappingVo = dingCorpMappingVoResult.getData();
//
//        if(dingCorpMappingVoResult!=null && StringUtils.isNotEmpty(dingCorpMappingVo.getCategory())) {
//            category = dingCorpMappingVoResult.getData().getCategory();
//        } else {
//            CrmProductCategoryArg arg = new CrmProductCategoryArg();
//            arg.setCategory_code(DING_PRODUCT_CATEGORY_CODE);
//            arg.setName(DING_PRODUCT_CATEGORY_NAME);
//            arg.setOrder_field("0");
//            CrmResponseResult<CrmProductCategoryResult> result = nCrmCloudManager.createProductCategoryRest(ei,arg);
//            if(result.isSuccess()) {
//                category = result.getData().getCode();
//                dingCorpMappingVo.setCategory(category);
//                dingCorpMappingService.update(dingCorpMappingVo);
//            }
//        }
//
//        objectDataMap.put("category",category);
//        String unitId = getUnitId(ei,productModel.getData().getBaseUnitCode());
//        if(StringUtils.isEmpty(unitId)) {
//            log.info("ConnectorSyncObjectServiceImpl.syncProduct,unit={} 在CRM中不存在",productModel.getData().getBaseUnitCode());
//            return new ConnectorResult(ConnectorErrorCodeEnum.BUSINESS_ERROR,
//                    "单位="+productModel.getData().getBaseUnitCode()+"在CRM中不存在，请先在CRM上创建相同的单位");
//        }
//        objectDataMap.put("unit",unitId);
//
//        String price = "0.0";
//        if(productModel.getData().getPriceList()!= null) {
//            for(ProductModel.PriceModel priceModel : productModel.getData().getPriceList()) {
//                if(priceModel.getUnitCode().equalsIgnoreCase(productModel.getData().getBaseUnitCode())) {
//                    price = priceModel.getRetailPrice();
//                    break;
//                }
//            }
//        }
//
//        objectDataMap.put("price",price);
//
//        Map<String,Object> dataMap = new HashMap<>();
//        dataMap.put("object_data",objectDataMap);
//
//        return syncObject(corpId,
//                ei,
//                appId,
//                jsonData,
//                CrmObjectApiName.PRODUCT_API_NAME,
//                productModel.getData().getCode(),dataMap);
//    }
//
//    @Override
//    public ConnectorResult syncProductBrand(String corpId,int ei,long appId,String jsonData) {
//        ProductBrandModel model = JSONObject.parseObject(jsonData,ProductBrandModel.class);
//        Map<String,Object> objectDataMap = new HashMap<>();
//        objectDataMap.put("owner", Lists.newArrayList("-10000"));
//        objectDataMap.put("name",model.getData().getName());
//        objectDataMap.put("brand_code",model.getData().getCode());
//        objectDataMap.put("parent_brand_code",model.getData().getParentCode());
//        objectDataMap.put("remark",model.getData().getRemark());
//
//        Map<String,Object> dataMap = new HashMap<>();
//        dataMap.put("object_data",objectDataMap);
//
//        return syncObject(corpId,
//                ei,
//                appId,
//                jsonData,
//                CrmObjectApiName.PRODUCT_BRAND_API_NAME,
//                model.getData().getCode(),dataMap);
//    }
//
//    @Override
//    public ConnectorResult syncWarehouse(String corpId,int ei,long appId,String jsonData) {
//        WarehouseModel warehouseModel = JSONObject.parseObject(jsonData,WarehouseModel.class);
//
//        Map<String,Object> objectDataMap = new HashMap<>();
//        objectDataMap.put("owner", Lists.newArrayList("-10000"));
//        objectDataMap.put("name",warehouseModel.getData().getName());
//        objectDataMap.put("number",warehouseModel.getData().getCode());
//        objectDataMap.put("remark",warehouseModel.getData().getRemark());
//        objectDataMap.put("is_default",false);
//        objectDataMap.put("is_enable","1");
//
//        Map<String,Object> dataMap = new HashMap<>();
//        dataMap.put("object_data",objectDataMap);
//
//        return syncObject(corpId,
//                ei,
//                appId,
//                jsonData,
//                CrmObjectApiName.WAREHOUSE_API_NAME,
//                warehouseModel.getData().getCode(),dataMap);
//    }
//
//    @Override
//    public ConnectorResult syncSalesOrder(String corpId, int ei, long appId, String jsonData) {
//        log.info("ConnectorSyncObjectServiceImpl.syncSalesOrder,corpId={},ei={},appId={},jsonData={}",
//                corpId,ei,appId,jsonData);
//
//        SalesOrderModel model = JSONObject.parseObject(jsonData,SalesOrderModel.class);
//
//        Map<String,Object> objectDataMap = new HashMap<>();
//        objectDataMap.put("name",model.getData().getCode());
//        objectDataMap.put("order_time",model.getData().getBillDate());
//        objectDataMap.put("order_amount",model.getData().getAmount());
//        objectDataMap.put("remark",model.getData().getRemark());
//
//        DingMappingEmployeeResult employeeResult = dingMappingEmployeeService.findMappingByDingUserId(ei,model.getData().getEmpCode());
//        log.info("ConnectorSyncObjectServiceImpl.syncSalesOrder,employeeResult={}",employeeResult);
//        if(employeeResult==null) {
//            employeeResult = dingMappingEmployeeService.findMappingByEmployeeId(ei,1000);
//        }
//
//        objectDataMap.put("owner", Lists.newArrayList(employeeResult.getEmployeeId()+""));
//
//        ObjectDataCacheVo objectDataCacheVo = connectorObjectDataCacheService.queryOneData(corpId,
//                appId,
//                CrmObjectApiName.ACCOUNT_API_NAME,
//                model.getData().getCustomerCode());
//        //如果客户在中间表中不存在，直接报错，可能这个客户不是从CRM同步到ERP的
//        if(objectDataCacheVo==null)
//            return new ConnectorResult(ConnectorErrorCodeEnum.BUSINESS_ERROR,
//                    "销售订单关联的客户编码="+model.getData().getCustomerCode()
//                            +",客户名称="+model.getData().getCustomerName()+"在中间表中不存在，请先同步关联的客户");
//        objectDataMap.put("account_id",objectDataCacheVo.getObjectDataId());
//
//        List<Map<String,Object>> orderProductList = new ArrayList<>();
//
//        for(SalesOrderModel.ProductModel productModel : model.getData().getProductList()) {
//            Map<String,Object> orderProductMap = new HashMap<>();
//
//            ObjectDataCacheVo productCacheVo = connectorObjectDataCacheService.queryOneData(corpId,
//                    appId,
//                    CrmObjectApiName.PRODUCT_API_NAME,
//                    productModel.getProductCode());
//
//            log.info("ConnectorSyncObjectServiceImpl.syncSalesOrder,productCacheVo={}",productCacheVo);
//            if(productCacheVo==null || StringUtils.isEmpty(productCacheVo.getObjectDataId())) {
//                return new ConnectorResult(ConnectorErrorCodeEnum.BUSINESS_ERROR,
//                        "销售订单关联的产品编码="+productModel.getProductCode() +"在中间表中不存在，请先同步关联的产品");
//            }
//
//            orderProductMap.put("product_id",productCacheVo.getObjectDataId());
//            orderProductMap.put("owner", Lists.newArrayList(employeeResult.getEmployeeId()+""));
//            orderProductMap.put("name",productModel.getProductCode());
//
//            //订单产品销售价格不能为空
//            //if(StringUtils.isEmpty(productModel.getTaxPrice())) return -1;
//            orderProductMap.put("sales_price",productModel.getTaxPrice());
//
//            //订单产品价格不能为空
//            //if(StringUtils.isEmpty(productModel.getPrice())) return -1;
//            orderProductMap.put("product_price",productModel.getPrice());
//
//            orderProductMap.put("quantity",productModel.getQuantity());
//
//            if(StringUtils.isNotEmpty(productModel.getUnitCode())) {
//                orderProductMap.put("unit",productModel.getUnitCode());
//            }
//
//            orderProductMap.put("is_multiple_unit","false");
//
//            //订单产品小计不能为空
//            //if(StringUtils.isEmpty(productModel.getAmount())) return -1;
//            orderProductMap.put("subtotal",productModel.getAmount());
//
//            String discount = "100";
//            if(StringUtils.isNotEmpty(productModel.getDiscountRate())) {
//                discount = productModel.getDiscountRate().replace("%","");
//            }
//            orderProductMap.put("discount",discount);
//
//            orderProductList.add(orderProductMap);
//        }
//
//        Map<String, List<Map<String,Object>>> detailsMap = new HashMap<>();
//        detailsMap.put(CrmObjectApiName.SALES_ORDER_PRODUCT_API_NAME,orderProductList);
//
//        Map<String,Object> dataMap = new HashMap<>();
//        dataMap.put("object_data",objectDataMap);
//        dataMap.put("details",detailsMap);
//
//        return syncObject(corpId,
//                ei,
//                appId,
//                jsonData,
//                CrmObjectApiName.SALES_ORDER_API_NAME,
//                model.getData().getCode(),dataMap);
//    }
//
//    @Override
//    public ConnectorResult syncReceipt(String corpId,int ei,long appId,String jsonData) {
//        ReceiptModel model = JSONObject.parseObject(jsonData,ReceiptModel.class);
//
//        Map<String,Object> objectDataMap = new HashMap<>();
//
//        DingMappingEmployeeResult dingMappingEmployeeResult = dingMappingEmployeeService.findMappingByDingUserId(ei,model.getData().getEmpCode());
//        log.info("ConnectorSyncObjectServiceImpl.syncReceipt,dingMappingEmployeeResult={}",dingMappingEmployeeResult);
//        if(dingMappingEmployeeResult==null) {
//            dingMappingEmployeeResult = dingMappingEmployeeService.findMappingByEmployeeId(ei,1000);
//        }
//
//
//        objectDataMap.put("owner", Lists.newArrayList(dingMappingEmployeeResult.getEmployeeId()+""));
//
//        ObjectDataCacheVo objectDataCacheVo = connectorObjectDataCacheService.queryOneData(corpId,
//                appId,
//                CrmObjectApiName.ACCOUNT_API_NAME,
//                model.getData().getCustomerCode());
//        //如果客户在中间表中不存在，直接报错，可能这个客户不是从CRM同步到ERP的
//        if(objectDataCacheVo==null)
//            return new ConnectorResult(ConnectorErrorCodeEnum.BUSINESS_ERROR,
//                    "销售订单关联的客户编码="+model.getData().getCustomerCode()
//                            +",客户名称="+model.getData().getCustomerName()+"在中间表中不存在，请先同步关联的客户");
//
//        objectDataMap.put("account_id",objectDataCacheVo.getObjectDataId());
//        objectDataMap.put("payment_time",model.getData().getBillDate());
//
//
//        List<Map<String,Object>> orderPaymentList = new ArrayList<>();
//
//        for(ReceiptModel.OrderSettlementModel orderSettlementModel : model.getData().getOrderSettlementList()) {
//            Map<String,Object> orderPaymentMap = new HashMap<>();
//            orderPaymentMap.put("owner", Lists.newArrayList(dingMappingEmployeeResult.getEmployeeId()+""));
//
//            ObjectDataCacheVo salesOrderCacheVo = connectorObjectDataCacheService.queryOneData(corpId,
//                    appId,
//                    CrmObjectApiName.SALES_ORDER_API_NAME,
//                    orderSettlementModel.getOrderCode());
//
//            log.info("ConnectorSyncObjectServiceImpl.syncReceipt,salesOrderCacheVo={}",salesOrderCacheVo);
//            if(salesOrderCacheVo==null || StringUtils.isEmpty(salesOrderCacheVo.getObjectDataId())) {
//                return new ConnectorResult(ConnectorErrorCodeEnum.BUSINESS_ERROR,
//                        "收款单关联的销售订单编码="+orderSettlementModel.getOrderCode() +"在中间表中不存在，请先同步关联的销售订单");
//            }
//
//            orderPaymentMap.put("order_id",salesOrderCacheVo.getObjectDataId());
//            orderPaymentMap.put("payment_amount",orderSettlementModel.getSettlementAmount());
//            orderPaymentList.add(orderPaymentMap);
//        }
//
//        Map<String, List<Map<String,Object>>> detailsMap = new HashMap<>();
//        detailsMap.put(CrmObjectApiName.ORDER_PAYMENT_API_NAME,orderPaymentList);
//
//        Map<String,Object> dataMap = new HashMap<>();
//        dataMap.put("object_data",objectDataMap);
//        dataMap.put("details",detailsMap);
//
//        return syncObject(corpId,
//                ei,
//                appId,
//                jsonData,
//                CrmObjectApiName.PAYMENT_API_NAME,
//                model.getData().getCode(),dataMap);
//    }
//
//    @Override
//    public ConnectorResult syncOpportunity(String corpId, int ei, long appId, String jsonData) {
//        return new ConnectorResult();
//    }
//
//    private ObjectDataCacheVo save2db(String corpId, int ei, Long appId, String objectApiName, String dataCode, String jsonData, int syncDirection) {
//        ObjectDataCacheVo entity = connectorObjectDataCacheService.queryOneData(corpId,appId,objectApiName,dataCode);
//        if(entity==null) {
//            entity = new ObjectDataCacheVo();
//            entity.setId(UUID.randomUUID().toString());
//            entity.setCorpId(corpId);
//            entity.setEi(ei);
//            entity.setAppId(appId);
//            entity.setObjectApiName(objectApiName);
//            entity.setDataCode(dataCode);
//            entity.setJsonData(jsonData);
//            entity.setSyncDirection(syncDirection);
//            entity.setSynced(false);
//            entity.setSyncFailed(false);
//            entity.setFailedReason(null);
//
//            int count = connectorObjectDataCacheService.insert(entity);
//            log.info("ConnectorSyncObjectServiceImpl.save2db,insert db,count={}",count);
//        } else {
//            entity.setJsonData(jsonData);
//            entity.setSynced(false);
//            entity.setSyncFailed(false);
//            entity.setFailedReason(null);
//            int count = connectorObjectDataCacheService.update(entity);
//            log.info("ConnectorSyncObjectServiceImpl.save2db,update db,count={}",count);
//        }
//        return entity;
//    }
//
//    public String getUnitId(int ei,String unit) {
//        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResultResult
//                = crmObjectSupportManager.getDescribe(ei,CrmObjectApiName.PRODUCT_API_NAME);
//        if(describeResultResult.isSuccess()) {
//            ObjectDescribe objectDescribe = describeResultResult.getData().getDescribe();
//            for(String key : objectDescribe.getFields().keySet()) {
//                if(key.equalsIgnoreCase("unit")) {
//                    FieldDescribe unitFieldDescribe = objectDescribe.getFields().get(key);
//                    for(Map<String,Object> map : unitFieldDescribe.getOptions()) {
//                        String label = MapUtils.getString(map,"label");
//                        if(label.equalsIgnoreCase(unit)) {
//                            return MapUtils.getString(map,"value");
//                        }
//                    }
//                }
//            }
//        }
//        return null;
//    }
//}
