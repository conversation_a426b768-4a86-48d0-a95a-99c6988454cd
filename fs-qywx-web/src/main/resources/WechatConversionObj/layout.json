{"api_name": "layout_WechatConversionObj_default", "display_name": "默认布局", "is_default": true, "is_deleted": false, "components": [{"field_section": [{"form_fields": [{"field_name": "name", "render_type": "auto_number", "is_required": true, "is_readonly": false}, {"field_name": "fs_ea", "render_type": "text", "is_required": true, "is_readonly": false}, {"field_name": "msg_id", "render_type": "text", "is_required": true, "is_readonly": false}, {"field_name": "seq", "render_type": "number", "is_required": true, "is_readonly": false}, {"field_name": "key_version", "render_type": "number", "is_required": true, "is_readonly": false}, {"field_name": "from_user_cipher", "render_type": "text", "is_required": true, "is_readonly": false}, {"field_name": "from_user", "render_type": "text", "is_required": true, "is_readonly": false}, {"field_name": "to_list_cipher", "render_type": "long_text", "is_required": true, "is_readonly": false}, {"field_name": "to_list", "render_type": "long_text", "is_required": true, "is_readonly": false}, {"field_name": "room_id", "render_type": "text", "is_required": false, "is_readonly": false}, {"field_name": "msg_time", "render_type": "number", "is_required": true, "is_readonly": false}, {"field_name": "msg_type", "render_type": "text", "is_required": true, "is_readonly": false}, {"field_name": "content", "render_type": "long_text", "is_required": false, "is_readonly": false}, {"field_name": "md5sum", "render_type": "text", "is_required": false, "is_readonly": false}, {"field_name": "sdk_file_id", "render_type": "text", "is_required": false, "is_readonly": false}, {"field_name": "file_size", "render_type": "number", "is_required": false, "is_readonly": false}, {"field_name": "image", "render_type": "image", "is_required": false, "is_readonly": false}, {"field_name": "attachment", "render_type": "file_attachment", "is_required": false, "is_readonly": false}, {"field_name": "npath", "render_type": "text", "is_required": false, "is_readonly": false}, {"field_name": "file_name", "render_type": "text", "is_required": false, "is_readonly": false}, {"field_name": "file_ext", "render_type": "text", "is_required": false, "is_readonly": false}, {"field_name": "qywx_customer_id", "render_type": "object_refrence", "is_required": false, "is_readonly": false}, {"field_name": "qywx_group_id", "render_type": "object_refrence", "is_required": false, "is_readonly": false}, {"field_name": "owner", "render_type": "employee", "is_required": true, "is_readonly": false}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "is_show": true}, {"form_fields": [{"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}], "api_name": "group_f4v1D__c", "tab_index": "ltr", "column": 2, "header": "系统信息", "is_show": true}], "buttons": [], "api_name": "form_component", "related_list_name": "", "unDeletable": true, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "type": "form", "_id": "form_component", "order": 5}], "layout_type": "detail", "layout_description": "默认布局", "ref_object_api_name": "WechatConversionObj", "buttons": [{"action_type": "default", "api_name": "Add_button_default", "action": "Add", "label": "新建"}, {"action_type": "default", "api_name": "Edit_button_default", "action": "Edit", "label": "编辑"}, {"action_type": "default", "api_name": "Import_button_default", "action": "Import", "label": "导入"}, {"action_type": "default", "api_name": "Export_button_default", "action": "Export", "label": "导出"}, {"action_type": "default", "api_name": "Abolish_button_default", "action": "Abolish", "label": "作废"}, {"action_type": "default", "api_name": "Lock_button_default", "action": "Lock", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "action": "Unlock", "label": "解锁"}], "events": [], "default_component": "form_component", "package": "CRM", "api_version": 1.0}