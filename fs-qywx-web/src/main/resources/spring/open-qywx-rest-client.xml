<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


    <!-- 使用OkHttpSupport -->
    <bean id="okHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"/>
    <bean id="qywxApiHostProfile" class="com.facishare.dubbo.plugin.client.ServerHostProfile">
        <property name="configName" value="fs-open-qywx-app-config"/>
    </bean>

    <!--open api-->
    <bean class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService"/>
        <property name="serverHostProfile" ref="qywxApiHostProfile"/>
    </bean>


</beans>