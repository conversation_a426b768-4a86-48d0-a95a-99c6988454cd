<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <description>dubbo提供者接口</description>
    <dubbo:service interface="com.facishare.open.qywx.save.service.SaveMessageService"
                   version="1.0"
                   ref="saveMessageService"
                   timeout="300000"
                   group="${oaDubboConfigGroup}"
                   />

    <dubbo:service interface="com.facishare.open.qywx.save.service.MessageGeneratingService"
                   version="1.0"
                   ref="messageGeneratingService"
                   timeout="300000"
                   group="${oaDubboConfigGroup}"
                   />

    <dubbo:service interface="com.facishare.open.qywx.save.service.AutoPullMessageService"
                   version="1.0"
                   ref="autoPullMessageService"
                   timeout="300000"
                   group="${oaDubboConfigGroup}"
                   />
</beans>
