<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       default-lazy-init="true" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd">
    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor" c:placeholderConfigurer-ref="autoConf"/>

    <bean id="autoConf" class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:locations="classpath:application.properties"
          p:configName="fs-open-qywx-app-config,fs-open-qywx-authorized-ea,fs-webhook-provider,fs-webhook-provider-mongodb,fs-webhook-wechat-message,fs-open-qywx-mq-cms"/>
</beans>
