<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!--发送企业自建应用的secret -->
    <bean id="secretMessageSender" class="com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="QYWX_UPGRADE_NOTICE_EVENT_SECTION"/>
    </bean>

    <bean id="conversionSettingMessageSender" class="com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="QYWX-CONVERSION-SETTING-EVENT-SECTION"/>
    </bean>

</beans>