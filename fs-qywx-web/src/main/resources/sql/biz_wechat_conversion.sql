CREATE TABLE IF NOT EXISTS biz_wechat_conversion (
	id VARCHAR (32) NOT NULL,
	tenant_id VARCHAR (10) NOT NULL,
	name VARCHA<PERSON> (500),
	display_name VARCHAR (128),
	owner VARCHAR (100),
	lock_status VARCHAR (50),
	life_status VARCHAR (100) NOT NULL,
	record_type VARCHAR (100),
	created_by VARCHAR (32),
	create_time INT8,
	last_modified_by VARCHAR (32),
	last_modified_time INT8,
	extend_obj_data_id VARCHAR (24),
	package VARCHAR (50),
	object_describe_id VARCHAR (50),
	object_describe_api_name VARCHAR (100),
	version INT4,
	lock_user VARCHAR (100),
	lock_rule VARCHAR (100),
	life_status_before_invalid VARCHAR (100),
	is_deleted INT2,
	out_tenant_id VARCHAR (10),
	out_owner VARCHAR (100),
	data_own_department VARCHAR (32),
	data_own_organization VARCHAR (100),
	data_auth_code VARCHAR (64),
	change_type INT4,
	out_data_auth_code VA<PERSON>HAR (64),
	order_by INT4,
	data_auth_id INT4,
	out_data_auth_id INT4,
	dimension_d1 VARCHAR [],
	dimension_d2 VARCHAR [],
	dimension_d3 VARCHAR [],
	mc_currency VARCHAR (128),
	mc_exchange_rate NUMERIC,
	mc_functional_currency VARCHAR (128),
	mc_exchange_rate_version VARCHAR (64),
	origin_source VARCHAR (32),
	sys_modified_time INT8,
	
	fs_ea VARCHAR(10),
	msg_id VARCHAR(128),
	seq INT4,
	key_version INT4,
	from_user_cipher VARCHAR(100),
	from_user VARCHAR(100),
	to_list_cipher VARCHAR,
	to_list VARCHAR,
	room_id VARCHAR(100),
	msg_time INT8,
	msg_type VARCHAR(100),
	content VARCHAR,
	md5sum VARCHAR(100),
	sdk_file_id VARCHAR(100),
	file_size INT8,
	image VARCHAR(15000),
	attachment VARCHAR(15000),
	npath VARCHAR(100),
	file_name VARCHAR(1000),
	file_ext VARCHAR(32),
	qywx_customer_id VARCHAR(32),
	qywx_group_id VARCHAR(32),
PRIMARY KEY (id, tenant_id));

CREATE INDEX IF NOT EXISTS biz_wechat_conversion_ei_name_idx ON biz_wechat_conversion USING btree (tenant_id, name, is_deleted, object_describe_api_name);
CREATE INDEX IF NOT EXISTS biz_wechat_conversion_ei_dname_idx ON biz_wechat_conversion USING btree (tenant_id, display_name, is_deleted, object_describe_api_name);
CREATE INDEX IF NOT EXISTS biz_wechat_conversion_ei_extend_obj_data_id_idx ON biz_wechat_conversion USING btree (extend_obj_data_id, tenant_id);
CREATE INDEX IF NOT EXISTS biz_wechat_conversion_last_mtime_tenant_id_api_name_idx ON biz_wechat_conversion USING btree (last_modified_time desc, tenant_id, is_deleted, object_describe_api_name);
CREATE INDEX IF NOT EXISTS biz_wechat_conversion_ctime_tenant_idx on biz_wechat_conversion (create_time desc, tenant_id, is_deleted, object_describe_api_name);
CREATE INDEX IF NOT EXISTS biz_wechat_conversion_ei_owner_idx ON biz_wechat_conversion USING btree (tenant_id, owner, is_deleted, object_describe_api_name);
CREATE INDEX IF NOT EXISTS biz_wechat_conversion_ei_data_own_department_idx on biz_wechat_conversion (tenant_id, data_own_department, is_deleted, object_describe_api_name);
CREATE INDEX IF NOT EXISTS biz_wechat_conversion_data_auth_code_idx ON biz_wechat_conversion USING btree (data_auth_code);
CREATE INDEX IF NOT EXISTS biz_wechat_conversion_out_data_auth_code_idx ON biz_wechat_conversion USING btree (out_data_auth_code);
CREATE INDEX IF NOT EXISTS biz_wechat_conversion_last_mtime_out_tenant_id_idx ON biz_wechat_conversion USING btree (last_modified_time desc, out_tenant_id, is_deleted, tenant_id);
CREATE INDEX IF NOT EXISTS biz_wechat_conversion_sys_mtime_ei_idx ON biz_wechat_conversion USING btree (sys_modified_time desc, tenant_id, is_deleted, object_describe_api_name);


