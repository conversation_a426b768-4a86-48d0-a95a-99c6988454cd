package com.facishare.open.qywx.accountbind.arg;

import com.facishare.common.fsi.ProtoBase;
import io.protostuff.Tag;
import lombok.Data;

import java.io.Serializable;

@Data
public class QyweixinEmpRebindArg extends ProtoBase implements Serializable {
    /**
     * 纷享ea
     */
    @Tag(1)
    private String ea;
    /**
     * 企业微信的corpId
     */
    @Tag(2)
    private String corpId;
    /**
     * 纷享员工的账号
     */
    @Tag(3)
    private String fsUserId;
    /**
     * 换绑后企业微信员工的账号
     */
    @Tag(4)
    private String qwNewUserId;
    /**
     * 换绑前企业微信员工的账号
     */
    @Tag(5)
    private String qwOldUserId;
}
