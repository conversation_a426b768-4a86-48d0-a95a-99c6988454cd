package com.facishare.open.qywx.save.arg;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/25 11:29
 * @Version 1.0
 */
@Data
public class QywxQueryMessageArg implements Serializable {
    private String queryWord;
    private Long startTime;
    private Long endTime;
    private Integer limit;
    private String cursor;
    private ChatInfo chatInfo;
    @Data
    public static class ChatInfo implements Serializable {
        private Integer chatType;
        private List<IdInfo> idList;
        private String chatId;
        private List<String> msgTypeList;
        private IdInfo sender;
    }
    @Data
    public static class IdInfo implements Serializable {
        private String openUserid;
        private String externalUserid;

    }
}
