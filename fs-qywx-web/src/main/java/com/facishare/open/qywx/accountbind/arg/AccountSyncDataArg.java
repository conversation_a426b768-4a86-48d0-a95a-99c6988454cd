package com.facishare.open.qywx.accountbind.arg;

import com.facishare.common.fsi.ProtoBase;
import io.protostuff.Tag;
import lombok.Data;

import java.io.Serializable;

@Data
public class AccountSyncDataArg extends ProtoBase implements Serializable {
    /**
     * 对象
     */
    @Tag(1)
    private String apiName;
    /**
     * 纷享的ea
     */
    @Tag(2)
    private String ea;
    /**
     * 开启时间
     */
    @Tag(3)
    Long upTime;
    /**
     * 关闭时间
     */
    @Tag(4)
    Long downTime;
}
