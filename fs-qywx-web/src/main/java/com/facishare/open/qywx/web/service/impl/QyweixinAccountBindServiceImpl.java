package com.facishare.open.qywx.web.service.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.manager.DescManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.*;
import com.facishare.open.oa.base.dbproxy.pg.manager.*;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaDepartmentBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.admin.QywxConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.facishare.open.outer.oa.connector.common.api.params.QyweixinAppInfoParams;
import com.facishare.open.outer.oa.connector.common.api.result.SettingsResult;
import com.facishare.open.qywx.accountbind.arg.QyweixinEmpBindArg;
import com.facishare.open.qywx.accountbind.arg.QyweixinEnterpriseBindArg;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.web.arg.BatchGetEmpBindArg;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.manager.DataPersistorManager;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountDepartmentMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.web.mq.sender.MQSender;
import com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer;
import com.facishare.open.qywx.accountbind.result.EmployeeAccountMatchResult;
import com.facishare.open.qywx.accountbind.result.ErrorRefer;
import com.facishare.open.qywx.accountbind.result.Result;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountinner.model.OaconnectorEventDateChangeProto;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountinner.service.ToolsService;
import com.facishare.open.qywx.accountsync.core.enums.CloudProxyEnum;
import com.facishare.open.qywx.accountsync.model.CloudMessageProxyProto;
import com.fxiaoke.api.IdGenerator;
import com.facishare.open.qywx.web.result.accountsync.BatchEmpBindResult;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by liuwei on 2018/7/18.
 */
@Slf4j
@Service("qyweixinAccountBindService")
// IgnoreI18nFile
public class QyweixinAccountBindServiceImpl implements QyweixinAccountBindService {

//    @Autowired
//    private QyweixinAccountEmployeeBindDao accountEmployeeBindDao;
//
//    @Autowired
//    private QyweixinAccountEnterpriseBindDao accountEnterpriseBindDao;
//
//    @Autowired
//    private QyweixinAccountDepartmentBindDao accountDepartmentBindDao;

    @Autowired
    private DataPersistorManager dataPersistorManager;

    @Autowired
    private ToolsService toolsService;

    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;

    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;

    @Resource(name = "enterpriseAccountBindSender")
    private AutoConfRocketMQProducer enterpriseAccountBindSender;

    @Resource(name = "employeeAccountBindSender")
    private AutoConfRocketMQProducer employeeAccountBindSender;

    @Autowired
    private MQSender mqSender;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Resource
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaDepartmentBindManager outerOaDepartmentBindManager;
    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;
    @Autowired
    private DescManager descManager;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;

    @PostConstruct
    public void init() {
        String traceId = UUID.randomUUID().toString();
        log.info("QyweixinAccountBindServiceImpl.init,traceId={}", traceId);
        TraceUtil.initTrace(traceId);
    }

    @Override
    public Result<Boolean> bindAccountEmployeeMapping(List<OuterOaEmployeeBindEntity> arg) {
        if(arg == null || arg.isEmpty()){
            throw new IllegalArgumentException("exist null List");
        }

        int bindCount = outerOaEmployeeBindManager.batchBindEmployeeList(arg);
        if(bindCount > 0) {
            //发送员工绑定成功的mq
            log.info("QyweixinAccountBindServiceImpl.bindAccountEmployeeMapping,arg={}.", arg);
            for(OuterOaEmployeeBindEntity employeeMapping : arg) {
                //如果状态为100，未开通成功,状态为0是开通成功并绑定
                if(employeeMapping.getBindStatus().equals(BindStatusEnum.normal)) {
                    String ea = employeeMapping.getFsEa();
                    String fsUserId = employeeMapping.getFsEmpId();
                    Message message = new Message();
                    message.setTags(ConfigCenter.EMPLOYEE_BIND);
                    QyweixinEmpBindArg body = new QyweixinEmpBindArg();
                    body.setEa(ea);
                    body.setCorpId(employeeMapping.getOutEa());
                    body.setFsUserId(fsUserId);
                    body.setQwUserId(employeeMapping.getOutEmpId());
                    message.setBody(body.toProto());
                    if (ConfigCenter.TEM_CLOUD_EA.contains(ea)) {
                        CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
                        cloudMessageProxyProto.setType(CloudProxyEnum.employeeAccountBindSender.name());
                        cloudMessageProxyProto.setCorpId(employeeMapping.getOutEa());
                        cloudMessageProxyProto.setFsEa(ea);
                        cloudMessageProxyProto.setMessage(message);
                        // 跨云
                        mqSender.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(ea), cloudMessageProxyProto);
                    } else {
                        SendResult sendResult = employeeAccountBindSender.send(message, employeeMapping.getOutEa());
                        log.info("QyweixinAccountBindServiceImpl.bindAccountEmployeeMapping,sendResult={},message={}.",
                                sendResult, message);
                    }
                }
            }
        }
        return new Result<>(bindCount > 0);
    }

    @Override
    public Result<Boolean> bindAccountEnterpriseMapping(OuterOaEnterpriseBindEntity arg) {
        Gson gson = new Gson();

        //查询企业绑定信息
        int bindCount = 0;
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.qywx, arg.getFsEa(), arg.getOutEa(), arg.getAppId());
        log.info("QyweixinAccountBindServiceImpl.bindAccountEnterpriseMapping,enterpriseBindEntity={}.", enterpriseBindEntity);
        //更新
        if (ObjectUtils.isNotEmpty(enterpriseBindEntity)) {
            QywxConnectorVo qywxConnectorVo = JSON.parseObject(enterpriseBindEntity.getConnectInfo(), QywxConnectorVo.class);
            OuterOaAppInfoEntity appInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, arg.getOutEa(), arg.getAppId());
            if (ObjectUtils.isEmpty(appInfoEntity) || appInfoEntity.getStatus() == OuterOaAppInfoStatusEnum.stop) {
                return new Result<Boolean>(false).addError(ErrorRefer.BIND_ERROR.getCode());
            }
            QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(appInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);
            qywxConnectorVo.setQywxEnterpriseName(qyweixinAppInfoParams.getAuthCorpInfo().getCorpName());
            qywxConnectorVo.setAgentId(qyweixinAppInfoParams.getAuthAppInfo().getAgentId());
            arg.setConnectInfo(gson.toJson(qywxConnectorVo));
            bindCount = outerOaEnterpriseBindManager.updateById(arg);
            log.info("QyweixinAccountBindServiceImpl.bindAccountEnterpriseMapping,bindCount={}.", bindCount);
            return new Result<Boolean>(false).addError(ErrorRefer.BIND_ERROR.getCode());
        }
        String dcId = arg.getId();
        if (StringUtils.isEmpty(arg.getId())) {
            dcId = IdGenerator.get();
            arg.setId(dcId);
        }

        if(null == arg.getBindType()){
            arg.setBindType(BindTypeEnum.auto);
        }

        //是否有时间
        if (arg.getCreateTime() == null) {
            arg.setCreateTime(System.currentTimeMillis());
        }
        if (arg.getUpdateTime() == null) {
            arg.setUpdateTime(System.currentTimeMillis());
        }
        QywxConnectorVo qywxConnectorVo;
        if (StringUtils.isEmpty(arg.getConnectInfo())) {
            qywxConnectorVo = new QywxConnectorVo();
            qywxConnectorVo.setDataCenterId(dcId);
            qywxConnectorVo.setQywxCorpId(arg.getOutEa());
            qywxConnectorVo.setAppType(OuterOaAppInfoTypeEnum.isv);
            qywxConnectorVo.setConnectorName("企业微信连接器");  //ignoreI18n
            qywxConnectorVo.setDataCenterName("企业微信连接器"); //ignoreI18n
            qywxConnectorVo.setAuthType(AuthTypeEnum.OAUTH2.getCode());
            qywxConnectorVo.setAlertTypes(Arrays.asList(AlertTypeEnum.CRM_TODO, AlertTypeEnum.CRM_NOTIFICATION));
            qywxConnectorVo.setChannel(ChannelEnum.qywx);
            qywxConnectorVo.setDomain(ConfigCenter.crm_domain);
            qywxConnectorVo.setQywxDepartmentId("1");
            qywxConnectorVo.setIsFirstLand(Boolean.TRUE);
            qywxConnectorVo.setIsRetainInformation(Boolean.FALSE);
            qywxConnectorVo.setAlertConfig(true);
            //通过应用信息拿
            OuterOaAppInfoEntity appInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, arg.getOutEa(), arg.getAppId());
            if (ObjectUtils.isEmpty(appInfoEntity) || appInfoEntity.getStatus() == OuterOaAppInfoStatusEnum.stop) {
                return new Result<Boolean>(false).addError(ErrorRefer.BIND_ERROR.getCode());
            }
            QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(appInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);
            qywxConnectorVo.setQywxEnterpriseName(qyweixinAppInfoParams.getAuthCorpInfo().getCorpName());
            qywxConnectorVo.setAgentId(qyweixinAppInfoParams.getAuthAppInfo().getAgentId());
            arg.setConnectInfo(gson.toJson(qywxConnectorVo));
        } else {
            qywxConnectorVo = gson.fromJson(arg.getConnectInfo(), QywxConnectorVo.class);
            if (StringUtils.isEmpty(qywxConnectorVo.getDomain())) {
                qywxConnectorVo.setDomain(ConfigCenter.crm_domain);
            }
            if (StringUtils.isEmpty(qywxConnectorVo.getQywxDepartmentId())) {
                qywxConnectorVo.setQywxDepartmentId("1");
            }

            //通过应用信息拿
            OuterOaAppInfoEntity appInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, arg.getOutEa(), arg.getAppId());
            if (ObjectUtils.isEmpty(appInfoEntity) || appInfoEntity.getStatus() == OuterOaAppInfoStatusEnum.stop) {
                return new Result<Boolean>(false).addError(ErrorRefer.BIND_ERROR.getCode());
            }
            QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(appInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);
            qywxConnectorVo.setQywxEnterpriseName(qyweixinAppInfoParams.getAuthCorpInfo().getCorpName());
            qywxConnectorVo.setAgentId(qyweixinAppInfoParams.getAuthAppInfo().getAgentId());

            arg.setConnectInfo(gson.toJson(qywxConnectorVo));
        }

        //配置规则设置
        OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager
                .queryConnectorConfigInfoByDcId(dcId, OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES);
        if (ObjectUtils.isEmpty(configInfoEntity)) {
            SettingAccountRulesModel settingAccountRulesModel = new SettingAccountRulesModel();
//            settingAccountRulesModel.setCurrentDcId(dcId);
            settingAccountRulesModel.setSyncTypeEnum(EnterpriseConfigAccountSyncTypeEnum.accountSync);
            settingAccountRulesModel.setEmployeeLeaveRule(SettingAccountRulesModel.EmployeeLeaveRule.builder()
                    .unbind(Boolean.TRUE).stopEmp(Boolean.TRUE).build());
            settingAccountRulesModel.setEmployeeRangeRemoveRule(SettingAccountRulesModel.EmployeeRangeRemoveRule.builder()
                    .unbind(Boolean.TRUE).stopEmp(Boolean.TRUE).build());

            configInfoEntity = OuterOaConfigInfoEntity.builder()
                    .id(IdGenerator.get())
                    .channel(ChannelEnum.qywx)
                    .dcId(dcId)
                    .fsEa(arg.getFsEa())
                    .outEa(arg.getOutEa())
                    .appId(arg.getAppId())
                    .type(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES)
                    .configInfo(gson.toJson(settingAccountRulesModel))
                    .createTime(System.currentTimeMillis())
                    .updateTime(System.currentTimeMillis())
                    .build();
            Integer count = outerOaConfigInfoManager.insert(configInfoEntity);
            log.info("QyweixinAccountBindServiceImpl.bindAccountEnterpriseMapping,insert configInfoEntity={}.count={}.", configInfoEntity, count);
        }


        bindCount = outerOaEnterpriseBindManager.insert(arg);

        if(bindCount >=1) {
            //埋点统计企业绑定数量
            dataPersistorManager.enterpriseAccountBind(arg);
            //如果状态为100，未开通成功,状态为0是开通成功并绑定
            if(arg.getBindStatus().equals(BindStatusEnum.normal)) {
                //绑定企业成发送mq
                log.info("QyweixinAccountBindServiceImpl.bindAccountEnterpriseMapping,arg={}.", arg);
                Message message = new Message();
                QyweixinEnterpriseBindArg body = new QyweixinEnterpriseBindArg();
                body.setEa(arg.getFsEa());
                body.setCorpId(arg.getOutEa());
                body.setAppId(arg.getAppId());
                message.setBody(body.toProto());
                if (ConfigCenter.TEM_CLOUD_EA.contains(arg.getFsEa())) {
                    CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
                    cloudMessageProxyProto.setType(CloudProxyEnum.enterpriseAccountBindSender.name());
                    cloudMessageProxyProto.setCorpId(arg.getOutEa());
                    cloudMessageProxyProto.setFsEa(arg.getFsEa());
                    cloudMessageProxyProto.setMessage(message);
                    // 跨云
                    mqSender.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(arg.getFsEa()),
                            cloudMessageProxyProto);
                } else {
                    //不是这个环境的企业
                    if(ConfigCenter.crm_domain.equals(qywxConnectorVo.getDomain())) {
                        SendResult sendResult = enterpriseAccountBindSender.send(message, arg.getOutEa());
                        log.info(
                                "QyweixinAccountBindServiceImpl.bindAccountEnterpriseMapping,sendResult={},message={}.",
                                sendResult, message);
                    }
                }

                if (!ConfigCenter.MAIN_ENV) {
                    log.info("QyweixinAccountBindServiceImpl.bindAccountEnterpriseMapping,MAIN_ENV={}.",
                            ConfigCenter.MAIN_ENV);
                    OaconnectorEventDateChangeProto proto = new OaconnectorEventDateChangeProto();
                    proto.setOutEa(arg.getOutEa());
                    proto.setEventType("oaconnector_enterprise_bind");
                    proto.setType("insert");
                    proto.setFsEa(arg.getFsEa());
                    proto.setDomain(ConfigCenter.crm_domain);
                    proto.setContent(new Gson().toJson(arg));
                    mqSender.sendOaconnectorEventDataChangeMQ(ChannelEnum.qywx.name(), proto,
                            String.valueOf(eieaConverter.enterpriseAccountToId(arg.getFsEa())));
                }
            }
            return new Result<>();
        } else {
            return new Result<Boolean>(false).addError(ErrorRefer.BIND_ERROR.getCode());
        }
    }

//    /**
//     * 绑定企业下的部门,
//     *
//     * @param arg : 请参考 AccountDepartmentMapping的注释说明
//     * @return :true-绑定关系保存成功，false-保存失败。
//     **/
//    @Override
//    public Result<Boolean> bindAccountDepartmentMapping(List<QyweixinAccountDepartmentMapping> arg) {
//        arg.stream().forEach(m->{
//            if(!m.checkArg()) {
//                throw new IllegalArgumentException("exist null parameter");
//            }
//            String mainAppId = qyweixinGatewayInnerService.getMainAppId(m.getOutEa()).getData();
//            m.setAppId(StringUtils.defaultString(m.getAppId(), mainAppId));
//        });
//
//        int bindCount = accountDepartmentBindDao.bindAccountEnterpriseMapping(arg);
//        return new Result<>(bindCount > 0);
//    }

//    /**
//     *
//     * @param source: "qywx"是企业微信
//     * @param fsEnterpriseAccount: 纷享企业账号
//     * @param outAccountList
//     * @return 内部账号。 格式E.xx.yyy
//     */
//    @Override
//    public Result<Map<String, String>> outAccountToFsAccountBatch(String source, String fsEnterpriseAccount, List<String> outAccountList) {
//        return outAccountToFsAccountBatch(source, fsEnterpriseAccount, ConfigCenter.crmAppId, outAccountList);
//    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> outAccountToFsAccount(String source, String fsEnterpriseAccount, String appId, String outAccount) {
        //检查是否绑定了
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEnterpriseAccount).appId(appId).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEnterpriseAccount).build());
        }

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result().addError("未查到企业绑定信息");  //ignoreI18n
        }

        List<QyweixinAccountEmployeeMapping> accountEmployeeMappingList = new ArrayList<>();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String outEa = enterpriseBindEntity.getOutEa();
            String finalAppId = enterpriseBindEntity.getAppId();

            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).appId(finalAppId).outEmpId(outAccount).build());
            if(CollectionUtils.isEmpty(employeeBindEntities)) {
                com.facishare.open.qywx.accountsync.result.Result<String> userId2OpenUserId = toolsService.userId2OpenUserId2(outEa,finalAppId,outAccount);
                if(StringUtils.isNotEmpty(userId2OpenUserId.getData())) {
                    outAccount = userId2OpenUserId.getData();
                    employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).appId(finalAppId).outEmpId(outAccount).build());
                }
            }
            if(CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            for (OuterOaEmployeeBindEntity employeeBindEntity : employeeBindEntities) {
                QyweixinAccountEmployeeMapping accountEmployeeMapping = new QyweixinAccountEmployeeMapping();
                accountEmployeeMapping.setSource(employeeBindEntity.getChannel().name());
                accountEmployeeMapping.setAppId(employeeBindEntity.getAppId());
                accountEmployeeMapping.setFsAccount("E." + employeeBindEntity.getFsEa() + "." + employeeBindEntity.getFsEmpId());
                accountEmployeeMapping.setOutAccount(employeeBindEntity.getOutEmpId());
                accountEmployeeMapping.setOutEa(employeeBindEntity.getOutEa());
                accountEmployeeMapping.setStatus(employeeBindEntity.getBindStatus() == BindStatusEnum.normal ? 0 : 1);
                accountEmployeeMapping.setGmtCreate(new Timestamp(employeeBindEntity.getCreateTime()));
                accountEmployeeMapping.setGmtModified(new Timestamp(employeeBindEntity.getUpdateTime()));
                accountEmployeeMappingList.add(accountEmployeeMapping);
            }
        }

//        QyweixinAccountEmployeeMapping result = new QyweixinAccountEmployeeMapping();
//        Result<String> outEaResult = fsEaToOutEa(source, fsEnterpriseAccount);
//        String outEa = "";
//        if(outEaResult.isSuccess()){
//            if(StringUtils.isEmpty(outEaResult.getData())){
//                return new Result().addError("未查到企业绑定信息");
//            }
//            outEa = outEaResult.getData();
//        }
//        List<QyweixinAccountEmployeeMapping> accountEmployeeMappingList =
//                accountEmployeeBindDao.queryMappingFromOutAccountBatch(source, appId,  Lists.newArrayList(outAccount), outEa);
//        if(CollectionUtils.isEmpty(accountEmployeeMappingList)) {
//            com.facishare.open.qywx.accountsync.result.Result<String> userId2OpenUserId = toolsService.userId2OpenUserId2(outEa,appId,outAccount);
//            if(StringUtils.isNotEmpty(userId2OpenUserId.getData())) {
//                outAccount = userId2OpenUserId.getData();
//                accountEmployeeMappingList =
//                        accountEmployeeBindDao.queryMappingFromOutAccountBatch(source, appId,  Lists.newArrayList(outAccount), outEa);
//            }
//        }
//        return new Result<>(accountEmployeeMappingList);
        return new Result<>(accountEmployeeMappingList);
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> employeeToOutAccount(String source, String outEa, String appId, List<String> outAccounts) {
//        List<QyweixinAccountEmployeeMapping> accountEmployeeMappingList =
//                accountEmployeeBindDao.queryMappingFromOutAccountBatch(source, appId,  outAccounts, outEa);
//        return new Result<>(accountEmployeeMappingList);
        return new Result<>();
    }

    @Override
    public Result<Map<String, String>> outAccountToFsAccountBatch(String source, String fsEnterpriseAccount,
                                                                  String appId,List<String> outAccountList) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEnterpriseAccount).bindStatus(BindStatusEnum.normal).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>(Maps.newHashMap());
        }

        Map<String, String> allOutAccountToFsAccountMap = Maps.newHashMap();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String outEa = enterpriseBindEntity.getOutEa();
            String finalAppId = enterpriseBindEntity.getAppId();
            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, outEa, fsEnterpriseAccount, finalAppId, null, outAccountList);
            if(CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            Map<String, String> outAccountToFsAccountMap = employeeBindEntities.stream()
                    .collect(Collectors.toMap(
                            OuterOaEmployeeBindEntity::getOutEmpId,
                            entity -> "E." + fsEnterpriseAccount + "." + entity.getFsEmpId(),
                            (key1, key2) -> key2
                    ));

            allOutAccountToFsAccountMap.putAll(outAccountToFsAccountMap);

        }
        return new Result<>(allOutAccountToFsAccountMap);
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> outAccountToFsAccountBatch(String source, String fsEnterpriseAccount,
                                                                                   String appId, List<String> outAccountList, int status) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEnterpriseAccount).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>(new ArrayList<>());
        }

        List<QyweixinAccountEmployeeMapping> accountEmployeeMappingList = new ArrayList<>();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String outEa = enterpriseBindEntity.getOutEa();
            String finalAppId = enterpriseBindEntity.getAppId();
            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEnterpriseAccount).outEa(outEa).appId(finalAppId).bindStatus(status == 0 ? BindStatusEnum.normal : BindStatusEnum.stop).build());
            if(CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            for (OuterOaEmployeeBindEntity employeeBindEntity : employeeBindEntities) {
                QyweixinAccountEmployeeMapping accountEmployeeMapping = new QyweixinAccountEmployeeMapping();
                accountEmployeeMapping.setSource(employeeBindEntity.getChannel().name());
                accountEmployeeMapping.setAppId(employeeBindEntity.getAppId());
                accountEmployeeMapping.setFsAccount("E." + employeeBindEntity.getFsEa() + "." + employeeBindEntity.getFsEmpId());
                accountEmployeeMapping.setOutAccount(employeeBindEntity.getOutEmpId());
                accountEmployeeMapping.setOutEa(employeeBindEntity.getOutEa());
                accountEmployeeMapping.setStatus(employeeBindEntity.getBindStatus() == BindStatusEnum.normal ? 0 : 1);
                accountEmployeeMapping.setGmtCreate(new Timestamp(employeeBindEntity.getCreateTime()));
                accountEmployeeMapping.setGmtModified(new Timestamp(employeeBindEntity.getUpdateTime()));
                accountEmployeeMappingList.add(accountEmployeeMapping);
            }
        }
        return new Result<>(accountEmployeeMappingList);
    }

    /**
     * 内部账号换外部账号。
     *
     * @param source        : "qywx"是企业微信
     * @param fsAccountList
     * @return : 外部账号。 企业微信账号
     */
    @Deprecated
    @Override
    public Result<Map<String, String>> fsAccountToOutAccountBatch(String source, List<String> fsAccountList) {
        return fsAccountToOutAccountBatch(source, ConfigCenter.crmAppId, fsAccountList);
    }

    @Deprecated
    @Override
    public Result<Map<String, String>> fsAccountToOutAccountBatch(String source, String appId, List<String> fsAccountList) {
        List<String> accountList = Splitter.on(".").splitToList(fsAccountList.get(0));
        String fsEa = accountList.get(1);
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).bindStatus(BindStatusEnum.normal).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>(new HashMap<>());
        }

        List<String> fsUserIds = fsAccountList.stream()
                .map(account -> {
                    // 按照 "." 分割字符串
                    String[] parts = account.split("\\.");
                    // 取最后一个部分作为 fsUserId
                    return parts[parts.length - 1];
                })
                .collect(Collectors.toList());

        Map<String, String> allOutAccountToFsAccountMap = Maps.newHashMap();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String outEa = enterpriseBindEntity.getOutEa();
            String finalAppId = enterpriseBindEntity.getAppId();
            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, outEa, fsEa, finalAppId, fsUserIds, null);
            if(CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            Map<String, String> outAccountToFsAccountMap = employeeBindEntities.stream()
                    .collect(Collectors.toMap(
                            entity -> "E." + fsEa + "." + entity.getFsEmpId(),
                            OuterOaEmployeeBindEntity::getOutEmpId,
                            (key1, key2) -> key2
                    ));

            allOutAccountToFsAccountMap.putAll(outAccountToFsAccountMap);
        }
        return new Result<>(allOutAccountToFsAccountMap);

//        List<QyweixinAccountEmployeeMapping> accountEmployeeMappingList =
//                accountEmployeeBindDao.queryMappingFromFsAccountBatch(source, appId, 0, fsAccountList, null);
//        if (CollectionUtils.isNotEmpty(accountEmployeeMappingList)) {
//            Map<String, String> fsAccountToOutAccountMap = accountEmployeeMappingList.stream()
//                    .collect(Collectors.toMap(QyweixinAccountEmployeeMapping::getFsAccount,
//                            QyweixinAccountEmployeeMapping::getOutAccount, (oldValue, newValue) -> newValue));
//            return new Result<>(fsAccountToOutAccountMap);
//        } else {
//            return new Result<>(Maps.newHashMap());
//        }
    }

    /**
     * 支持一对多场景，
     *
     * @param source
     * @param appId
     * @param fsAccountList
     * @param outEa
     * @return
     */
    @Override
    public Result<List<EmployeeAccountMatchResult>> fsAccountToOutAccountBatch2(String source, String appId, List<String> fsAccountList, String outEa) {
        List<String> accountList = Splitter.on(".").splitToList(fsAccountList.get(0));
        String fsEa = accountList.get(1);
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).outEa(outEa).bindStatus(BindStatusEnum.normal).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>(new ArrayList<>());
        }

        List<String> fsUserIds = fsAccountList.stream()
                .map(account -> {
                    // 按照 "." 分割字符串
                    String[] parts = account.split("\\.");
                    // 取最后一个部分作为 fsUserId
                    return parts[parts.length - 1];
                })
                .collect(Collectors.toList());

        List<EmployeeAccountMatchResult> matchResultList = new ArrayList<>();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String corpId = enterpriseBindEntity.getOutEa();
            String finalAppId = enterpriseBindEntity.getAppId();
            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, corpId, fsEa, finalAppId, fsUserIds, null);
            if (CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            for (OuterOaEmployeeBindEntity employeeBindEntity : employeeBindEntities) {
                String outEmpId = employeeBindEntity.getOutEmpId();
                matchResultList.add(new EmployeeAccountMatchResult("E." + fsEa + "." + employeeBindEntity.getFsEmpId(), outEmpId, corpId, finalAppId));
            }
        }

        return new Result<>(matchResultList);
    }

    @Deprecated
    @Override
    public Result<Map<String, String>> fsAccountToOutAccount(String source, List<String> fsAccountList) {
        List<String> accountList = Splitter.on(".").splitToList(fsAccountList.get(0));
        String fsEa = accountList.get(1);
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>(new HashMap<>());
        }

        List<String> fsUserIds = fsAccountList.stream()
                .map(account -> {
                    // 按照 "." 分割字符串
                    String[] parts = account.split("\\.");
                    // 取最后一个部分作为 fsUserId
                    return parts[parts.length - 1];
                })
                .collect(Collectors.toList());

        Map<String, String> allOutAccountToFsAccountMap = Maps.newHashMap();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String outEa = enterpriseBindEntity.getOutEa();
            String finalAppId = enterpriseBindEntity.getAppId();
            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, outEa, fsEa, finalAppId, fsUserIds, null);
            if(CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            Map<String, String> outAccountToFsAccountMap = employeeBindEntities.stream()
                    .collect(Collectors.toMap(
                            entity -> "E." + fsEa + "." + entity.getFsEmpId(),
                            OuterOaEmployeeBindEntity::getOutEmpId,
                            (key1, key2) -> key2
                    ));

            allOutAccountToFsAccountMap.putAll(outAccountToFsAccountMap);
        }
        return new Result<>(allOutAccountToFsAccountMap);
    }

    @Override
    public Result<List<EmployeeAccountMatchResult>> fsAccountToOutAccount2(String source, List<String> fsAccountList, String outEa) {
        List<String> accountList = Splitter.on(".").splitToList(fsAccountList.get(0));
        String fsEa = accountList.get(1);
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).outEa(outEa).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>(new ArrayList<>());
        }

        List<String> fsUserIds = fsAccountList.stream()
                .map(account -> {
                    // 按照 "." 分割字符串
                    String[] parts = account.split("\\.");
                    // 取最后一个部分作为 fsUserId
                    return parts[parts.length - 1];
                })
                .collect(Collectors.toList());

        List<EmployeeAccountMatchResult> matchResultList = new ArrayList<>();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String corpId = enterpriseBindEntity.getOutEa();
            String finalAppId = enterpriseBindEntity.getAppId();
            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, corpId, fsEa, finalAppId, fsUserIds, null);
            if (CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            for (OuterOaEmployeeBindEntity employeeBindEntity : employeeBindEntities) {
                String outEmpId = employeeBindEntity.getOutEmpId();
                matchResultList.add(new EmployeeAccountMatchResult("E." + fsEa + "." + employeeBindEntity.getFsEmpId(), outEmpId, corpId, finalAppId));
            }
        }

        return new Result<>(matchResultList);
    }

    @Deprecated
    @Override
    public Result<Map<String, String>> fsAccountToOutAccount(String source, List<String> fsAccountList, Integer status) {
        List<String> accountList = Splitter.on(".").splitToList(fsAccountList.get(0));
        String fsEa = accountList.get(1);
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>(new HashMap<>());
        }

        List<String> fsUserIds = fsAccountList.stream()
                .map(account -> {
                    // 按照 "." 分割字符串
                    String[] parts = account.split("\\.");
                    // 取最后一个部分作为 fsUserId
                    return parts[parts.length - 1];
                })
                .collect(Collectors.toList());

        Map<String, String> allOutAccountToFsAccountMap = Maps.newHashMap();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String outEa = enterpriseBindEntity.getOutEa();
            String finalAppId = enterpriseBindEntity.getAppId();
            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, outEa, fsEa, finalAppId, fsUserIds, null);
            if (status != null) {
                BindStatusEnum statusEnum = status == 0 ? BindStatusEnum.normal : BindStatusEnum.stop;
                employeeBindEntities = employeeBindEntities.stream().filter(entity -> entity.getBindStatus() == statusEnum).collect(Collectors.toList());
            }
            if(CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            Map<String, String> outAccountToFsAccountMap = employeeBindEntities.stream()
                    .collect(Collectors.toMap(
                            entity -> "E." + fsEa + "." + entity.getFsEmpId(),
                            OuterOaEmployeeBindEntity::getOutEmpId,
                            (key1, key2) -> key2
                    ));

            allOutAccountToFsAccountMap.putAll(outAccountToFsAccountMap);
        }
        return new Result<>(allOutAccountToFsAccountMap);
    }

    @Override
    public Result<List<EmployeeAccountMatchResult>> fsAccountToOutAccount2(String source, List<String> fsAccountList, Integer status, String outEa) {
        List<String> accountList = Splitter.on(".").splitToList(fsAccountList.get(0));
        String fsEa = accountList.get(1);
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>(new ArrayList<>());
        }

        List<String> fsUserIds = fsAccountList.stream()
                .map(account -> {
                    // 按照 "." 分割字符串
                    String[] parts = account.split("\\.");
                    // 取最后一个部分作为 fsUserId
                    return parts[parts.length - 1];
                })
                .collect(Collectors.toList());

        List<EmployeeAccountMatchResult> matchResultList = new ArrayList<>();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String corpId = enterpriseBindEntity.getOutEa();
            String finalAppId = enterpriseBindEntity.getAppId();
            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, corpId, fsEa, finalAppId, fsUserIds, null);
            if (status != null) {
                BindStatusEnum statusEnum = status == 0 ? BindStatusEnum.normal : BindStatusEnum.stop;
                employeeBindEntities = employeeBindEntities.stream().filter(entity -> entity.getBindStatus() == statusEnum).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            for (OuterOaEmployeeBindEntity employeeBindEntity : employeeBindEntities) {
                String outEmpId = employeeBindEntity.getOutEmpId();
                matchResultList.add(new EmployeeAccountMatchResult("E." + fsEa + "." + employeeBindEntity.getFsEmpId(), outEmpId, corpId, finalAppId));
            }
        }

        return new Result<>(matchResultList);
    }

    @Deprecated
    @Override
    public Result<Map<String, String>> fsAccountToOutAccountBatchByIsv(String source, String appId, List<String> fsAccountList) {
        List<String> accountList = Splitter.on(".").splitToList(fsAccountList.get(0));
        String fsEa = accountList.get(1);
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>(new HashMap<>());
        }

        List<String> fsUserIds = fsAccountList.stream()
                .map(account -> {
                    // 按照 "." 分割字符串
                    String[] parts = account.split("\\.");
                    // 取最后一个部分作为 fsUserId
                    return parts[parts.length - 1];
                })
                .collect(Collectors.toList());

        Map<String, String> allOutAccountToFsAccountMap = Maps.newHashMap();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String outEa = enterpriseBindEntity.getOutEa();
            String finalAppId = enterpriseBindEntity.getAppId();
            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, outEa, fsEa, finalAppId, fsUserIds, null);
            if(CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            Map<String, String> outAccountToFsAccountMap = employeeBindEntities.stream()
                    .collect(Collectors.toMap(
                            entity -> "E." + fsEa + "." + entity.getFsEmpId(),
                            OuterOaEmployeeBindEntity::getOutEmpId,
                            (key1, key2) -> key2
                    ));

            allOutAccountToFsAccountMap.putAll(outAccountToFsAccountMap);
        }
        return new Result<>(allOutAccountToFsAccountMap);
    }

    @Override
    public Result<List<EmployeeAccountMatchResult>> fsAccountToOutAccountBatchByIsv2(String source, String appId, List<String> fsAccountList, String outEa) {
        List<String> accountList = Splitter.on(".").splitToList(fsAccountList.get(0));
        String fsEa = accountList.get(1);
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).outEa(outEa).appId(appId).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>(new ArrayList<>());
        }

        List<String> fsUserIds = fsAccountList.stream()
                .map(account -> {
                    // 按照 "." 分割字符串
                    String[] parts = account.split("\\.");
                    // 取最后一个部分作为 fsUserId
                    return parts[parts.length - 1];
                })
                .collect(Collectors.toList());

        List<EmployeeAccountMatchResult> matchResultList = new ArrayList<>();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String corpId = enterpriseBindEntity.getOutEa();
            String finalAppId = enterpriseBindEntity.getAppId();
            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, corpId, fsEa, finalAppId, fsUserIds, null);
            if (CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            for (OuterOaEmployeeBindEntity employeeBindEntity : employeeBindEntities) {
                String outEmpId = employeeBindEntity.getOutEmpId();
                matchResultList.add(new EmployeeAccountMatchResult("E." + fsEa + "." + employeeBindEntity.getFsEmpId(), outEmpId, corpId, finalAppId));
            }
        }

        return new Result<>(matchResultList);
    }

    @Deprecated
    @Override
    public Result<Map<String, String>> fsAccountToOutAccountByIsv(String source, List<String> fsAccountList) {
        List<String> accountList = Splitter.on(".").splitToList(fsAccountList.get(0));
        String fsEa = accountList.get(1);
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>(new HashMap<>());
        }

        List<String> fsUserIds = fsAccountList.stream()
                .map(account -> {
                    // 按照 "." 分割字符串
                    String[] parts = account.split("\\.");
                    // 取最后一个部分作为 fsUserId
                    return parts[parts.length - 1];
                })
                .collect(Collectors.toList());

        Map<String, String> allOutAccountToFsAccountMap = Maps.newHashMap();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String outEa = enterpriseBindEntity.getOutEa();
            String finalAppId = enterpriseBindEntity.getAppId();
            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, outEa, fsEa, finalAppId, fsUserIds, null);
            if(CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            Map<String, String> outAccountToFsAccountMap = employeeBindEntities.stream()
                    .collect(Collectors.toMap(
                            entity -> "E." + fsEa + "." + entity.getFsEmpId(),
                            OuterOaEmployeeBindEntity::getOutEmpId,
                            (key1, key2) -> key2
                    ));

            allOutAccountToFsAccountMap.putAll(outAccountToFsAccountMap);
        }
        return new Result<>(allOutAccountToFsAccountMap);
    }

    @Override
    public Result<List<EmployeeAccountMatchResult>> fsAccountToOutAccountByIsv2(String source, List<String> fsAccountList, String outEa) {
        List<String> accountList = Splitter.on(".").splitToList(fsAccountList.get(0));
        String fsEa = accountList.get(1);
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).outEa(outEa).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>(new ArrayList<>());
        }

        List<String> fsUserIds = fsAccountList.stream()
                .map(account -> {
                    // 按照 "." 分割字符串
                    String[] parts = account.split("\\.");
                    // 取最后一个部分作为 fsUserId
                    return parts[parts.length - 1];
                })
                .collect(Collectors.toList());

        List<EmployeeAccountMatchResult> matchResultList = new ArrayList<>();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String corpId = enterpriseBindEntity.getOutEa();
            String finalAppId = enterpriseBindEntity.getAppId();
            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, corpId, fsEa, finalAppId, fsUserIds, null);
            if (CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            for (OuterOaEmployeeBindEntity employeeBindEntity : employeeBindEntities) {
                String outEmpId = employeeBindEntity.getOutEmpId();
                matchResultList.add(new EmployeeAccountMatchResult("E." + fsEa + "." + employeeBindEntity.getFsEmpId(), outEmpId, corpId, finalAppId));
            }
        }

        return new Result<>(matchResultList);
    }

    @Deprecated
    @Override
    public Result<QyweixinAccountEmployeeMapping> getQywxEmployeeMapping(String fsEa, String fsUserId) {
        return getQywxEmployeeMapping2(fsEa, fsUserId, null);
    }

    @Override
    public Result<QyweixinAccountEmployeeMapping> getQywxEmployeeMapping2(String fsEa, String fsUserId, String outEa) {
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).fsEmpId(fsUserId).outEa(outEa).build());
        if(CollectionUtils.isEmpty(employeeBindEntities)) {
            return new Result<>(null);
        }
        OuterOaEmployeeBindEntity employeeBindEntity = employeeBindEntities.get(0);
        QyweixinAccountEmployeeMapping accountEmployeeBind = new QyweixinAccountEmployeeMapping();
        accountEmployeeBind.setSource(employeeBindEntity.getChannel().name());
        accountEmployeeBind.setAppId(employeeBindEntity.getAppId());
        accountEmployeeBind.setFsAccount("E." + employeeBindEntity.getFsEa()+"."+employeeBindEntity.getFsEmpId());
        accountEmployeeBind.setOutAccount(employeeBindEntity.getOutEmpId());
        accountEmployeeBind.setOutEa(employeeBindEntity.getOutEa());
        accountEmployeeBind.setStatus(employeeBindEntity.getBindStatus() == BindStatusEnum.normal ? 0 : 1);
        accountEmployeeBind.setGmtCreate(new Timestamp(employeeBindEntity.getCreateTime()));
        accountEmployeeBind.setGmtModified(new Timestamp(employeeBindEntity.getUpdateTime()));

        return new Result<>(accountEmployeeBind);
    }

    @Override
    public Result<QyweixinAccountEmployeeMapping> getQywxEmployeeMapping3(String fsEa, String fsUserId, String outEa, String outUserId) {
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).fsEmpId(fsUserId).outEa(outEa).outEmpId(outUserId).build());
        if(CollectionUtils.isEmpty(employeeBindEntities)) {
            return new Result<>(null);
        }
        OuterOaEmployeeBindEntity employeeBindEntity = employeeBindEntities.get(0);
        QyweixinAccountEmployeeMapping accountEmployeeBind = new QyweixinAccountEmployeeMapping();
        accountEmployeeBind.setSource(employeeBindEntity.getChannel().name());
        accountEmployeeBind.setAppId(employeeBindEntity.getAppId());
        accountEmployeeBind.setFsAccount("E." + employeeBindEntity.getFsEa()+"."+employeeBindEntity.getFsEmpId());
        accountEmployeeBind.setOutAccount(employeeBindEntity.getOutEmpId());
        accountEmployeeBind.setOutEa(employeeBindEntity.getOutEa());
        accountEmployeeBind.setStatus(employeeBindEntity.getBindStatus() == BindStatusEnum.normal ? 0 : 1);
        accountEmployeeBind.setGmtCreate(new Timestamp(employeeBindEntity.getCreateTime()));
        accountEmployeeBind.setGmtModified(new Timestamp(employeeBindEntity.getUpdateTime()));

        return new Result<>(accountEmployeeBind);
    }

    @Override
    public Result<QyweixinAccountDepartmentMapping> getQywxDepartmentMapping(String fsEa, String fsDepId, String outEa, String outDepId) {
        List<OuterOaDepartmentBindEntity> departmentBindManagerEntities = outerOaDepartmentBindManager.getEntities(OuterOaDepartmentBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).fsDepId(fsDepId).outEa(outEa).outDepId(outDepId).build());
        if(CollectionUtils.isEmpty(departmentBindManagerEntities)) {
            return new Result<>(null);
        }
        OuterOaDepartmentBindEntity departmentBindManagerEntity = departmentBindManagerEntities.get(0);
        QyweixinAccountDepartmentMapping accountDepartmentBind = new QyweixinAccountDepartmentMapping();
        accountDepartmentBind.setSource(departmentBindManagerEntity.getChannel().name());
        accountDepartmentBind.setAppId(departmentBindManagerEntity.getAppId());
        accountDepartmentBind.setFsDepartmentId(Integer.valueOf(departmentBindManagerEntity.getFsDepId()));
        accountDepartmentBind.setOutDepartmentId(departmentBindManagerEntity.getOutDepId());
        accountDepartmentBind.setOutEa(departmentBindManagerEntity.getOutEa());
        accountDepartmentBind.setStatus(departmentBindManagerEntity.getBindStatus() == BindStatusEnum.normal ? 0 : 1);
        return new Result<>(accountDepartmentBind);
    }

    @Override
    public Result<QyweixinAccountEmployeeMapping> getFsEmployeeMapping(String fsEa, String qywxUserId) {
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).outEmpId(qywxUserId).build());
        if(CollectionUtils.isEmpty(employeeBindEntities)) {
            return new Result<>(null);
        }
        OuterOaEmployeeBindEntity employeeBindEntity = employeeBindEntities.get(0);
        QyweixinAccountEmployeeMapping accountEmployeeBind = new QyweixinAccountEmployeeMapping();
        accountEmployeeBind.setSource(employeeBindEntity.getChannel().name());
        accountEmployeeBind.setAppId(employeeBindEntity.getAppId());
        accountEmployeeBind.setFsAccount("E." + employeeBindEntity.getFsEa()+"."+employeeBindEntity.getFsEmpId());
        accountEmployeeBind.setOutAccount(employeeBindEntity.getOutEmpId());
        accountEmployeeBind.setOutEa(employeeBindEntity.getOutEa());
        accountEmployeeBind.setStatus(employeeBindEntity.getBindStatus() == BindStatusEnum.normal ? 0 : 1);
        accountEmployeeBind.setGmtCreate(new Timestamp(employeeBindEntity.getCreateTime()));
        accountEmployeeBind.setGmtModified(new Timestamp(employeeBindEntity.getUpdateTime()));

        return new Result<>(accountEmployeeBind);
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> getFsEmployeeMapping2(String outEa, String qywxUserId) {
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).outEmpId(qywxUserId).build());
        if(CollectionUtils.isEmpty(employeeBindEntities)) {
            return new Result<>(null);
        }
        List<QyweixinAccountEmployeeMapping> accountEmployeeBinds = new ArrayList<>();
        for (OuterOaEmployeeBindEntity employeeBindEntity : employeeBindEntities) {
            QyweixinAccountEmployeeMapping accountEmployeeBind = new QyweixinAccountEmployeeMapping();
            accountEmployeeBind.setSource(employeeBindEntity.getChannel().name());
            accountEmployeeBind.setAppId(employeeBindEntity.getAppId());
            accountEmployeeBind.setFsAccount("E." + employeeBindEntity.getFsEa()+"."+employeeBindEntity.getFsEmpId());
            accountEmployeeBind.setOutAccount(employeeBindEntity.getOutEmpId());
            accountEmployeeBind.setOutEa(employeeBindEntity.getOutEa());
            accountEmployeeBind.setStatus(employeeBindEntity.getBindStatus() == BindStatusEnum.normal ? 0 : 1);
            accountEmployeeBind.setGmtCreate(new Timestamp(employeeBindEntity.getCreateTime()));
            accountEmployeeBind.setGmtModified(new Timestamp(employeeBindEntity.getUpdateTime()));
            accountEmployeeBinds.add(accountEmployeeBind);
        }
        return new Result<>(accountEmployeeBinds);
    }

    @Deprecated
    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> fsAccountToOutAccountBatchV2(String source, String appId,
            int status, List<String> fsAccountList) {
        return fsAccountToOutAccountBatchV21(source, appId, status, fsAccountList, null);
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> fsAccountToOutAccountBatchV21(String source, String appId, int status, List<String> fsAccountList, String outEa) {
        List<String> accountList = Splitter.on(".").splitToList(fsAccountList.get(0));
        String fsEa = accountList.get(1);
        List<String> fsUserIds = fsAccountList.stream()
                .map(account -> {
                    // 按照 "." 分割字符串
                    String[] parts = account.split("\\.");
                    // 取最后一个部分作为 fsUserId
                    return parts[parts.length - 1];
                })
                .collect(Collectors.toList());
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, outEa, fsEa, appId, fsUserIds, null);
        if(CollectionUtils.isEmpty(employeeBindEntities)) {
            return new Result<>(null);
        }
        if (status != -1) {
            BindStatusEnum statusEnum = status == 0 ? BindStatusEnum.normal : BindStatusEnum.stop;
            employeeBindEntities = employeeBindEntities.stream().filter(entity -> entity.getBindStatus() == statusEnum).collect(Collectors.toList());
        }

        List<QyweixinAccountEmployeeMapping> accountEmployeeBinds = new ArrayList<>();
        for (OuterOaEmployeeBindEntity employeeBindEntity : employeeBindEntities) {
            QyweixinAccountEmployeeMapping accountEmployeeBind = new QyweixinAccountEmployeeMapping();
            accountEmployeeBind.setSource(employeeBindEntity.getChannel().name());
            accountEmployeeBind.setAppId(employeeBindEntity.getAppId());
            accountEmployeeBind.setFsAccount("E." + employeeBindEntity.getFsEa()+"."+employeeBindEntity.getFsEmpId());
            accountEmployeeBind.setOutAccount(employeeBindEntity.getOutEmpId());
            accountEmployeeBind.setOutEa(employeeBindEntity.getOutEa());
            accountEmployeeBind.setStatus(employeeBindEntity.getBindStatus() == BindStatusEnum.normal ? 0 : 1);
            accountEmployeeBind.setGmtCreate(new Timestamp(employeeBindEntity.getCreateTime()));
            accountEmployeeBind.setGmtModified(new Timestamp(employeeBindEntity.getUpdateTime()));
            accountEmployeeBinds.add(accountEmployeeBind);
        }
        return new Result<>(accountEmployeeBinds);
    }

    /**
     * 修改员工状态
     *
     * @param isDelete true-正常 false-停用
     * @return
     */
    @Override
    public Result<Void> deleteOrResumeEmployee(String source, String ea, String appId, List<String> outAccounts, boolean isDelete) {
//        int status = isDelete ? 1 : 0;
//        int i = accountEmployeeBindDao.changeEmployeeStatus(source, ea, appId, outAccounts, status);
//        log.debug("deleteOrResumeEmployee success. ea:{}, appId:{}, outAccounts:{}, status:{}", ea, appId, outAccounts, status);
        return new Result<>();
    }

    @Override
    public Result<String> outEaToFsEa(String source, String outEa) {
        return outEaToFsEa(source, outEa, "1");
    }

    /**
     * 企业的外部账号转内部账号
     *
     * @param source : "qywx"是企业微信
     * @param outEa  :企业在外部平台上的账号
     * @return : 企业在纷享的账号
     */
    @Override
    public Result<String> outEaToFsEa(String source, String outEa, String depId) {
        log.info("QyweixinAccountBindServiceImpl.outEaToFsEa,outEa={}", outEa);
        if (StringUtils.isEmpty(depId)) {
            depId = "1";
        }

        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).bindStatus(BindStatusEnum.normal).build());
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>();
        }

        //只能选择一个
        return new Result<>(enterpriseBindEntities.get(0).getFsEa());

//        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = accountEnterpriseBindDao.queryEaMappingFromOutEa(source, outEa,depId,0, ConfigCenter.crm_domain);
//        log.info("QyweixinAccountBindServiceImpl.outEaToFsEa,enterpriseMappingList={}",enterpriseMappingList);
//        if (CollectionUtils.isNotEmpty(enterpriseMappingList)) {
//            return new Result<>(enterpriseMappingList.get(0).getFsEa());
//        } else {
//            return new Result<>();
//        }
    }

//    @Override
//    public Result<List<QyweixinAccountEnterpriseMapping>> selectEnterpriseBind(String source, String outEa) {
//
//        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = accountEnterpriseBindDao.queryEaMappingFromOutEa(source, outEa,null,-1, ConfigCenter.crm_domain);
//        log.info("QyweixinAccountBindServiceImpl.selectEnterpriseBind source:{}, outEa:{}, enterpriseMappingList:{}", source, outEa, enterpriseMappingList);
//        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
//            return new Result<>();
//        }
//        return new Result<>(enterpriseMappingList);
//    }
//
//    @Override
//    public Result<List<QyweixinAccountEnterpriseMapping>> selectAllEnterpriseBind(String source, String outEa) {
//        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = accountEnterpriseBindDao.queryEaMappingFromOutEa(source, outEa,null,-1, null);
//        log.info("QyweixinAccountBindServiceImpl.selectAllEnterpriseBind source:{}, outEa:{}, enterpriseMappingList:{}", source, outEa, enterpriseMappingList);
//        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
//            return new Result<>();
//        }
//        return new Result<>(enterpriseMappingList);
//    }

    /**
     * 企业的外部账号转内部账号
     *
     * @param source : "yunzhijia"是企业微信
     * @param fsEa   :企业在纷享上的账号
     * @return : 企业在外部平台上的账号
     */
    @Override
    public Result<String> fsEaToOutEa(String source, String fsEa) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).bindStatus(BindStatusEnum.normal).build());
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>();
        }
        return new Result<>(enterpriseBindEntities.get(0).getOutEa());
    }

//    @Override
//    public Result<QyweixinAccountEnterpriseMapping> fsEaToOutEaResult(String source, String fsEa) {
//        return fsEaToOutEaResult2(source,fsEa,null);
//    }
//
    @Override
    public Result<List<QyweixinAccountEnterpriseMapping>> fsEaToOutEaResultList(String source, String fsEa) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).bindStatus(BindStatusEnum.normal).build());
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>();
        }

        List<QyweixinAccountEnterpriseMapping> mappingList = enterpriseBindEntities.stream()
                .map(enterpriseBindEntity -> {
                    QyweixinAccountEnterpriseMapping accountEnterpriseMapping = new QyweixinAccountEnterpriseMapping();
                    accountEnterpriseMapping.setSource(enterpriseBindEntity.getChannel().name());
                    accountEnterpriseMapping.setFsEa(enterpriseBindEntity.getFsEa());
                    accountEnterpriseMapping.setOutEa(enterpriseBindEntity.getOutEa());
                    accountEnterpriseMapping.setBindType(enterpriseBindEntity.getBindType() == BindTypeEnum.auto ? 0 : 1);
                    accountEnterpriseMapping.setStatus(enterpriseBindEntity.getBindStatus() == BindStatusEnum.normal ? 0 : 1);
                    accountEnterpriseMapping.setGmtCreate(new Timestamp(enterpriseBindEntity.getCreateTime()));
                    accountEnterpriseMapping.setGmtModified(new Timestamp(enterpriseBindEntity.getUpdateTime()));
                    return accountEnterpriseMapping;
                })
                .collect(Collectors.toList());

        return new Result<>(mappingList);
    }

//
//    @Override
//    public Result<QyweixinAccountEnterpriseMapping> fsEaToOutEaResult2(String source, String fsEa, String outEa) {
//        QyweixinAccountEnterpriseMapping mapping = accountEnterpriseBindDao.queryEaMappingFromFsEa(source, fsEa, outEa, ConfigCenter.crm_domain);
//        return new Result<>(mapping);
//    }
//
//    @Override
//    public Result<QyweixinAccountEnterpriseMapping> fsEaToOutEaAllResult2(String source, String fsEa, String outEa) {
//        QyweixinAccountEnterpriseMapping mapping = accountEnterpriseBindDao.queryEaMappingFromFsEa(source, fsEa, outEa, null);
//        return new Result<>(mapping);
//    }

    /**
     * 默认使用CRM应用查询
     */
    @Override
    public Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindByFsDepartment(String source, String fsEa, List<Integer> fsDepartmentIdList) {
        return queryDepartmentBindByFsDepartment(source, fsEa, null, fsDepartmentIdList);
    }

    @Override
    public Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindByFsDepartment(String source,
                                                                                            String fsEa,
                                                                                            String appId,
                                                                                            List<Integer> fsDepartmentIdList) {
        List<String> fsDepartList = fsDepartmentIdList.stream().map(String::valueOf).collect(Collectors.toList());
        List<OuterOaDepartmentBindEntity> departmentBindEntities = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntities(ChannelEnum.qywx, fsEa, null, appId, fsDepartList, null);
        log.info("QyweixinAccountBindServiceImpl.queryDepartmentBindByFsDepartment,departmentBindEntities={}", departmentBindEntities);
        List<QyweixinAccountDepartmentMapping> departmentBindList = departmentBindEntities.stream().map(departmentBindEntity -> {
            QyweixinAccountDepartmentMapping departmentBind = new QyweixinAccountDepartmentMapping();
            departmentBind.setSource(departmentBindEntity.getChannel().name());
            departmentBind.setFsEa(departmentBindEntity.getFsEa());
            departmentBind.setAppId(departmentBindEntity.getAppId());
            departmentBind.setOutEa(departmentBindEntity.getOutEa());
            departmentBind.setFsDepartmentId(Integer.valueOf(departmentBindEntity.getFsDepId()));
            departmentBind.setOutDepartmentId(departmentBindEntity.getOutDepId());
            departmentBind.setStatus(departmentBindEntity.getBindStatus() == BindStatusEnum.normal ? 0 : 1);
            return departmentBind;
        }).collect(Collectors.toList());
        return new Result<>(departmentBindList);
    }

    /**
     * 修改部门状态
     *
     * @param isDelete true-正常 false-停用
     * @return
     */
    @Override
    public Result<Void> deleteOrResumeDepartment(String source, String ea, String appId,
                                                 List<String> outDepartmentIds, boolean isDelete) {
//        int status = isDelete ? 1 : 0;
//        int i = accountDepartmentBindDao.changeDepartmentStatus(source, ea, appId, outDepartmentIds, status);
//        log.debug("deleteOrResumeDepartment success. ea:{}, appId:{}, outDepartmentIds:{}, status:{}", ea, appId, outDepartmentIds, status);
        return new Result<>();
    }

    @Override
    public Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindByOutDepartment(String source, String outEa, List<String> outDepartmentIdList) {
        return queryDepartmentBindByOutDepartment(source, outEa, null, 0, outDepartmentIdList);
    }

    @Override
    public Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindByOutDepartment(String source,
            String outEa, String appId, List<String> outDepartmentIdList) {
        return queryDepartmentBindByOutDepartment(source, outEa, appId, 0, outDepartmentIdList);
    }

    @Override
    public Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindByOutDepartment(String source,
                                                                                             String outEa,
                                                                                             String appId,
                                                                                             int status,
                                                                                             List<String> outDepartmentIdList) {
        List<OuterOaDepartmentBindEntity> departmentBindEntities = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntities(ChannelEnum.qywx, null, outEa, appId, null, outDepartmentIdList);
        log.info("QyweixinAccountBindServiceImpl.queryDepartmentBindByFsDepartment,departmentBindEntities={}", departmentBindEntities);
        if (status != -1) {
            BindStatusEnum statusEnum = status == 0 ? BindStatusEnum.normal : BindStatusEnum.stop;
            departmentBindEntities = departmentBindEntities.stream().filter(entity -> entity.getBindStatus() == statusEnum).collect(Collectors.toList());
        }

        List<QyweixinAccountDepartmentMapping> departmentBindList = departmentBindEntities.stream().map(departmentBindEntity -> {
            QyweixinAccountDepartmentMapping departmentBind = new QyweixinAccountDepartmentMapping();
            departmentBind.setSource(departmentBindEntity.getChannel().name());
            departmentBind.setFsEa(departmentBindEntity.getFsEa());
            departmentBind.setAppId(departmentBindEntity.getAppId());
            departmentBind.setOutEa(departmentBindEntity.getOutEa());
            departmentBind.setFsDepartmentId(Integer.valueOf(departmentBindEntity.getFsDepId()));
            departmentBind.setOutDepartmentId(departmentBindEntity.getOutDepId());
            departmentBind.setStatus(departmentBindEntity.getBindStatus() == BindStatusEnum.normal ? 0 : 1);
            return departmentBind;
        }).collect(Collectors.toList());
        return new Result<>(departmentBindList);
//        List<QyweixinAccountDepartmentMapping> result =
//                accountDepartmentBindDao.queryDepartmentBindByOutDepartment(source, outEa, appId, status,
//                        outDepartmentIdList);
//        if(null != result && ! result.isEmpty() && CollectionUtils.isNotEmpty(outDepartmentIdList)) {
//            //主属部门放在第一位
//            List<QyweixinAccountDepartmentMapping> DepartmentResult = this.swapDepartment(result, outDepartmentIdList.get(0));
//            return new Result<>(DepartmentResult);
//        } else {
//            return new Result<>(result);
//        }
    }

//    private List<QyweixinAccountDepartmentMapping> swapDepartment(List<QyweixinAccountDepartmentMapping> result, String mainDepartment) {
//        for (int i = 0; i < result.size(); i++) {
//            if(result.get(0).getOutDepartmentId().equals(mainDepartment)) {
//                return result;
//            }
//            if(result.get(i).getOutDepartmentId().equals(mainDepartment)) {
//                Collections.swap(result, 0, i);
//                break;
//            }
//        }
//        return result;
//    }

//    @Override
//    public Result<List<String>> queryFsEaBindBatch(int pageNum, int pageSize) {
//        pageNum = pageNum <=0 ? 1:pageNum;
//        pageSize = pageSize <=0 ? 50: pageSize;
//        ArrayList<HashMap<String, String>> queryFsEaBindLists = accountEnterpriseBindDao.queryFsEaBindBatch((pageNum - 1) * pageSize, pageSize, ConfigCenter.crm_domain);
//        if(queryFsEaBindLists.isEmpty()){
//            return new Result<>(Lists.newArrayList());
//        }
//        return new Result<>(queryFsEaBindLists.stream().map(v -> v.get("fs_ea")).collect(Collectors.toList()));
//    }

    /**
     * 企业微信用户修改UserId后根据旧的UserId查询企业微信的绑定关系
     *
     * @param
     * @return 企业微信的员工绑定关系
     **/
    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> queryFsAccountBindByOldOutAccount(String source,List outAccountList, String outEa) {
        return queryFsAccountBindByOldOutAccount(source, outAccountList, null, outEa);
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> queryFsAccountBindByOldOutAccount(String source,
                                                                                          List outAccountList,
                                                                                          String appId,
                                                                                          String outEa) {
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, outEa, null, appId, null, outAccountList);
        log.info("trace queryFsAccountBindByOldOutAccount employeeBindEntities" + employeeBindEntities);
        List<QyweixinAccountEmployeeMapping> employeeBindList = employeeBindEntities.stream().map(employeeBindEntity -> {
            QyweixinAccountEmployeeMapping employeeBind = new QyweixinAccountEmployeeMapping();
            employeeBind.setSource(employeeBindEntity.getChannel().name());
            employeeBind.setFsAccount("E." + employeeBindEntity.getFsEa() + "." + employeeBindEntity.getFsEmpId());
            employeeBind.setOutAccount(employeeBindEntity.getOutEmpId());
            employeeBind.setOutEa(employeeBindEntity.getOutEa());
            employeeBind.setAppId(employeeBindEntity.getAppId());
            employeeBind.setStatus(employeeBindEntity.getBindStatus() == BindStatusEnum.normal ? 0 : 1);
            employeeBind.setGmtCreate(new Timestamp(employeeBindEntity.getCreateTime()));
            employeeBind.setGmtModified(new Timestamp(employeeBindEntity.getUpdateTime()));
            return employeeBind;
        }).collect(Collectors.toList());
        return new Result<>(employeeBindList);

//        List<QyweixinAccountEmployeeMapping> result = accountEmployeeBindDao.queryMappingFromOutAccountBatch(source, appId, outAccountList, outEa);
//        log.info("trace queryFsAccountBindByOldOutAccount result" + result);
//        if(null != result && ! result.isEmpty()) {
//            return new Result<>(result);
//        } else {
//            return new Result<>(Lists.newArrayList());
//        }
    }

//    /**
//     * 更新企业微信的userId绑定关系
//     *
//     * @param newAccount 新userId
//     * @param oldAccount 旧userId
//     * @param outEa 企业微信ID
//     * @return
//     **/
//    @Override
//    public int updateByNewOutAccount(String newAccount,String oldAccount, String outEa) {
//        return updateByNewOutAccount(newAccount, oldAccount, ConfigCenter.crmAppId, outEa);
//    }
//
//    @Override
//    public int updateByNewOutAccount(String newAccount,String oldAccount,String appId, String outEa) {
//        List<QyweixinAccountEmployeeMapping> mappings = accountEmployeeBindDao.queryMappingFromOutAccountBatch(
//                SourceTypeEnum.QYWX.getSourceType(), appId, Lists.newArrayList(newAccount), outEa);
//        log.info("updateByNewOutAccount start. newAccount:{}, oldAccount:{}, " +
//                "appId:{}, outEa:{}, mappings:{}", newAccount, oldAccount, appId, outEa, mappings);
//        if (!mappings.isEmpty() && mappings.get(0).getOutAccount().equals(newAccount)){
//            log.warn("updateByNewOutAccount failed. duplicate new account, newAccount:{}, oldAccount:{}, appId:{}, " +
//                    "outEa:{}, mappings:{}", newAccount, oldAccount, appId, outEa, mappings);
//            return 1;
//        }
//        return accountEmployeeBindDao.updateByNewOutAccount(newAccount, oldAccount, appId, outEa);
//    }
//
//    @Override
//    public int updateAccountByIsv(String newAccount,String oldAccount,String appId, String outEa) {
//        return accountEmployeeBindDao.updateAccountByIsv(newAccount, oldAccount, appId, outEa);
//    }
//
//    @Override
//    public void resumeStoppedEmployees(String source, String fsEa,String appId) {
//        List<QyweixinAccountEmployeeMapping> entityList = accountEmployeeBindDao.queryAccountBindByFsEa("E."+fsEa+".", appId, null);
//        log.info("QyweixinAccountBindServiceImpl.resumeStoppedEmployees,entityList={}",entityList);
//        if (entityList != null && entityList.size() > 0) {
//            List<String> outAccountList = new ArrayList<>();
//            for (QyweixinAccountEmployeeMapping item : entityList) {
//                if(item.getStatus()==0) continue;
//                outAccountList.add(item.getOutAccount());
//            }
//            if(outAccountList.size()>0) {
//                accountEmployeeBindDao.changeEmployeeStatus(source,fsEa,appId,outAccountList,0);
//            }
//            log.info("QyweixinAccountBindServiceImpl.resumeStoppedEmployees,resume stopped employee finished");
//        }
//    }
//
//    @Override
//    public void resumeStoppedDepartments(String source, String fsEa,String appId) {
//        QyweixinAccountDepartmentMapping entity = new QyweixinAccountDepartmentMapping();
//        entity.setSource(source);
//        entity.setFsEa(fsEa);
//        entity.setAppId(appId);
//        entity.setStatus(1);
//        List<QyweixinAccountDepartmentMapping> entityList = accountDepartmentBindDao.findByEntity(entity);
//        log.info("QyweixinAccountBindServiceImpl.resumeStoppedDepartments,entityList={}",entityList);
//        if (entityList != null && entityList.size() > 0) {
//            List<String> outDepartmentIdList = new ArrayList<>();
//            for (QyweixinAccountDepartmentMapping item : entityList) {
//                outDepartmentIdList.add(item.getOutDepartmentId());
//            }
//            accountDepartmentBindDao.changeDepartmentStatus(source,fsEa,appId,outDepartmentIdList,0);
//            log.info("QyweixinAccountBindServiceImpl.resumeStoppedDepartments,resume stopped department finished");
//        }
//    }
//
//    @Override
//    public Result<Integer> bindOutAccountEnterpriseMapping(QyweixinAccountEnterpriseMapping arg) {
//        Integer enterpriseMapping = accountEnterpriseBindDao.updateEnterpriseMapping(arg);
//        return new Result<>(enterpriseMapping);
//    }
//
//    /**
//     * 查询corpId企业下的所有员工
//     * @param corpId
//     * @return
//     */
//    @Override
//    public Result<List<QyweixinAccountEmployeeMapping>> selectAll(String corpId) {
//        QyweixinAccountEmployeeMapping employeeMapping = new QyweixinAccountEmployeeMapping();
//        employeeMapping.setOutEa(corpId);
//        List<QyweixinAccountEmployeeMapping> mappingList = accountEmployeeBindDao.findByEntity(employeeMapping);
//        return new Result<>(mappingList);
//    }
//
//    /**
//     * 批量更新员工信息
//     * @param employeeMappingList
//     * @return
//     */
//    @Transactional(isolation = Isolation.SERIALIZABLE)
//    @Override
//    public Result<Integer> batchUpdateEmployeeMapping(List<QyweixinAccountEmployeeMapping> employeeMappingList) {
//        if(CollectionUtils.isEmpty(employeeMappingList)) return new Result<>(0);
//        int totalCount = 0;
//        for(QyweixinAccountEmployeeMapping mapping : employeeMappingList) {
//            int count = accountEmployeeBindDao.update(mapping);
//            log.info("QyweixinAccountBindServiceImpl.batchUpdateEmployeeMapping,count={}",count);
//            if(count>0) {
//                totalCount+=count;
//            }
//        }
//        return new Result<>(totalCount);
//    }
//
//    /**
//     * 更新企业信息
//     * @param enterpriseMapping
//     * @return
//     */
//    @Transactional(isolation = Isolation.SERIALIZABLE)
//    @Options(useGeneratedKeys = false)
//    @Override
//    public Result<Integer> updateQyweixinAccountEnterpriseMapping(QyweixinAccountEnterpriseMapping enterpriseMapping) {
//        if(ObjectUtils.isEmpty(enterpriseMapping)) return new Result<>(0);
//        int totalCount = 0;
//        int count = accountEnterpriseBindDao.updateQyweixinAccountEnterpriseMapping(enterpriseMapping);
//        log.info("QyweixinAccountBindServiceImpl.batchUpdateEnterpriseMapping,count={}",count);
//        if(count>0) {
//            totalCount+=count;
//        }
//        return new Result<>(totalCount);
//    }
//
//    /**
//     * 批量更新部门信息
//     * @return
//     */
//    @Transactional(isolation = Isolation.SERIALIZABLE)
//    @Override
//    public Result<Integer> batchUpdateDepartmentBind(String outEa, String openOutEa) {
//        int count = accountDepartmentBindDao.BatchUpdateDepartmentBind(outEa, openOutEa, "qywx");
//        log.info("QyweixinAccountBindServiceImpl.batchUpdateDepartmentBind,outEa={},count={}",outEa, count);
//        return new Result<>(count);
//    }
//
//    @Override
//    public Result<Boolean> isManualBinding(String fsEa, String eid) {
//        boolean isBinding = false;
//
//        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(eid);
//        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
//            return new Result<>(isBinding);
//        }
//        QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMappingList.get(0);
//        Integer type = enterpriseMapping.getBindType();
//        if(StringUtils.isEmpty(fsEa)) {
//            fsEa = enterpriseMapping.getFsEa();
//        }
//        log.debug("isManualBinding start. fsEa:{}, eid:{}, enterpriseMapping:{}", fsEa, eid, enterpriseMapping);
//        if (QYWXBindTypeEnum.OLD_CORP_BIND.getCode().equals(type) && !ConfigCenter.EMPLOYEE_SYNC_EA.contains(fsEa)) {
//            isBinding = true;
//        }
//        return new Result<>(isBinding);
//    }
//
//    @Override
//    public Result<List<QyweixinAccountEnterpriseMapping>> queryEnterpriseMappingByBindType(Integer bindType) {
//        return new Result<>(accountEnterpriseBindDao.queryEnterpriseMappingByBindType(bindType, ConfigCenter.crm_domain));
//    }
//
//    @Deprecated
//    @Override
//    public Result<List<QyweixinAccountEmployeeMapping>> batchGetEmployeeMapping(String fsEa, String appId) {
//        return batchGetEmployeeMapping2(fsEa, appId, null);
//    }
//
//    @Override
//    public Result<List<QyweixinAccountEmployeeMapping>> batchGetEmployeeMapping2(String fsEa, String appId, String outEa) {
//        if(StringUtils.isEmpty(appId)) {
//            String mainAppId = qyweixinGatewayInnerService.getMainAppId(outEa).getData();
//            appId = mainAppId;
//        }
//        List<QyweixinAccountEmployeeMapping> entityList = accountEmployeeBindDao.queryAccountBindByFsEa("E."+fsEa+".", appId, outEa);
//        return new Result<>(entityList);
//    }

    @Override
    public Result<Integer> updateEnterpriseBindStatus(String fsEa, BindStatusEnum status) {
        //查询之前的状态，如果是create，证明是创建企业后的更新状态，需要发送mq
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByFsEa(ChannelEnum.qywx, fsEa);
        log.info("QyweixinAccountBindServiceImpl.updateEnterpriseBindStatus,enterpriseBindEntities={}", enterpriseBindEntities);
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>(0);
        }

        OuterOaEnterpriseBindEntity enterpriseBindEntity = enterpriseBindEntities.get(0);
        BindStatusEnum oldStatus = enterpriseBindEntity.getBindStatus();
        enterpriseBindEntity.setBindStatus(status);
        Integer count = outerOaEnterpriseBindManager.updateById(enterpriseBindEntity);
        if(oldStatus.equals(BindStatusEnum.create)) {
            //绑定企业成发送mq
            log.info("QyweixinAccountBindServiceImpl.updateEnterpriseBindStatus,fsEa={}", fsEa);
            Message message = new Message();
            QyweixinEnterpriseBindArg body = new QyweixinEnterpriseBindArg();
            body.setEa(fsEa);
            body.setCorpId(enterpriseBindEntity.getOutEa());
            message.setBody(body.toProto());
            if (ConfigCenter.TEM_CLOUD_EA.contains(fsEa)) {
                CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
                cloudMessageProxyProto.setType(CloudProxyEnum.enterpriseAccountBindSender.name());
                cloudMessageProxyProto.setCorpId(enterpriseBindEntity.getOutEa());
                cloudMessageProxyProto.setFsEa(fsEa);
                cloudMessageProxyProto.setMessage(message);
                // 跨云
                mqSender.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(fsEa), cloudMessageProxyProto);
            } else {
                SendResult sendResult = enterpriseAccountBindSender.send(message, enterpriseBindEntity.getOutEa());
                log.info("QyweixinAccountBindServiceImpl.updateEnterpriseBindStatus,sendResult={},message={}.", sendResult, message);
            }
        }
        return new Result<>(count);
    }

//    @Deprecated
//    @Override
//    public Result<Integer> batchUpdateFsDepBindStatus(String fsEa, List<String> fsDepIdList, int status,String appId) {
//        return batchUpdateFsDepBindStatus2(fsEa, fsDepIdList, status, appId, null);
//    }
//
//    @Override
//    public Result<Integer> batchUpdateFsDepBindStatus2(String fsEa, List<String> fsDepIdList, int status, String appId, String outEa) {
//        return new Result<>(accountDepartmentBindDao.batchUpdateFsDepBindStatus(fsEa,fsDepIdList,status,appId,outEa));
//    }
//
//    @Override
//    public Result<Integer> batchUpdateOutDepBindStatus(String fsEa, List<String> outDepIdList, int status, String appId) {
//        return new Result<>(accountDepartmentBindDao.changeDepartmentStatus(SourceTypeEnum.QYWX.getSourceType(),fsEa,appId,outDepIdList,status));
//    }

    @Override
    public Result<Integer> updateEmployeeBindStatus(OuterOaEmployeeBindEntity employeeBindEntity) {
        //查询之前的状态，如果是100，证明是创建企业后的更新状态，需要发送mq
        OuterOaEmployeeBindEntity OldEmployeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(ChannelEnum.qywx, employeeBindEntity.getOutEa(), employeeBindEntity.getFsEa(), employeeBindEntity.getAppId(), employeeBindEntity.getOutEmpId());
        String outEa = employeeBindEntity.getOutEa();
        String fsEa = employeeBindEntity.getFsEa();
        String appId = employeeBindEntity.getAppId();
        Integer count = outerOaEmployeeBindManager.updateById(employeeBindEntity);
        if(ObjectUtils.isNotEmpty(OldEmployeeBindEntity) && OldEmployeeBindEntity.getBindStatus().equals(BindStatusEnum.create)) {
            String fsUserId = employeeBindEntity.getFsEmpId();
            String outUserId = employeeBindEntity.getOutEmpId();
            Message message = new Message();
            message.setTags(ConfigCenter.EMPLOYEE_BIND);
            QyweixinEmpBindArg body = new QyweixinEmpBindArg();
            body.setEa(fsEa);
            body.setCorpId(outEa);
            body.setFsUserId(fsUserId);
            body.setQwUserId(outUserId);
            body.setAppId(appId);
            message.setBody(body.toProto());
            if(ConfigCenter.TEM_CLOUD_EA.contains(fsEa)) {
                CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
                cloudMessageProxyProto.setType(CloudProxyEnum.employeeAccountBindSender.name());
                cloudMessageProxyProto.setCorpId(outEa);
                cloudMessageProxyProto.setFsEa(fsEa);
                cloudMessageProxyProto.setMessage(message);
                //跨云
                mqSender.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(fsEa), cloudMessageProxyProto);
            } else {
                SendResult sendResult = employeeAccountBindSender.send(message, outEa);
                log.info("QyweixinAccountBindServiceImpl.updateEmployeeBindStatus,sendResult={},message={}.", sendResult, message);
            }
        }
        return new Result<>(count);
    }

//    @Override
//    public Result<Integer> batchUpdateFsEmpBindStatus(List<String> fsAccountList,
//                                                      int status,
//                                                      String appId) {
//        return batchUpdateFsEmpBindStatus2(fsAccountList, status, appId, null);
//    }
//
//    @Override
//    public Result<Integer> batchUpdateFsEmpBindStatus2(List<String> fsAccountList, int status, String appId, String outEa) {
//        return new Result<>(accountEmployeeBindDao.batchUpdateFsEmpBindStatus(fsAccountList, status,appId,outEa));
//    }
//
//    @Override
//    public Result<Integer> batchUpdateOutEmpBindStatus(String fsEa, List<String> outAccountList, int status, String appId) {
//        return new Result<>(accountEmployeeBindDao.changeEmployeeStatus(SourceTypeEnum.QYWX.getSourceType(),fsEa,appId,outAccountList, status));
//    }
//
//    @Override
//    public Result<List<QyweixinAccountEmployeeMapping>> findEmployeeBinds(List<QyweixinAccountEmployeeMapping> employeeMappings) {
//        List<QyweixinAccountEmployeeMapping> allEmployeeMappings = new LinkedList<>();
//        for(QyweixinAccountEmployeeMapping mapping : employeeMappings) {
//            List<QyweixinAccountEmployeeMapping> employeeMappingList = accountEmployeeBindDao.findByEntity(mapping);
//            if(CollectionUtils.isNotEmpty(employeeMappingList)) {
//                allEmployeeMappings.addAll(employeeMappingList);
//            }
//        }
//        return new Result<>(allEmployeeMappings.stream().distinct().collect(Collectors.toList()));
//    }
//
//    @Override
//    public Result<Integer> saveEmployeeBinds(List<QyweixinAccountEmployeeMapping> employeeMappings) {
//        int sum = 0;
//        for(QyweixinAccountEmployeeMapping mapping : employeeMappings) {
//            mapping.setIsvAccount(mapping.getOutAccount());
//            int count = accountEmployeeBindDao.save(mapping);
//            sum = sum + count;
//        }
//        return new Result<>(sum);
//    }
//
//    @Override
//    public Result<Integer> updateEmployeeBinds(List<QyweixinAccountEmployeeMapping> employeeMappings) {
//        int sum = 0;
//        for(QyweixinAccountEmployeeMapping mapping : employeeMappings) {
//            mapping.setIsvAccount(mapping.getOutAccount());
//            int count = accountEmployeeBindDao.updateEmployeeBindMapping(mapping);
//            sum = sum + count;
//        }
//        return new Result<>(sum);
//    }
//
//    @Override
//    public Result<List<QyweixinAccountDepartmentMapping>> findDepartmentBinds(List<QyweixinAccountDepartmentMapping> departmentMappings) {
//        List<QyweixinAccountDepartmentMapping> allDepartmentMappings = new LinkedList<>();
//        for(QyweixinAccountDepartmentMapping mapping : departmentMappings) {
//            List<QyweixinAccountDepartmentMapping> departmentMappingList = accountDepartmentBindDao.findByEntity(mapping);
//            if(CollectionUtils.isNotEmpty(departmentMappingList)) {
//                allDepartmentMappings.addAll(departmentMappingList);
//            }
//        }
//        return new Result<>(allDepartmentMappings.stream().distinct().collect(Collectors.toList()));
//    }
//
//    @Override
//    public Result<Integer> saveDepartmentBinds(List<QyweixinAccountDepartmentMapping> departmentMappings) {
//        int sum = 0;
//        for(QyweixinAccountDepartmentMapping mapping : departmentMappings) {
//            int count = accountDepartmentBindDao.save(mapping);
//            sum = sum + count;
//        }
//        return new Result<>(sum);
//    }
//
//    @Override
//    public Result<Integer> updateDepartmentBinds(List<QyweixinAccountDepartmentMapping> departmentMappings) {
//        int sum = 0;
//        for(QyweixinAccountDepartmentMapping mapping : departmentMappings) {
//            int count = accountDepartmentBindDao.updateDepartmentBindMapping(mapping);
//            sum = sum + count;
//        }
//        return new Result<>(sum);
//    }
//
//    @Override
//    public Result<List<QyweixinEmployeeAccountModel>> queryRepeatEmployees() {
//        return new Result<>(accountEmployeeBindDao.queryRepeatEmployees());
//    }

    @Override
    public Result<BatchEmpBindResult> batchGetEmpBind(BatchGetEmpBindArg arg) {
        //查询企业绑定关系
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(arg.getOutEa()).fsEa(arg.getFsEa()).appId(arg.getAppId()).build());
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<BatchEmpBindResult>().addError(ErrorRefer.BIND_ERROR.getCode());
        }
        //查询员工绑定关系
        List<OuterOaEmployeeBindEntity> employeeBindEntities;
        if (CollectionUtils.isNotEmpty(arg.getOutEmpIds())) {
            employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, arg.getOutEa(), arg.getFsEa(), arg.getAppId(), null, arg.getOutEmpIds());
        } else {
            employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, arg.getOutEa(), arg.getFsEa(), arg.getAppId(), arg.getFsEmpIds(), null);
        }

        log.info("QyweixinAccountBindServiceImpl.batchGetEmpBind.employeeBindEntities={}", employeeBindEntities);

        return new Result<>(swapBatchEmpBindResult(employeeBindEntities));
    }

    public BatchEmpBindResult swapBatchEmpBindResult(List<OuterOaEmployeeBindEntity> employeeBindEntities) {
        BatchEmpBindResult batchEmpBindResult = new BatchEmpBindResult();
        if (CollectionUtils.isEmpty(employeeBindEntities)) {
            return batchEmpBindResult;
        }
        List<BatchEmpBindResult.EmpBindResult> empBindResults = new LinkedList<>();
        for (OuterOaEmployeeBindEntity entity : employeeBindEntities) {
            BatchEmpBindResult.EmpBindResult  empBindResult = new BatchEmpBindResult.EmpBindResult();
            empBindResult.setFsEa(entity.getFsEa());
            empBindResult.setOutEa(entity.getOutEa());
            empBindResult.setAppId(entity.getAppId());
            empBindResult.setFsEmpId(entity.getFsEmpId());
            empBindResult.setOutEmpId(entity.getOutEmpId());
            empBindResults.add(empBindResult);
        }
        batchEmpBindResult.setEmpBindResults(empBindResults);
        return batchEmpBindResult;
    }
}
