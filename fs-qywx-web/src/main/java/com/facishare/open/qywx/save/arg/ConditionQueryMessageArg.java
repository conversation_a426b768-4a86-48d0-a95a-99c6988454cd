package com.facishare.open.qywx.save.arg;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/3/25 11:29
 * @Version 1.0
 */
@Data
public class ConditionQueryMessageArg implements Serializable {
    private String fsEa;
    private Integer empId;
    private String senderIds;
    private String receiveIds;//传对应的外部联系人id
    private Integer pageNum=1;//当前页
    private Integer pageSize=20;//每页的数量
    private Integer labelValue;//跟北京前端约定的value
    private String roomId;
    /**
     * [{
     *                     label: '最近10条', value: 1
     *                 },{
     *                     label: '最近20条', value: 2
     *                 },{
     *                     label: '最近50条', value: 3
     *                 },{
     *                     label: '最近100条', value: 4
     *                 },{
     *                     label: '最近1小时', value: 5
     *                 },{
     *                     label: '最近4小时', value: 6
     *                 },{
     *                     label: '最近1天', value: 7
     *                 },{
     *                     label: '最近3天', value: 8
     *                 }]
     */
}
