package com.facishare.open.qywx.accountbind.model;

import com.github.mybatis.entity.IdEntity;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/7/16.
 */
@Data
@Table(name = "employee_account_bind")
public class QyweixinAccountEmployeeMapping extends IdEntity implements Serializable{
    private static final long serialVersionUID = -1L;

    private String source;    //合作方渠道：qywx
    /**
     * 应用ID.默认CRM应用ID
     */
    private String appId;
    private String fsAccount; //员工的纷享账号 如E.56305.1000
    private String outAccount;//员工的企业微信账号, qywx是userId.
    private String outEa;     //员工的企业id
    private Integer status;      //0-正常 1-停用 2-已删除 100-纷享企业未创建时的中间状态
    private String isvAccount;//员工的明文企业微信的账号
    private Timestamp gmtCreate;//创建时间
    private Timestamp gmtModified;//更新时间


    public boolean checkArg() {
        if(null == source || null == fsAccount || null == outAccount || null == outEa) {
            return false;
        }
        return true;
    }
}
