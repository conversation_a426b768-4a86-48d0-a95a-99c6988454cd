package com.facishare.open.qywx.save.result;


import lombok.Data;

import java.io.Serializable;

/**
 * <p>纷享SFA接口返回结果封装类</p>
 * @dateTime 2018/7/13 14:50
 * <AUTHOR> <EMAIL>
 * @version 1.0
 */
@Data
public class QywxResult implements Serializable {

    /** 返回码 **/
    private Integer errcode;

    /** 返回描叙 **/
    private String errmsg;


    public boolean isSuccess() {
        if (0== errcode) {
            return true;
        }
        return false;
    }


}
