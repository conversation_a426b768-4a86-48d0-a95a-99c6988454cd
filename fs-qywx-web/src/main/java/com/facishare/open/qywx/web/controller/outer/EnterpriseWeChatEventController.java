package com.facishare.open.qywx.web.controller.outer;

import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountsync.model.EnterpriseWeChatEventProto;
import com.facishare.open.qywx.web.template.QyweixinOuterRecvCmdEventHandlerTemplate;
import com.facishare.open.qywx.web.template.QyweixinOuterRecvDataEventHandlerTemplate;
import com.facishare.open.qywx.web.template.QyweixinOuterRecvSystemEventHandlerTemplate;
import com.facishare.open.qywx.web.template.QyweixinOuterRepMsgEventHandlerTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * 接收企业微信回调数据
 * Created by liuwei on 2018/07/18
 */
@Slf4j
@RestController
@RequestMapping(value ="qyweixin")
public class EnterpriseWeChatEventController {
    @Resource
    private QyweixinOuterRecvCmdEventHandlerTemplate qyweixinOuterRecvCmdEventHandlerTemplate;

    @Resource
    private QyweixinOuterRecvDataEventHandlerTemplate qyweixinOuterRecvDataEventHandlerTemplate;

    @Resource
    private QyweixinOuterRepMsgEventHandlerTemplate qyweixinOuterRepMsgEventHandlerTemplate;

    @Resource
    private QyweixinOuterRecvSystemEventHandlerTemplate qyweixinOuterRecvSystemEventHandlerTemplate;

    /**
     * 接收企业微信消息事件。
     *
     * 这里有很多事件。包括ticket接收，授权成功/变更通知，通讯录变更通知
     * */
    @RequestMapping(value = "/recvCmdEvent")
    public String recvCmdEvent(@RequestParam(value = "msg_signature") String msgSignature,
                               @RequestParam(value = "timestamp") String timeStamp,
                               @RequestParam(value = "nonce") String nonce,
                               @RequestParam(value = "echostr", required = false) String echostr,
                               @RequestParam(required = false) String appID,
                               @RequestBody(required = false) String postData) {

        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace(traceId);

        log.info("EnterpriseWeChatEventController.recvCmdEvent,msgSignature:{}, timeStamp:{}, nonce:{}, postData:{}, echostr:{},appID:{}",
                msgSignature, timeStamp, nonce, postData, echostr,appID);

        EnterpriseWeChatEventProto eventProto = new EnterpriseWeChatEventProto();
        eventProto.setSignature(msgSignature);
        eventProto.setTimestamp(timeStamp);
        eventProto.setNonce(nonce);
        eventProto.setEchoStr(echostr);
        eventProto.setData(postData);
        eventProto.setAppId(appID);

        TemplateResult templateResult = qyweixinOuterRecvCmdEventHandlerTemplate.execute(eventProto);
        return ObjectUtils.isNotEmpty(templateResult.getData()) ? templateResult.getData().toString() : "success";
    }

    /**
     * 企微登录授权回调URL
     *
     * @param msgSignature
     * @param timeStamp
     * @param nonce
     * @param echostr
     * @param appID
     * @param postData
     * @return
     */
    @RequestMapping(value = "/recvCmdEvent2")
    public String recvCmdEvent2(@RequestParam(value = "msg_signature") String msgSignature,
                               @RequestParam(value = "timestamp") String timeStamp,
                               @RequestParam(value = "nonce") String nonce,
                               @RequestParam(value = "echostr", required = false) String echostr,
                               @RequestParam(required = false) String appID,
                               @RequestBody(required = false) String postData) {

        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace(traceId);

        log.info("EnterpriseWeChatEventController.recvCmdEvent2,msgSignature:{}, timeStamp:{}, nonce:{}, postData:{}, echostr:{},appID:{}",
                msgSignature, timeStamp, nonce, postData, echostr, appID);

        EnterpriseWeChatEventProto eventProto = new EnterpriseWeChatEventProto();
        eventProto.setSignature(msgSignature);
        eventProto.setTimestamp(timeStamp);
        eventProto.setNonce(nonce);
        eventProto.setEchoStr(echostr);
        eventProto.setData(postData);
        eventProto.setAppId(appID);

        TemplateResult templateResult = qyweixinOuterRecvCmdEventHandlerTemplate.execute(eventProto);
        return ObjectUtils.isNotEmpty(templateResult.getData()) ? templateResult.getData().toString() : "success";
    }

    /**
     * 接收企业微信用户从应用session上行的消息。
     * */
    @RequestMapping(value = "/recvDataEvent")
    public String recvDataEvent(@RequestParam(value = "msg_signature") String msgSignature,
                                @RequestParam(value = "timestamp") String timeStamp,
                                @RequestParam(value = "nonce") String nonce,
                                @RequestParam(value = "echostr", required = false) String echostr,
                                @RequestParam(required = false) String appID,
                                @RequestBody(required = false) String postData) {

        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace(traceId);

        log.info("EnterpriseWeChatEventController.recvDataEvent,msgSignature:{}, timeStamp:{}, nonce:{}, postData:{}, echostr:{}",
                msgSignature, timeStamp, nonce, postData, echostr);

        EnterpriseWeChatEventProto eventProto = new EnterpriseWeChatEventProto();
        eventProto.setSignature(msgSignature);
        eventProto.setTimestamp(timeStamp);
        eventProto.setNonce(nonce);
        eventProto.setEchoStr(echostr);
        eventProto.setData(postData);
        eventProto.setAppId(appID);

        TemplateResult templateResult = qyweixinOuterRecvDataEventHandlerTemplate.execute(eventProto);
        return ObjectUtils.isNotEmpty(templateResult.getData()) ? templateResult.getData().toString() : "success";
    }

    /**
     * 接收企业微信代开发应用事件。
     *
     * 这里有很多事件。包括ticket接收，授权成功，更改secret
     * */
    @RequestMapping(value = "/repMsgEvent")
    public String repMsgEvent(@RequestParam(value = "msg_signature") String msgSignature,
                               @RequestParam(value = "timestamp") String timeStamp,
                               @RequestParam(value = "nonce") String nonce,
                               @RequestParam(value = "echostr", required = false) String echostr,
                               @RequestParam(required = false) String appID,
                               @RequestBody(required = false) String postData) {
        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace(traceId);

        log.info("ControllerQyweixinCallback.repMsgEvent,msgSignature={}, timeStamp={}, nonce={}, postData={}, echostr={},appID={}",
                msgSignature, timeStamp, nonce, postData, echostr,appID);


        EnterpriseWeChatEventProto eventProto = new EnterpriseWeChatEventProto();
        eventProto.setSignature(msgSignature);
        eventProto.setTimestamp(timeStamp);
        eventProto.setNonce(nonce);
        eventProto.setEchoStr(echostr);
        eventProto.setData(postData);
        eventProto.setAppId(appID);

        TemplateResult templateResult = qyweixinOuterRepMsgEventHandlerTemplate.execute(eventProto);
        return ObjectUtils.isNotEmpty(templateResult.getData()) ? templateResult.getData().toString() : "success";
    }

    /**
     * 系统事件接收URL
     * 应用管理 -通用开发参数-系统事件接收URL
     * */
    @RequestMapping(value = "/recvSystemDataUrl")
    public String recvSystemDataUrl(@RequestParam(value = "msg_signature") String msgSignature,
                                @RequestParam(value = "timestamp") String timeStamp,
                                @RequestParam(value = "nonce") String nonce,
                                @RequestParam(value = "echostr", required = false) String echostr,
                                @RequestParam(required = false) String appID,
                                @RequestBody(required = false) String postData) {

        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace(traceId);

        log.info("EnterpriseWeChatEventController.recvDataEvent,msgSignature:{}, timeStamp:{}, nonce:{}, postData:{}, echostr:{}",
                msgSignature, timeStamp, nonce, postData, echostr);

        EnterpriseWeChatEventProto eventProto = new EnterpriseWeChatEventProto();
        eventProto.setSignature(msgSignature);
        eventProto.setTimestamp(timeStamp);
        eventProto.setNonce(nonce);
        eventProto.setEchoStr(echostr);
        eventProto.setData(postData);
        eventProto.setAppId(appID);

        TemplateResult templateResult = qyweixinOuterRecvSystemEventHandlerTemplate.execute(eventProto);
        return ObjectUtils.isNotEmpty(templateResult.getData()) ? templateResult.getData().toString() : "success";
    }
}
