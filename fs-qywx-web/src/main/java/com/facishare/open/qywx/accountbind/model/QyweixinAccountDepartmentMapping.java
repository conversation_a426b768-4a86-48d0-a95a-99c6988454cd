package com.facishare.open.qywx.accountbind.model;

import com.github.mybatis.entity.IdEntity;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 *  Created by <PERSON><PERSON><PERSON> on 2018/7/16.
 */
@Data
@Table(name = "department_account_bind")
public class QyweixinAccountDepartmentMapping extends IdEntity implements Serializable {
    private static final long serialVersionUID = -1L;
    private String source;   //合作方渠道：qywx
    private String fsEa;     //纷享企业账号
    /**
     * 应用ID
     */
    private String appId;
    private String outEa;    //企业微信corpId
    private Integer fsDepartmentId;//纷享部门id
    private String outDepartmentId;//企业微信部门id
    private Integer status;      //0-正常 1-停用

    private Timestamp gmtCreate;//创建时间
    private Timestamp gmtModified;//更新时间

    public boolean checkArg() {
        if(null == source || null == fsEa || null == outEa || null == fsDepartmentId || null == outDepartmentId) {
            return false;
        }
        return true;
    }
}
