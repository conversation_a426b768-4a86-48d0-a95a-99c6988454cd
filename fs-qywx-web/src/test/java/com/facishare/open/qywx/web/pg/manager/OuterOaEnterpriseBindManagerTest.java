package com.facishare.open.qywx.web.pg.manager;

import com.alibaba.fastjson2.JSON;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.admin.QywxConnectorVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class OuterOaEnterpriseBindManagerTest extends AbstractJUnit4SpringContextTests {

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Test
    public void insert() {
        // 创建 QyweixinEnterpriseConnectParams 对象
        QywxConnectorVo connectParams = new QywxConnectorVo();
        connectParams.setConnectorName("超人不会飞");
        connectParams.setQywxDepartmentId("1");
        connectParams.setDomain("www.ceshi112.com");
        connectParams.setIsFirstLand(true);
        connectParams.setIsRetainInformation(false);

        // 创建 OuterOaEnterpriseBindEntity 对象
        OuterOaEnterpriseBindEntity bindEntity = new OuterOaEnterpriseBindEntity();
        bindEntity.setChannel(ChannelEnum.qywx); // 渠道
        bindEntity.setFsEa("xkhdx003"); // 纷享企业 ea
        bindEntity.setOutEa("wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA"); // 外部企业 ea
        bindEntity.setAppId("dkdf3684b6720635f7"); // 外部应用 appId
        bindEntity.setConnectInfo(JSON.toJSONString(connectParams)); // 连接参数
        bindEntity.setBindType(BindTypeEnum.manual); // 绑定类型
        bindEntity.setBindStatus(BindStatusEnum.normal); // 绑定状态
        bindEntity.setCreateTime(System.currentTimeMillis()); // 创建时间
        bindEntity.setUpdateTime(System.currentTimeMillis()); // 修改时间

        // 插入数据
        int result = outerOaEnterpriseBindManager.insert(bindEntity);
        System.out.println(result);
    }
}
