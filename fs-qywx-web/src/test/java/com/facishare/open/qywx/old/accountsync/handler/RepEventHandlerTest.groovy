package com.facishare.open.qywx.old.accountsync.handler

import com.facishare.open.qywx.web.eventHandler.RepEventHandler
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class RepEventHandlerTest extends Specification {
    @Resource
    private RepEventHandler repEventHandler;

    def "handle"() {
        given:
        String msg = msgCase
        expect:
        repEventHandler.handle(msg, null)
        where:
        type  |   msgCase
        "foneshare"  |  "<xml><ToUserName><![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>**********</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[change_contact]]></Event><ChangeType><![CDATA[update_user]]></ChangeType><UserID><![CDATA[wowx1mDAAAT_Zt89-HkoCPIVNJfcwtWA]]></UserID><Alias><![CDATA[涂秋平测试1]]></Alias></xml>"
        "foneshare-gray"  |  "<xml><ToUserName><![CDATA[wpwx1mDAAALBq67t8rJnrC5I5n7r55ng]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>1701155899</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[change_contact]]></Event><ChangeType><![CDATA[update_user]]></ChangeType><UserID><![CDATA[wowx1mDAAAYk9Ox4AAqd4eYYxlS9vveQ]]></UserID><Alias><![CDATA[lisisi]]></Alias></xml>"
    }
}
