package com.facishare.open.qywx.old.accountsync.test;

import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.qywx.BaseTest;

import javax.annotation.Resource;

public class OuterOaEnterpriseBindManagerTest extends BaseTest {
    @Resource
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private OuterOaAppInfoManager outerOaAppInfoManager;

//    @Test
//    public void getEntity() {
//        OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.feishu, "90429", "16bdc45070d5975f", "appId");
//        System.out.println(entity);
//    }
//
//    @Test
//    public void intEntity() {
//        OuterOaEnterpriseBindEntity entity = new OuterOaEnterpriseBindEntity();
//        entity.setChannel(ChannelEnum.feishu);
//        entity.setFsEa("90429");
//        entity.setOutEa("16bdc45070d5975f");
//        entity.setAppId("appId1");
//        entity.setConnectInfo("{}");
//        entity.setBindType(BindTypeEnum.auto);
//        entity.setBindStatus(BindStatusEnum.normal);
//        entity.setCreateTime(System.currentTimeMillis());
//        entity.setUpdateTime(System.currentTimeMillis());
//
//        Integer c = outerOaEnterpriseBindManager.insert(entity);
//        System.out.println(c);
//    }
//
//    @Test
//    public void getAppEntity() {
//        OuterOaAppInfoEntity entity = new OuterOaAppInfoEntity();
//        entity.setId(IdGenerator.get());
//        entity.setChannel(ChannelEnum.qywx);
//        entity.setOutEa("wpwx1mDAAApDuHDla66rUas9XUWu0rdg");
//        entity.setAppId("dk2819cf1b932d7406");
//        entity.setAppType(OuterOaAppInfoTypeEnum.serviceRepDev);
//        QyweixinAppInfo qyweixinAppInfo = new QyweixinAppInfo();
//        qyweixinAppInfo.setAppName("代开发测试断网");
//        qyweixinAppInfo.setCorpName("贝贝CRM断网测试企业");
//        qyweixinAppInfo.setAppLogo("www.baidu.com");
//        qyweixinAppInfo.setShareType("1");
//        qyweixinAppInfo.setAgentId("1000007");
//        qyweixinAppInfo.setPermanentCode("yWY8pt9cwg6LPI6Pzg0qZjZtlRrCENG7i2Glz49EMmZoU58/7j+ld1JA6FQbjJ/y");
//        entity.setAppInfo(new Gson().toJson(qyweixinAppInfo));
//        entity.setStatus(OuterOaAppInfoStatusEnum.normal);
//        entity.setCreateTime(System.currentTimeMillis());
//        entity.setUpdateTime(System.currentTimeMillis());
//        Integer insert = outerOaAppInfoManager.insert(entity);
//        System.out.println(insert);
//    }
}
