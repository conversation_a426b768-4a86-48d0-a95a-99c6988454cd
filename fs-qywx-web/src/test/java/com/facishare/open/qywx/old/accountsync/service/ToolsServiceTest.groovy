package com.facishare.open.qywx.old.accountsync.service

import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource
import com.facishare.open.qywx.accountinner.service.ToolsService
import com.facishare.open.qywx.accountsync.arg.BatchCreateQywxConnectorArg
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class ToolsServiceTest extends Specification {

    @Autowired
    private ToolsService toolsService;
    @Autowired
    private RedisDataSource redisDataSource;

    def "queryFsEnterpriseOpen"() {
        expect:
        def result = toolsService.queryFsEnterpriseOpen("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
        println result;
    }

    def "queryFsEmployeeOpen"() {
        expect:
        def result = toolsService.queryFsEmployeeOpen("wpwx1mDAAAPMUmYv7SV55wPfDzCLZyVQ", "wowx1mDAAAdskoHGOGyFVZNesVHHgwDg");
        println result;
    }

//    def "queryEnterpriseBindType"() {
//        expect:
//        def result = toolsService.queryEnterpriseBindType("85666");
//        println result;
//    }

    def "queryFsEmployeeStatus"() {
        expect:
        def result = toolsService.queryFsEmployeeStatus("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ");
        println result;
    }

//    def "dealRepeatEmployees"() {
//        expect:
//        def result = toolsService.dealRepeatEmployees();
//        println result;
//    }
//
//    def "queryRepeatEmployees"() {
//        expect:
//        def result = toolsService.queryRepeatEmployees();
//        println result;
//    }
//
//    def "stopEmployee"() {
//        expect:
//        def result = toolsService.stopEmployee(85666, "1001");
//        println result;
//    }
//
//    def "dealEmpData"() {
//        expect:
//        def result = toolsService.dealEmpData(85666, "1002", "1001");
//        println result;
//    }
//
//    def "updateDeptBindStatus"() {
//        expect:
//        def result = toolsService.updateDeptBindStatus("xqykty8609", "999999", 0, "wx88a141937dd6f838");
//        println result;
//    }
//
//    def "pushCorpBindData2Cloud"() {
//        expect:
//        def result = toolsService.pushCorpBindData2Cloud("hhh");
//        println result;
//    }
//
//    def "pushEnterpriseData2Cloud"() {
//        expect:
//        def result = toolsService.pushEnterpriseData2Cloud("cgrh8585", "hhh");
//        println result;
//    }

    def "batchCreateQywxConnector"() {
        given:
        def arg = new BatchCreateQywxConnectorArg();
        arg.setBindList(new ArrayList());

        BatchCreateQywxConnectorArg.EnterpriseMapping enterpriseMapping = new BatchCreateQywxConnectorArg.EnterpriseMapping();
        enterpriseMapping.setOutEa("")
        enterpriseMapping.setOutEn("下游测试企业0913")
        enterpriseMapping.setFsEa("91355")

        arg.getBindList().add(enterpriseMapping);

        arg.setDataCenterName("企微连接器")
        arg.setUpstreamCorpId("wpwx1mDAAA0WQKpOLnXO6GcOzlxVY7ew")
        arg.setUpstreamAppId("dk3ff8a65e707ca3c2")
        arg.setUpstreamAgentId("1000002")

        expect:
        def result = toolsService.batchCreateQywxConnector(arg);
        println result;
    }
}
