package com.facishare.open.qywx.web.controller.outer;

import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinExternalContactInfo;
import com.facishare.open.qywx.accountsync.result.Result;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class ControllerOpenQYWeixinTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private ControllerOpenQYWeixin controllerOpenQYWeixin;

    @Test
    public void outAccountToFsAccountBatchTest() throws Exception {
        Result<List<QyweixinExternalContactInfo>> qyweixinExternalContact = controllerOpenQYWeixin.getQyweixinExternalContact(ImmutableMap.of("userIDs", Lists.newArrayList("wmwx1mDAAAEHC7C10PhMXCGverrKMlKQ")),
                "wx88a141937dd6f838", "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
        log.info("outAccountToFsAccountBatchTest result:{}", qyweixinExternalContact);
    }
}
