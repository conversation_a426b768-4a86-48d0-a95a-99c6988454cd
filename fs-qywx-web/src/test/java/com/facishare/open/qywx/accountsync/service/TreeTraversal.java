import java.util.*;

// 定义节点类
class TreeNode {
    int id;
    int parentId;
    int order;
    List<TreeNode> children;

    TreeNode(int id, int parentId, int order) {
        this.id = id;
        this.parentId = parentId;
        this.order = order;
        this.children = new ArrayList<>();
    }

    // 添加子节点
    void addChild(TreeNode child) {
        this.children.add(child);
    }

    // 递归遍历树
    void traverse() {
        System.out.println("ID: " + id + ", Parent ID: " + parentId + ", Order: " + order);
        for (TreeNode child : children) {
            child.traverse();
        }
    }
}

public class TreeTraversal {
    public static void main(String[] args) {
        // 模拟 JSON 数据
        List<Map<String, Integer>> departments = new ArrayList<>();
        departments.add(createDepartment(1, 0, 100000000));
        departments.add(createDepartment(2, 1, 100000000));
        departments.add(createDepartment(5, 4, 100000000));
        departments.add(createDepartment(4, 3, 100000000));
        departments.add(createDepartment(6, 3, 100000000));

        // 构建树结构
        Map<Integer, TreeNode> nodeMap = new HashMap<>();
        TreeNode root = null;

        for (Map<String, Integer> dept : departments) {
            int id = dept.get("id");
            int parentId = dept.get("parentid");
            int order = dept.get("order");

            TreeNode node = new TreeNode(id, parentId, order);
            nodeMap.put(id, node);

            if (parentId == 0) {
                root = node; // 根节点
            } else {
                TreeNode parent = nodeMap.get(parentId);
                if (parent != null) {
                    parent.addChild(node);
                }
            }
        }

        // 遍历树结构
        if (root != null) {
            root.traverse();
        } else {
            System.out.println("No root node found!");
        }
    }

    // 辅助方法：创建部门 Map
    private static Map<String, Integer> createDepartment(int id, int parentId, int order) {
        Map<String, Integer> department = new HashMap<>();
        department.put("id", id);
        department.put("parentid", parentId);
        department.put("order", order);
        return department;
    }
}
