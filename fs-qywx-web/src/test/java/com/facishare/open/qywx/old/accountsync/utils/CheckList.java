package com.facishare.open.qywx.old.accountsync.utils;

import com.alibaba.dubbo.common.URL;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.qywx.accountsync.core.enums.QyweixinErrorCodeEnum;
import com.facishare.open.qywx.accountsync.model.QyweixinEnterpriseOrder;
import com.google.common.collect.Lists;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.text.StrSubstitutor;

import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by l<PERSON><PERSON> on 2018/07/27
 */
public class CheckList {

    /**
     * 计算列表aList相对于bList的增加的情况，兼容任何类型元素的列表数据结构
     * @param aList 本列表
     * @param bList 对照列表
     * @return 返回增加的元素组成的列表
     */
    public static <E> List<E> getAddaListThanbList(List<E> aList, List<E> bList){
        long st = System.currentTimeMillis();
        List<E> addList = new ArrayList<E>();
        for (int i = 0; i < aList.size(); i++){
            if(!myListContains(bList, aList.get(i))){
                addList.add(aList.get(i));
            }
        }
        System.out.println("getAddaListThanbList total times "+(System.currentTimeMillis()-st));
        return addList;
    }

    /**
     * 计算列表aList相对于bList的减少的情况，兼容任何类型元素的列表数据结构
     * @param aList 本列表
     * @param bList 对照列表
     * @return 返回减少的元素组成的列表
     */
    public static <E> List<E> getReduceaListThanbList(List<E> aList, List<E> bList){
        long st = System.currentTimeMillis();
        List<E> reduceaList = new ArrayList<E>();
        for (int i = 0; i < bList.size(); i++){
            if(!myListContains(aList, bList.get(i))){
                reduceaList.add(bList.get(i));
            }
        }
        System.out.println("getReduceaListThanbList total times "+(System.currentTimeMillis()-st));
        return reduceaList;

    }

    /**
     * 判断元素element是否是sourceList列表中的一个子元素
     * @param sourceList 源列表
     * @param element 待判断的包含元素
     * @return 包含返回 true，不包含返回 false
     */
    private static <E> boolean myListContains(List<E> sourceList, E element) {
        if (sourceList == null || element == null){
            return false;
        }
        if (sourceList.isEmpty()){
            return false;
        }
        for (E tip : sourceList){
            if(element.equals(tip)){
                return true;
            }
        }
        return false;
    }

    /**
     * 获取两个List的不同元素
     * @param list1
     * @param list2
     * @return
     */
    private static List<String> getDiffrent5(List<String> list1, List<String> list2) {
        long st = System.currentTimeMillis();
        List<String> diff = new ArrayList<String>();
        List<String> maxList = list1;
        List<String> minList = list2;
        if(list2.size()>list1.size())
        {
            maxList = list2;
            minList = list1;
        }
        Map<String,Integer> map = new HashMap<String,Integer>(maxList.size());
        for (String string : maxList) {
            map.put(string, 1);
        }
        for (String string : minList) {
            if(map.get(string)!=null)
            {
                map.put(string, 2);
                continue;
            }
            diff.add(string);
        }
        for(Map.Entry<String, Integer> entry:map.entrySet())
        {
            if(entry.getValue()==1)
            {
                diff.add(entry.getKey());
            }
        }
        System.out.println("getDiffrent5 total times "+(System.currentTimeMillis()-st));
        return diff;

    }

    public static void main(String[] args) throws UnsupportedEncodingException {
//        List<String> list1 = new ArrayList<String>();
//        List<String> list2 = new ArrayList<String>();
//        for (int i = 0; i < 10000; i++) {
//            list1.add("test"+i);
//            list2.add("test"+i*2);
//        }
//        getDiffrent5(list1,list2);
//        getAddaListThanbList(list1, list2);


        Map<String, String> textForm = new HashMap<>();
        String msgContent = "{'text':'dsfafa'}";
        textForm = JSONObject.parseObject(msgContent, new HashMap<>().getClass());
        System.out.println(textForm);
        System.out.println(JSONObject.toJSONString(textForm));


        System.out.println(new String("你好".getBytes("GBK"), Charset.forName("GBK")));
        System.out.println(new String("\\u627E\\u4E0D\\u5230\\u6B64ticket\\u7684\\u4FE1\\u606F".getBytes("GBK"), Charset.forName("UTF-8")));

        System.out.println(URL.encode("https://www.ceshi113.com/qyweixin/callback/oauth2?param=24234325"));
        System.out.println(URL.encode(URL.encode("https://www.ceshi112.com/hcrm/cloudhub/function/{}?id=xx&apiname=yy")));
        System.out.println(URL.encode("https://www.ceshi112.com/hcrm/cloudhub/function/{}?id=xx&apiname=yy"));
        System.out.println(Base64.encodeBase64String("https://www.ceshi112.com/hcrm/cloudhub/function/{}?id=xx&apiname=yy".getBytes()));
        System.out.println(new String(Base64.decodeBase64("aHR0cHM6Ly93d3cuY2VzaGkxMTIuY29tL2hjcm0vY2xvdWRodWIvZnVuY3Rpb24ve30/aWQ9eHgmYXBpbmFtZT15eQ==".getBytes())));
        System.out.println(URL.decode("http%3A%2F%2Flocalhost%3A8086%2Fqyweixin%2Fcallback%2Foauth2%3Fparam%3Dhttps%3A%2F%2Fwww.ceshi112.com%2Fhcrm%2Fcloudhub%2Ffunction%2F%7B%7D%3Fid%3Dxx%26apiname%3Dyy"));




        String appId= "wx4c7edab730f4fdc9";
        appId = "wx88a141937dd6f838";
        String messageUrl="%s/qyweixin/doFunction?param=%s&appID="+appId;
        String severPrefix = "http://open.ceshi113.com";
        String param = Base64.encodeBase64String("https://www.ceshi113.com/hcrm/wechat/function/todo?id=fde70df775104dfea9a831a6c0bac08b&apiname=MarketingEventObj".getBytes());
        messageUrl = String.format(messageUrl, severPrefix,  param);
        System.out.println(" messageUrl:" + messageUrl);

        QyweixinEnterpriseOrder qyweixinAddEnterprise = new QyweixinEnterpriseOrder();
        qyweixinAddEnterprise.setAdminList(Lists.newArrayList());
        qyweixinAddEnterprise.setAppId("43234524525");
        qyweixinAddEnterprise.setCorpId("3241234");

        byte[] result = qyweixinAddEnterprise.toProto();
        System.out.println(" qyweixinAddEnterprise :" + JSONObject.toJSONString(qyweixinAddEnterprise));

        QyweixinEnterpriseOrder qyweixinAddEnterpriseProto = new QyweixinEnterpriseOrder();
        qyweixinAddEnterpriseProto.fromProto(result);
        System.out.println(" toProto" + JSONObject.toJSONString(qyweixinAddEnterpriseProto));

        System.out.println(QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(0));

        String v= "E.74647.1016";
        System.out.println(Integer.valueOf(v.substring(v.lastIndexOf(".")+1)));

        System.out.println(new Integer(1).equals("1"));

//        字符串匹配
        Map<String, String> valuesMap = new HashMap<String, String>();
        valuesMap.put("animal", "quick brown fox");
        valuesMap.put("target", "lazy dog");
        String templateString = "The ${animal} jumped over the ${target}.";
        String replace = new StrSubstitutor(valuesMap).replace(templateString);
        System.out.println(replace);
    }
}
