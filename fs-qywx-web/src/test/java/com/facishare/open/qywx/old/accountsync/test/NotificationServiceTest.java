package com.facishare.open.qywx.old.accountsync.test;

import com.facishare.open.qywx.accountinner.arg.SendTextNoticeArg;
import com.facishare.open.qywx.accountinner.service.NotificationService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class NotificationServiceTest {

    @Autowired
    private NotificationService notificationService;

    @Test
    public void sendQYWXNoticeTest() {
        SendTextNoticeArg arg = new SendTextNoticeArg();
        arg.setEnterpriseAccount("84883");
        arg.setTenantId("84883");
        arg.setReceivers(Lists.newArrayList(1021));
        arg.setMsgTitle("【测试】警告推送");
        arg.setMsg("【测试】警告内容");
        notificationService.sendQYWXNotice(arg);
    }
}
