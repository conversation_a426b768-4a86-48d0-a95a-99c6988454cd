package com.facishare.open.qywx.web.mq.listener;

import com.alibaba.fastjson.JSON;
import com.facishare.enterprise.event.EnterpriseAddEvent;
import com.facishare.enterprise.event.EnterpriseEventType;
import com.facishare.open.order.contacts.proxy.api.utils.ProtoUtil;
import com.facishare.open.qywx.web.job.AutoGetUserAndDepartmentInfoHandler;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class EnterpriseEventListenerTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private EnterpriseEventListener enterpriseEventListener;

    @Test
    public void test() throws Exception {
        MessageExt msg = new MessageExt();
        msg.setFlag(EnterpriseEventType.EnterpriseAdd.getType());
        msg.setMsgId("test0000000001");
        //{enterpriseId=93408, enterpriseName='测试1202', enterpriseShortName='测试1202', enterpriseAccount='cs7115', source=10, accountTotalAmount=1, createTime=*************}
        String json = "{\"enterpriseId\":93408,\"enterpriseName\":\"测试1202\",\"enterpriseShortName\":\"测试1202\",\"enterpriseAccount\":\"cs7115\",\"source\":10,\"accountTotalAmount\":1,\"createTime\":*************}";
        EnterpriseAddEvent enterpriseAddEvent = JSON.parseObject(json, EnterpriseAddEvent.class);
        msg.setBody(ProtoUtil.toProto(enterpriseAddEvent));
        //{"CONSUME_START_TIME":"*************","uniqId":"67eca389c930ae0001e020ef","UNIQ_KEY":"0A221BDC00015034EEA407706FCD9CCA","CLUSTER":"FS-MQ","traceId":"fs-plat-user-login-cgi/fstest/67c7f1afc930ae0001d60fa9","ei":"93408","MIN_OFFSET":"22","sourceApp":"fs-plat-user-login-cgi","locale":"zh-CN","MAX_OFFSET":"24"}
        //转成setProperties(Map<String, String> properties)

        ConsumeConcurrentlyStatus consumeMessage = enterpriseEventListener.consumeMessage(Lists.newArrayList(msg), null);
        log.info("consumeMessage={}",consumeMessage);
    }
}
