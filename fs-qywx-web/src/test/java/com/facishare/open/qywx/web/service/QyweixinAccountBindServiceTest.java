package com.facishare.open.qywx.web.service;

import com.alibaba.fastjson.JSON;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.result.EmployeeAccountMatchResult;
import com.facishare.open.qywx.accountbind.result.Result;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class QyweixinAccountBindServiceTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private QyweixinAccountBindService qyweixinAccountBindService;

    @Test
    public void test() throws Exception {
        String x = "{\"id\":\"67f613299c90100001b78ac6\",\"channel\":\"qywx\",\"fsEa\":\"lgg6737\",\"outEa\":\"wpwx1mDAAAOvtbJxYM8CScaAVqKJlC-Q\",\"appId\":\"wx88a141937dd6f838\",\"connectInfo\":null,\"bindType\":\"auto\",\"bindStatus\":\"normal\",\"createTime\":*************,\"updateTime\":*************}";
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = JSON.parseObject(x, OuterOaEnterpriseBindEntity.class);
        qyweixinAccountBindService.bindAccountEnterpriseMapping(outerOaEnterpriseBindEntity);
    }

    @Test
    public void outAccountToFsAccountBatch() throws Exception {
        Result<Map<String, String>> mapResult = qyweixinAccountBindService.outAccountToFsAccountBatch("qywx", "84883", ConfigCenter.crmAppId,
                Lists.newArrayList("wpwx1mDAAAOvtbJxYM8CScaAVqKJlC-Q", "wowx1mDAAAfPMspC9iKzWLx16d_zjcRA", "wowx1mDAAAmQjySE6h53NoAT1qbgQN8g"));
        System.out.println(mapResult);
    }

    @Test
    public void fsAccountToOutAccountBatch() throws Exception {
        Result<Map<String, String>> mapResult = qyweixinAccountBindService.fsAccountToOutAccountBatch("qywx", ConfigCenter.crmAppId,
                Lists.newArrayList("E.84883.1021", "E.84883.1000"));
        System.out.println(mapResult);
    }

    @Test
    public void fsAccountToOutAccountBatch2() throws Exception {
        Result<List<EmployeeAccountMatchResult>> listResult = qyweixinAccountBindService.fsAccountToOutAccountBatch2("qywx", ConfigCenter.crmAppId,
                Lists.newArrayList("E.84883.1021", "E.84883.1000"), "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
        System.out.println(listResult);
    }

    @Test
    public void outEaToFsEa() throws Exception {
        Result<String> stringResult = qyweixinAccountBindService.outEaToFsEa("qywx", "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", null);
        System.out.println(stringResult);
    }

    @Test
    public void fsEaToOutEa() throws Exception {
        Result<String> stringResult = qyweixinAccountBindService.fsEaToOutEa("qywx", "84883");
        System.out.println(stringResult);
    }

    @Test
    public void fsEaToOutEaResultList() throws Exception {
        Result<List<QyweixinAccountEnterpriseMapping>> stringResult = qyweixinAccountBindService.fsEaToOutEaResultList("qywx", "84883");
        System.out.println(stringResult);
    }
}
