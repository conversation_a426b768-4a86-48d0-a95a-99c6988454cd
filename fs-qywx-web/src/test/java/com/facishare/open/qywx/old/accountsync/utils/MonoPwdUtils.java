package com.facishare.open.qywx.old.accountsync.utils;

import com.fxiaoke.common.PasswordUtil;
import com.google.common.net.UrlEscapers;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MonoPwdUtils {
    private static final Pattern MONGO_URI = Pattern.compile("mongodb://((.+):(.*)@)");
    private static String decodePassword(final String servers) {
        String uri = servers;
        Matcher m = MONGO_URI.matcher(servers);
        if (m.find()) {
            try {
                String pwd = UrlEscapers.urlFormParameterEscaper().escape(PasswordUtil.decode(m.group(3)));
                uri = servers.substring(0, m.end(2) + 1) + pwd + servers.substring(m.end(1) - 1);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return uri;
    }

    public static void main(String[] args) {
        String pwd = PasswordUtil.decode("F0BC3B214612D729F06B283051F262FE92797B3849C1B007903C60C37D163F66");
        System.out.println(pwd);
    }
}
