package com.facishare.open.qywx.web.service;

import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.save.arg.QywxQueryMessageArg;
import com.facishare.open.qywx.save.arg.QywxQueryMessageArgByEa;
import com.facishare.open.qywx.save.result.QueryMessageIdResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class QyweixinAccountSyncServiceTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private QyweixinAccountSyncService qyweixinAccountSyncService;

    @Test
    public void test() throws Exception {
        QywxQueryMessageArgByEa qywxQueryMessageArg = new QywxQueryMessageArgByEa();
        qywxQueryMessageArg.setFsEa("84883");
        Result<QueryMessageIdResult> queryMessageIdResultResult =
                qyweixinAccountSyncService.queryQywxSessionMessage(qywxQueryMessageArg);
        log.info("queryMessageIdResultResult={}", queryMessageIdResultResult);
    }

    @Test
    public void test1() throws Exception {
        Result<Void> voidResult = qyweixinAccountSyncService.triggerActiveCodes("84883", "dd", "dd");
        log.info("voidResult={}", voidResult);
    }
}
