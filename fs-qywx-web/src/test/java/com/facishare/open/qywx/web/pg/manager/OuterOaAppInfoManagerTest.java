package com.facishare.open.qywx.web.pg.manager;

import com.alibaba.fastjson2.JSON;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.params.QyweixinAppInfoParams;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class OuterOaAppInfoManagerTest extends AbstractJUnit4SpringContextTests {

    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;

    @Test
    public void insert() {
        // 创建 QyweixinAppInfoParams 对象
        QyweixinAppInfoParams appInfoParams = new QyweixinAppInfoParams();
        appInfoParams.setPermanentCode("0G-O4_HVLKv17T5_0VH6akLXSazpdXU1nTIXJVDyScY");

        // 设置授权企业信息
        QyweixinAppInfoParams.AuthCorpInfo authCorpInfo = new QyweixinAppInfoParams.AuthCorpInfo();
        authCorpInfo.setCorpName("测试1201");
        authCorpInfo.setCorpType("unverified");
        authCorpInfo.setCorpSquareLogoUrl("https://wework.qpic.cn/wwpic3az/979535_4zXHU2egQzim7T__1733123579/0");
        authCorpInfo.setSubjectType(1);
        appInfoParams.setAuthCorpInfo(authCorpInfo);

        // 设置授权应用信息
        QyweixinAppInfoParams.AuthAppInfo authAppInfo = new QyweixinAppInfoParams.AuthAppInfo();
        authAppInfo.setAgentId(1000008);
        authAppInfo.setName("纷享销客代开发测试");
        authAppInfo.setSquareLogoUrl("https://wework.qpic.cn/bizmail/K79HQSp7e3BonlzI4fmdsg3iabrlIa3fLkm2Vx1vI4DIsxzEcFOtPibQ/0");
        authAppInfo.setAuthMode(0);

        appInfoParams.setAuthAppInfo(authAppInfo);

        // 创建 OuterOaAppInfoEntity 对象
        OuterOaAppInfoEntity appInfoEntity = new OuterOaAppInfoEntity();
        appInfoEntity.setChannel(ChannelEnum.qywx); // 渠道
        appInfoEntity.setOutEa("wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA"); // 外部企业 ea
        appInfoEntity.setAppId("dkdf3684b6720635f7"); // 外部应用 appId
        appInfoEntity.setAppType(OuterOaAppInfoTypeEnum.serviceRepDev); // 应用类型
        appInfoEntity.setAppInfo(JSON.toJSONString(appInfoParams)); // 应用信息
        appInfoEntity.setStatus(OuterOaAppInfoStatusEnum.normal); // 应用状态
        appInfoEntity.setCreateTime(System.currentTimeMillis()); // 创建时间
        appInfoEntity.setUpdateTime(System.currentTimeMillis()); // 修改时间

        // 插入数据
        int result = outerOaAppInfoManager.insert(appInfoEntity);
        System.out.println(result);
    }

    @Test
    public void testQuery() {
        OuterOaAppInfoEntity oaAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, "wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA", "dkdf3684b6720635f7");
        System.out.println(oaAppInfoEntity);
    }
}
