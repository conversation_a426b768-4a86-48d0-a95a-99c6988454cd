package com.facishare.open.qywx.web.handler.externalMessage;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.qywx.web.service.ExternalTodoMsgService;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class ExternalMessageHandlerTest extends AbstractJUnit4SpringContextTests {

    @Autowired
    private ExternalMessageHandler externalMessageHandler;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private ExternalTodoMsgService externalTodoMsgService;

    @Test
    public void senTextMsg() {
        int ei = eieaConverter.enterpriseAccountToId("lgg6737");
        System.out.println(ei);
        SendTextMessageArg arg = new Gson().fromJson("{\"ea\":\"xkhdx003\",\"ei\":92974,\"senderId\":0,\"receiverIds\":[1000,1002,1003],\"messageContent\":\"【转移】执行完毕， 执行人:超人不会飞\\n本次操作覆盖 63 条 【怪兽线索】，执行成功 63 条， 失败 0 条。 详细执行结果请点击查看详情\",\"receiverChannelType\":1,\"receiverChannelData\":\"{\\\"appId\\\":\\\"crmNotify\\\"}\",\"generateUrlType\":0,\"extraDataMap\":{}}", SendTextMessageArg.class);
        externalMessageHandler.dealSendTextMessageHandler(arg);
    }

    @Test
    public void sendTextCardMsg() {
        String type1Json = "{\"ea\":\"xkhdx003\",\"ei\":92974,\"extraDataMap\":{\"objectApiName\":\"AccountObj\",\"applicantId\":\"1000\",\"workflowInstanceId\":\"66cec436913d0a6c46fc6dd2\",\"objectId\":\"66cec4347c3e710007b21d6e\",\"taskId\":\"66cec4366201a634fe236b3e\"},\"generateUrlType\":1,\"messageContent\":\"深圳市贝莱特智能有限公司】有新单据上传，当前可申请金额【460407.52】，账套金额【0】，最长DPE【3】,请及时跟进~\",\"receiverChannelData\":\"{\\\"appId\\\":\\\"workflow\\\"}\",\"receiverChannelType\":1,\"receiverIds\":[1000,*********,1001,*********],\"senderId\":0,\"title\":\"【科瑞技术】【深圳市贝莱特智能有限公司】新单据上传\",\"url\":\"todo?apiname=SupplierObj&id=65095ae15da9ba0001450c77&ea=hzqyun888&ea=hzqyun888\"}";
        String type2Json = "{\"ea\":\"xkhdx003\",\"ei\":92974,\"extraDataMap\":{\"objectApiName\":\"AccountObj\",\"applicantId\":\"1000\",\"workflowInstanceId\":\"66cec436913d0a6c46fc6dd2\",\"objectId\":\"66cec4347c3e710007b21d6e\",\"taskId\":\"66cec4366201a634fe236b3e\"},\"generateUrlType\":2,\"messageContent\":\"业务流程结束提醒：业务流程: 售后报价单审批  2025-03-21 11:09 已结束。\",\"receiverChannelData\":\"{\\\"appId\\\":\\\"crmNotify\\\"}\",\"receiverChannelType\":1,\"receiverIds\":[1000,*********,1001,*********],\"senderId\":0,\"title\":\"业务流程结束提醒\",\"url\":\"bpm?workflowInstanceId=67dcd86d2c36f154ed33e3e6&ea=topstarltd&ea=topstarltd\"}";
        String type5Json = "{\"ea\":\"xkhdx003\",\"ei\":92974,\"extraDataMap\":{\"fileName\":\"3.21.xlsx\",\"fileSize\":\"127140\",\"filePath\":\"N_202503_21_c630784c5e7d49f4abc2d36fe393108d.xlsx\",\"filePre\":\"1\"},\"generateUrlType\":5,\"messageContent\":\"3.21.xlsx\",\"receiverChannelData\":\"{\\\"appId\\\":\\\"WJTZ\\\"}\",\"receiverChannelType\":1,\"receiverIds\":[1000,*********,1001,*********],\"senderId\":0,\"title\":\"报表导出\",\"url\":\"https://www.fxiaoke.com/dps/preview/bypath?path=N_202503_21_c630784c5e7d49f4abc2d36fe393108d.xlsx&showHeader=1\"}";
        SendTextCardMessageArg arg = new SendTextCardMessageArg();
        arg.setGenerateUrlType(5);
        arg.setEa("xkhdx003");
        arg.setEi(92974);
        arg.setReceiverIds(Lists.newArrayList(1000, *********, 1001, *********));
        arg.setMessageContent("3.21.xlsx");
        arg.setReceiverChannelType(1);
        arg.setTitle("报表导出");
        Map<String, String> channel = new HashMap<>();
        channel.put("appId", "WJTZ");
        arg.setUrl("https://www.fxiaoke.com/dps/preview/bypath?path=N_202503_21_c630784c5e7d49f4abc2d36fe393108d.xlsx&showHeader=1");
        arg.setReceiverChannelData(JSON.toJSONString(channel));
//        List<KeyValueItem> form = new LinkedList<>();
//        KeyValueItem valueItem = new KeyValueItem("订单id", "我是id");
//        KeyValueItem valueItem1 = new KeyValueItem("订单金额", "没多少");
//        form.add(valueItem);
//        form.add(valueItem1);
//        arg.setForm(form);
        Map<String, String> extraDataMap = new HashMap<>();
        extraDataMap.put("fileName", "3.21.xlsx");
        extraDataMap.put("fileSize", "127140");
        extraDataMap.put("filePath", "N_202503_21_c630784c5e7d49f4abc2d36fe393108d.xlsx");
        extraDataMap.put("filePre", "1");
//        extraDataMap.put("objectId", "66cec4347c3e710007b21d6e");
//        extraDataMap.put("taskId", "66cec4366201a634fe236b3e");
        arg.setExtraDataMap(extraDataMap);
//        arg.setAppId("FSAID_11490d9e");
        String json = JSON.toJSONString(arg);
        System.out.println(json);
        externalMessageHandler.dealSendTextCardMessageHandler(arg);
    }

    @Test
    public void createTodo() {
        String t = "{\"ea\":\"xkhdx003\",\"ei\":92974,\"senderId\":0,\"receiverIds\":[1000,*********,1001,*********],\"sourceId\":\"66cec4366201a634fe236b3e006\",\"bizType\":\"452\",\"url\":\"todo?apiname=AccountObj&id=67dd1028792c4e000199bc5f&ea=xkhdx003\",\"title\":\"待处理的审批流程\",\"content\":\"客户(2024-08-01 16:29)\",\"form\":[{\"key\":\"测试失败\",\"value\":\"test001\"},{\"key\":\"流程主题\",\"value\":\"客户(2024-08-01 16:29)\"},{\"key\":\"客户名称\",\"value\":\"啦啦啦-客户009\"},{\"key\":\"1级行业\",\"value\":\"\"},{\"key\":\"客户级别\",\"value\":\"\"},{\"key\":\"成交状态\",\"value\":\"未成交\"},{\"key\":\"负责人\",\"value\":\"陈宗鑫\"},{\"key\":\"多余的\",\"value\":\"飞书只允许写5行，这个看下会不会展示，还是说只展示后5行\"}],\"generateUrlType\":1,\"extraDataMap\":{\"activityId\":\"1\",\"objectApiName\":\"AccountObj\",\"applicantId\":\"1000\",\"workflowInstanceId\":\"66cec436913d0a6c46fc6dd2\",\"objectId\":\"67dd1028792c4e000199bc5f\",\"taskId\":\"66cec4366201a634fe236b3e\"},\"appId\":\"FSAID_11490d9e\"}";
        CreateTodoArg arg = JSON.parseObject(t, CreateTodoArg.class);
        externalMessageHandler.dealCreateTodoHandler(arg);
    }
}
