package com.facishare.open.qywx.old.save.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.open.qywx.BaseTest;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinGroupChatDetail;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.web.arg.FileMessageArg;
import com.facishare.open.qywx.save.arg.QueryMessageArg;
import com.facishare.open.qywx.web.constant.QYWXApiAndObjectEnum;
import com.facishare.open.qywx.web.db.dao.QyweixinIdToOpenidDao;
import com.facishare.open.qywx.web.manager.ExternalContactManager;
import com.facishare.open.qywx.web.manager.QYWXFileManager;
import com.facishare.open.qywx.web.model.QyweixinExternalContactGroupChatInfo;
import com.facishare.open.qywx.web.entity.entity.QywxMessagePo;
import com.facishare.open.qywx.save.result.FileMessageResult;
import com.facishare.open.qywx.save.result.Pager;
import com.facishare.open.qywx.save.result.Result;
import com.facishare.open.qywx.save.service.AutoPullMessageService;
import com.facishare.open.qywx.save.service.MessageGeneratingService;
import com.facishare.open.qywx.save.service.SaveMessageService;
import com.facishare.open.qywx.save.vo.AutRetentionVo;
import com.facishare.open.qywx.save.vo.GenerateSettingVo;
import com.facishare.open.qywx.save.vo.QywxMessageVo;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.result.ObjectDataCreateResult;
import com.fxiaoke.crmrestapi.result.QueryBySearchTemplateResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/25 16:00
 * @Version 1.0
 */
@Slf4j
public class ServiceTest extends BaseTest {
    @Autowired
    private MessageGeneratingService generatingService;

    @Autowired
    private SaveMessageService saveMessageService;
    @Autowired
    private QYWXFileManager qywxFileManager;
    @Autowired
    private AutoPullMessageService autoPullMessageService;
    @Autowired
    private ExternalContactManager externalContactManager;
    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;
    @Autowired
    private MessageGeneratingService messageGeneratingService;
    @Autowired
    private ObjectDataService objectDataService;
    @Resource
    private QyweixinGatewayInnerService qyweixinGatewayServiceNormal;
    public QyweixinIdToOpenidDao qyweixinIdToOpenidDao;

    @ReloadableProperty("QYWX_REP_APPID")
    private String QYWX_REP_APPID;

    @Test
    public void saveGenerating() {
        GenerateSettingVo generateSettingVo = new GenerateSettingVo();
        String ea = "jjj7351";
        generateSettingVo.setCorpSecret("GZbKS1I7VMuzsIkrBk2ajsuUxSSvJrb9H_B2pkD1bq0");
        generateSettingVo.setAgentId("1000036");
        generateSettingVo.setQywxCorpId("wpwx1mDAAAZVdwgqOHLomWPL0J6rDUVA");
        generateSettingVo.setSecret("123456789101010101010");
        generateSettingVo.setAgentId("*********");
        generateSettingVo.setEa(ea);
        generateSettingVo.setSecret("ZWVYRYM11uaiG7xDtw0VUi2r67CXqcO0FPNkjO7P7ms");
        generateSettingVo.setPrivateKey("MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCLrlqxctpv1dg8ERgfrsizH9s5I099XKGX0qFYi1NwKXKeXTL1bbWRMEWesHSMiNiQPGTouXRDXKmqyVnKJcMGgIBfMfO4uDQ8U7hUyYHvVRURP4uSOL2ZGXLwnIqlI7WRDtTU1RgKVomyDtKlOK9vtEc0js+hMT7OiriBy91XG67nClzzrYdew284Skx61Jc6CpC4hRxJjIABlq5RX5F+Wp5VzntykY/G6L+vdRu4KBnY+fxEowGmlDk2QxIA4K6S4OfXCYHHRmZanF8oWs+uBUTw1eLh90SEa/5ajwtxdA7HyDC8QVlKxerJClyqcpxv1ptjqFBBbXLvsVhwgXEhAgMBAAECggEATTUdmlIS3ZhFQsZsIC8bbq9gHJAhAvkttN7PIkM45plyaoi3fyOaJduZz+JXOcr2cZuAZ4cC9a0Fd4p+YBdJWpGy42uX/PWMof/gtrT/ZkwQLg2C11sXqcWAW/EbTbaUSM764326IRS+XbaFxp+zkToD1dBOghnXTpEs9Um7WcbxzllZfiJ3rd2UxswWvpB9tQk8li9NphY3ioVxckiCtvQiVnUZaT/0RKoae/hN3VPqG6Uv1ZiZ0kcOiu6KwOQ0nXHEN1uzga5UG87Y17V2JadiEUFWI/5O3KEhRf1rl8cNsod1gf9ObbBHH7AuJCYLvdbuXD3fPHWrHCueUXy9hQKBgQD3pY1uUmHZrzsPkIE3oN0Ls2bUcSZ1cIb6gN0zNOn08D0gSYOgJ76sF32cFtSnhqZueqdu4QvPzqTHeMF6oFh8KhdSVtVlWGDQVC8cczsAOq8wBdyckgSa5Pt7XG+g5kL5aaTDb+6XOqzsrKGdiyMpG8AefLYySIixP669JobaJwKBgQCQZIK8N9fNmP6QUNIUtmlZYdtG+LyUZKiCT3fSeYKZwNtfO2IoQYHJFoP4or+FdeUXVU9eaNaYqxp/VH93ZWbERgPwKWagZDoWQskuyfg3HVz3hZ3lc1CVUear8dtO1TTRissVNJnR+ki3jgsf+sBx0vLniLTZsje/KnCX0YBPdwKBgFv2hYPPYfjlgqgwAFw5B3z93RTNA/weknFaA0qtvqevwvNHeXKy77KWcpXRQJ0JeqqSL7UUKz+7PCO66xZvjwxk0Q5Joqsk26bhbDFDdUiLglzyAE/ARaeDmwPferCkcYCPQ5kz6sUMDAVDwixv69mrLXfk1f/sQZ6YyHoDYZaHAoGBAIXK/HDW9bnmSAsFOIREub0+tWY/2M1Pr+x/IjH+sYsybpMBfWR7vnzLxiE+/GP35/0E6XQ7hI0WDolpjGrfpKe9kKyaUCPSexhhbfVS5BJ9vMUGJFaV0Vdq+mjcxC9502VOS/ssMFOmrHaYwaoyONu/caAkxh7pyknyUz35vADlAoGBAOFTuI8DFp16a0L3+D1tnh/zWr5rsIjjxsKZ4Ov35dlLoKMfHYSIBlV8vDATkfO8KdJHeuncUoKJuGibgXI/dO90rAmxIGjTrd9p1YyEsoHqat9NCPz1aq7KNBxNHwmwu1XZI4JWSJwj4dx4emZGnqnx/sBgUASIIDedjwGUWn==");
        generateSettingVo.setPublicKey("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAi65asXLab9XYPBEYH67Isx/bOSNPfVyhl9KhWItTcClynl0y9W21kTBFnrB0jIjYkDxk6Ll0Q1ypqslZyiXDBoCAXzHzuLg0PFO4VMmB71UVET+Lkji9mRly8JyKpSO1kQ7U1NUYClaJsg7SpTivb7RHNI7PoTE+zoq4gcvdVxuu5wpc862HXsNvOEpMetSXOgqQuIUcSYyAAZauUV+RflqeVc57cpGPxui/r3UbuCgZ2Pn8RKMBppQ5NkMSAOCukuDn1wmBx0ZmWpxfKFrPrgVE8NXi4fdEhGv+Wo8LcXQOx8gwvEFZSsXqyQpcqnKcb9abY6hQQW1y77FYcIFxIQIDAQA=");
        generateSettingVo.setVersion(6);
        generateSettingVo.setSeq(0);
        generateSettingVo.setAutRetention(1);
        generateSettingVo.setFsTenantId(11111);
        Result<Integer> integerResult = generatingService.saveSetting(generateSettingVo);
        log.info("insert:{}",integerResult);
    }

    @Test
    public void saveSecretGenerating() {
        GenerateSettingVo generateSettingVo = new GenerateSettingVo();

        generateSettingVo.setEa("83385");
        generateSettingVo.setFsTenantId(83385);
        generateSettingVo.setQywxCorpId("12323");
        generateSettingVo.setCorpSecret("321651fdsfds");
        generateSettingVo.setAgentId("1000108");
       // String token = qyweixinAccountSyncService.getToken("82234", generateSettingVo.getCorpSecret());
        Result<Integer> integerResult = generatingService.saveSecretSetting(generateSettingVo);
        log.info("insert:{}",integerResult);
    }

    @Test
    public void queryMessageSetting(){
        Result<GenerateSettingVo> generateSettingVoResult = generatingService.querySettingByAuto("82777", null,null);
        log.info("result:{}",generateSettingVoResult);
    }

    @Test
    public void querySecretSetting(){
        Result<GenerateSettingVo> generateSettingVoResult = generatingService.querySecretSetting("84263");
        log.info("result:{}",generateSettingVoResult);
    }


    @Test
    public void saveMessage(){
        List<QywxMessageVo> messagePos=new ArrayList<>();
        QywxMessageVo messagePo=new QywxMessageVo();
        messagePo.setId(5521188211211112L);
        messagePo.setFsEa("12121");
        messagePo.setFromUser("wewew");
        messagePo.setToList(Lists.newArrayList("1211","121"));
        messagePo.setFromEncryptionUser("wewewTest");
        messagePo.setToEncryptionList(Lists.newArrayList("1211Test","121Test"));

        messagePo.setKeyVersion(2);
        messagePo.setMessageTime(122112112L);
        messagePo.setMd5sum("12333232323");
        messagePo.setSdkFileId("慢啊哈哈哈哈慢");
        messagePo.setContent("快乐啊");
        messagePo.setMessageId("23210377221361222322232");
        messagePo.setSeq(121L);
        messagePo.setMessageType("I");

        QywxMessageVo messagePo2=new QywxMessageVo();
        messagePo2.setId(11123771117723L);
        messagePo2.setFsEa("12121");
        messagePo2.setFromUser("wewew");
        messagePo2.setToList(Lists.newArrayList("1211","121"));
        messagePo.setFromEncryptionUser("wewewTest");
        messagePo.setToEncryptionList(Lists.newArrayList("1211Test","121Test"));
        messagePo2.setKeyVersion(2);
        messagePo2.setMessageTime(121212112L);
        messagePo2.setMd5sum("12333232323");
        messagePo2.setSdkFileId("212121212");
        messagePo2.setMessageId("23232227711232322211");
        messagePo2.setSeq(121L);
        messagePo2.setMessageType("I");

        messagePos.add(messagePo);
        messagePos.add(messagePo2);
        Result<Integer> integerResult = saveMessageService.batchSaveMessage(messagePos);
        System.out.println(integerResult);
    }

    @Test
    public void BatchUpdate(){
        List<QywxMessageVo> messagePos=new ArrayList<>();
        QywxMessageVo messagePo=new QywxMessageVo();
        messagePo.setMessageId("2321002111361222322232");
        messagePo.setContent("haho哈喽");
        messagePo.setNpath("N_PAJEE");
        messagePo.setFileName("文语件11211");
        messagePo.setFileExt("haho哈喽hhh哈哈哈");
        messagePo.setFileSize(1212L);
        messagePos.add(messagePo);
        //1133121211111
        QywxMessageVo messagePo1=new QywxMessageVo();
        messagePo1.setMessageId("232361222322232");
        messagePo1.setContent("嘟嘟哈喽");
        messagePo1.setNpath("N_P222AJEE");
        messagePo1.setFileName("文语件11211");
        messagePo1.setFileExt("haho哈喽hhh哈哈哈");
        messagePo1.setFileSize(1212L);
        messagePos.add(messagePo1);


        Result<Integer> integerResult = saveMessageService.batchUpdateMessage(messagePos,"12121");
    }

    @Test
    public void queryMessage(){
        Result<QywxMessageVo> messageById = saveMessageService.getMessageById("12121", "2321002111361222322232");
        System.out.println("com.fscishare.open.qywx.save.test.ServiceTest.queryMessage"+messageById);
    }

    @Test
    public void updateMessagee(){
        QywxMessageVo qywxMessagePo=new QywxMessageVo();
        qywxMessagePo.setFsEa("12121");
        qywxMessagePo.setMessageId("2321002111361222322232");
        qywxMessagePo.setNpath("kikiki");
        qywxMessagePo.setFileName("今日文档");
        Result<Integer> count = saveMessageService.updateMessageById(qywxMessagePo);
        System.out.println(count);
    }

    @Test
    public void queryMessageConditory(){
        QueryMessageArg queryMessageArg=new QueryMessageArg();
        queryMessageArg.setFsEa("84883");
        queryMessageArg.setOutEa("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
        queryMessageArg.setPageNum(1);
        queryMessageArg.setPageSize(20);
        queryMessageArg.setSenderIds("wowx1mDAAArLI4aPJXTqJx_UJCABHILQ");
        queryMessageArg.setReceiveIds("wmwx1mDAAAEHC7C10PhMXCGverrKMlKQ");
//        queryMessageArg.setRoomId("wrQZ1uJQAAEH3PMSeQ96V8cqvhy7WQKg");
        Result<Pager<FileMessageResult>> pagerResult = saveMessageService.conditionQueryMessage(queryMessageArg);
        System.out.println(pagerResult);
    }

    @Test
    public void testArray(){
        String message="{\"msgid\":\"9619449085594287277_1617018676_external\",\"action\":\"send\",\"from\":\"KeNanYing\",\"tolist\":[\"wowx1mDAAAeWRNqUgWjlPF83-VUcFRTw\",\"wowx1mDAAAXmnq7Rw_dxQsuFqWRFYVZA\",\"18911149036\",\"lifang\"],\"roomid\":\"\",\"msgtime\":1617018676069,\"msgtype\":\"image\",\"image\":{\"md5sum\":\"b40e1bfd24c05cc12556af9b4190e37a\",\"filesize\":9502,\"sdkfileid\":\"CtQBMzA2ODAyMDEwMjA0NjEzMDVmMDIwMTAwMDIwNDIxZTYzYjY0MDIwMzBmNGRmYTAyMDQ1MzcwZmIzYTAyMDQ2MDYxYmYzNDA0MjQ2MzMzMzczMDM1MzkzMDYzMmQzODMyNjYzODJkMzQzOTMwMzkyZDM5MzUzMTM2MmQzNzM4MzEzODYzMzkzMjYxMzEzMjY0MzIwMjAxMDAwMjAyMjUyMDA0MTBiNDBlMWJmZDI0YzA1Y2MxMjU1NmFmOWI0MTkwZTM3YTAyMDEwMTAyMDEwMDA0MDASOE5EZGZNVFk0T0RnMU1EUXlPVEF3TURVME9GOHlNREk0TkRrMk1EYzJYekUyTVRjd01UZzJOelk9GiAzOTMyMzEzNjM4NjUzMjM5MzczNjMwNjI2MTY1Mzc2Ng==\"}}";
        Object read = JSONPath.read(message, "$.action");
        Object toList = JSONPath.read(message, "$.tolist");
        JSONArray jsonArray = JSONArray.parseArray(toList.toString());
        List<String> toUser = jsonArray.toJavaList(String.class);
        System.out.println("message:{}"+read.toString());
        System.out.println("message:{}"+toList.toString());
        System.out.println(toUser);
        System.out.println(toUser.get(0));
    }

    @Test
    public void testMessage(){
        Result<GenerateSettingVo> generateSettingVoResult = generatingService.querySetting("84883", 4, null);
        qywxFileManager.saveMessage(generateSettingVoResult.getData(),4397L);
    }

    @Test
    public void testQuerymessage(){
        autoPullMessageService.getCorpIdMessage();
    }
    @Test
    public void testAutoMessage(){
        autoPullMessageService.getAutoSynchronizationMessage();
    }
    @Test
    public void testAutoMessageq(){
        List<String> ea = qyweixinAccountSyncService.openAuthorizationByPage(1, 10);
        System.out.println(ea);
    }

//    @Test
//    public void testAutoMessageq1(){
//        List<String> ea = qyweixinAccountSyncService.getAutRetentionCorp();
//        System.out.println(ea);
//    }

    @Test
    public void testAutoMessage2(){
        qyweixinAccountSyncService.queryByPhone(83838, "phone", "***********", "AccountObj");
    }

    @Test
    public void testAutoMessage4(){
        qyweixinAccountSyncService.queryByPhone(83838, "phone", "***********", "AccountObj");
    }

    @Test
    public void testAutoMessage3(){
        QueryMessageArg queryMessageArg = new QueryMessageArg();
        queryMessageArg.setFsEa("84262");
        queryMessageArg.setOutEa("wpwx1mDAAAZVdwgqOHLomWPL0J6rDUVA");
        List<String> receiveIds=new ArrayList<>();
        receiveIds.add("wowx1mDAAABS8aOmm0Vi97W8jgeU20Mw");
        receiveIds.add("wmwx1mDAAACw7jH5tYISwl6cjEKe8-Ug");
//        queryMessageArg.setReceiveIds(receiveIds);
//        queryMessageArg.setSenderIds(receiveIds);
        int room = saveMessageService.getIsRoom(queryMessageArg);
        List<String> a = saveMessageService.getRoom(queryMessageArg);
        saveMessageService.test(queryMessageArg);
        Result<List<FileMessageResult>> b = saveMessageService.getConditionQueryMessage(queryMessageArg, null);

        System.out.println(room);
    }

    @Test
    public void test() {
        autoPullMessageService.test();
    }

    @Test
    public void test1() {
        //System.out.println(qyweixinAccountSyncService.queryExternalContactListTwoScheme("82236", "***********"));
        qyweixinAccountSyncService.AutoGetExternalContactEmployeeId2("83384",null);
    }

    @Test
    public void sendAutoMessage1() {

        autoPullMessageService.test1();
    }

    @Test
    public void sendAutoMessage2() {

        String userName = saveMessageService.getName("81002", "wxbbe44d073ff6c715", "KeNanYing");
        System.out.println(userName);
    }

    @Test
    public void sendAutoMessage3() {

        //autoPullMessageService.getAutoSynchronizationMessageTest();
    }

    @Test
    public void testRoomHost() {

        QyweixinGroupChatDetail.GroupChat roomMessageId = qyweixinAccountSyncService.getRoomMessage2("83384", "wrwx1mDAAAEbHZ5rpY7Wsyr-xPEXVnfw",null);
        System.out.println(roomMessageId);
    }

    @Test
    public void testMQ() {

        messageGeneratingService.sendMessage("83384");
    }

    @Test
    public void testUpdateCorpRepSecret() {
        GenerateSettingVo generateSettingVo = new GenerateSettingVo();
        generateSettingVo.setQywxCorpId("testRepSecret");
        generateSettingVo.setCorpSecret(null);
        generateSettingVo.setAgentId(null);
        Result<Integer> result = messageGeneratingService.updateCorpRepSecret(generateSettingVo);
        System.out.println(result.getData());
    }


    @Test
    public void testPain() {
        qyweixinAccountSyncService.plainToEncryption("83384", Lists.newArrayList("ChenZongXin"));
    }

    @Test
    public void testSaveSales() {
        AutRetentionVo autRetentionVo = new AutRetentionVo();
//        autRetentionVo.setEi(84262);
//        autRetentionVo.setEa("84262");
//        autRetentionVo.setFsId("1000");
//        autRetentionVo.setFsUserName("chenzongxin");
//        autRetentionVo.setUserName("chenzongxin");
//        autRetentionVo.setContactId(outId);
//        autRetentionVo.setContactName(qyweixinExternalContactInfo.getName());
//        autRetentionVo.setPagerResult(pagerResult);
//        autRetentionVo.setRelatedObject(relatedObject);
//        autRetentionVo.setAttempt(attempt);
        externalContactManager.saveSales(autRetentionVo);
    }

    @Test
    public void testGetMessageUpload() {
        List<QywxMessageVo> messageResult = new LinkedList<>();
        for (long i = 0; i < 10; i++) {
            QywxMessageVo qywxMessageVo = new QywxMessageVo();
            long a = 13000000L + i*1000000L;
            qywxMessageVo.setFileSize(a);
            messageResult.add(qywxMessageVo);
        }
        messageResult.add(new QywxMessageVo());
        String ea = "74860";
        //saveMessageService.getMessageUploadByAuto(messageResult, ea, Boolean.FALSE);
    }

//    @Test
//    public void testObjectDataServiceCreate() {
//
//        QywxMessagePo messagePo = saveMessageDao.queryMessageById("84883", "7103351322618273246_1665199931325_external");
//
//        HeaderObj headerObj = HeaderObj.newInstance(82777, -10000);
//        ObjectData objectData = new ObjectData();
//        //objectData.put(QYWXApiAndObjectEnum.NAME.getName(), messagePo.getMessageId());
//        objectData.put(QYWXApiAndObjectEnum.OWNER.getName(), Lists.newArrayList("-10000"));
//        objectData.put(QYWXApiAndObjectEnum.FS_EA.getName(), messagePo.getFsEa());
//        objectData.put(QYWXApiAndObjectEnum.SEQ.getName(), messagePo.getSeq());
//        objectData.put(QYWXApiAndObjectEnum.MSG_ID.getName(), "test");
//        objectData.put(QYWXApiAndObjectEnum.KEY_VERSION.getName(), messagePo.getKeyVersion());
//        objectData.put(QYWXApiAndObjectEnum.FROM_USER_CIPHER.getName(), messagePo.getFromEncryptionUser());
//        objectData.put(QYWXApiAndObjectEnum.FROM_USER.getName(), messagePo.getFromUser());
//        objectData.put(QYWXApiAndObjectEnum.TO_LIST_CIPHER.getName(), messagePo.getToEncryptionList());
//        objectData.put(QYWXApiAndObjectEnum.TO_LIST.getName(), messagePo.getToList());
//        //objectData.put(QYWXApiAndObjectEnum.ROOM_ID.getName(), messagePo.getRoomId());
//        objectData.put(QYWXApiAndObjectEnum.MSG_TIME.getName(), messagePo.getMessageTime());
//        objectData.put(QYWXApiAndObjectEnum.MSG_TYPE.getName(), messagePo.getMessageType());
//        objectData.put(QYWXApiAndObjectEnum.CONTENT.getName(), messagePo.getContent());
//        //objectData.put(QYWXApiAndObjectEnum.MD5SUM.getName(), messagePo.getMd5sum());
//        //objectData.put(QYWXApiAndObjectEnum.SDK_FILE_ID.getName(), messagePo.getSdkFileId());
//        //objectData.put(QYWXApiAndObjectEnum.FILE_SIZE.getName(), messagePo.getFileSize());
//        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataCreateResult> result = null;
//        result = objectDataService.create(headerObj, QYWXApiAndObjectEnum.WECHAT_CONVERSION_OBJ.getName(),
//                    false,false,false,objectData);
//        System.out.println(result);
//    }

    @Test
    public void testObjectDataServiceCreateTestMessage__c() {

        HeaderObj headerObj = HeaderObj.newInstance(84883, -10000);
        ObjectData objectData = new ObjectData();
        objectData.put(QYWXApiAndObjectEnum.NAME.getName(), "test010");
        objectData.put(QYWXApiAndObjectEnum.OWNER.getName(), Lists.newArrayList("-10000"));
        FileMessageArg arg = new FileMessageArg();
        arg.setFilename("fe0bd440fc6a46c8489d4f238cf9ef73.jpg");
        arg.setPath("N_202210_19_89ac0f762b30459bbbb674b012a9c029");
        arg.setExt("jpg");
        arg.setSize(1264728L);
        objectData.put("image__c", Lists.newArrayList(arg));
        FileMessageArg arg1 = new FileMessageArg();
        arg1.setFilename("强爽.pdf");
        arg1.setPath("N_202210_20_f43d48b1ab7e4a5183f7cf4f2ba63725");
        arg1.setExt("pdf");
        arg1.setSize(73590L);
        objectData.put("attachment__c", Lists.newArrayList(arg1));
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataCreateResult> result = null;
        result = objectDataService.create(headerObj, "testMessage__c",
                false,false,false,objectData);
        System.out.println(result);
    }

    @Test
    public void testObjectDataServiceCreate1() {
        HeaderObj headerObj = HeaderObj.newInstance(82777, -10000);
//        BulkDeleteArg arg = new BulkDeleteArg();
//        arg.setDataIds(Lists.newArrayList("63462acbf7ba6a000165a77d"));
//
//        objectDataService.bulkDelete(headerObj,"WechatGroupObj",
//                arg,true);
        //com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListByIdsResult> queryListByIdsResult = objectDataService.queryListByIds(headerObj, "WechatWorkExternalUserObj", Lists.newArrayList("wmwx1mDAAAEHC7C10PhMXCGverrKMlKQ"));
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.addFilter(QYWXApiAndObjectEnum.EXTERNAL_USER_ID.getName(), Lists.newArrayList("wmwx1mDAAAEHC7C10PhMXCGverrKMlKQ"), QYWXApiAndObjectEnum.OPERATOR.getName());
        //searchTemplateQuery.addFilter("chat_create_time", Lists.newArrayList("1663642641"), "EQ");
        searchTemplateQuery.setPermissionType(0);
        com.fxiaoke.crmrestapi.common.result.Result<QueryBySearchTemplateResult> oldSpuDataResult = objectDataService.queryBySearchTemplate(headerObj, QYWXApiAndObjectEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), searchTemplateQuery);


        System.out.println(oldSpuDataResult);


//        ObjectData objectData = new ObjectData();
//        objectData.put("name","wmwx1mDAAA6__TC8Z4Ya04gwZhE1epEw");
//        objectData.put("external_user_id","wmwx1mDAAA6__TC8Z4Ya04gwZhE1epEw");
//        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataCreateResult> result = objectDataService.create(headerObj,"WechatWorkExternalUserObj",
//                false,false,false,objectData);
//        System.out.println(result);
    }

    @Test
    public void testGroupInfo() {
        com.facishare.open.qywx.accountsync.result.Result<QyweixinGroupChatDetail> groupChatJSONObject =
                qyweixinGatewayServiceNormal.getGroupChatDetail("84883", "wrQZ1uJQAAbv9IYbAr6BrT-DPF9XU_jg", QYWX_REP_APPID);
        System.out.println(groupChatJSONObject);
        JSONPath.read(JSON.toJSONString(groupChatJSONObject.getData()),"$.group_chat").toString();
        QyweixinExternalContactGroupChatInfo externalContactRsp = JSONObject.parseObject(JSONPath.read(JSON.toJSONString(groupChatJSONObject.getData()),"$.group_chat").toString(), QyweixinExternalContactGroupChatInfo.class);
        System.out.println(externalContactRsp);
    }

//    @Test
//    public void testBatchQueryMessageByIds() {
//        List<QywxMessagePo> newMessageResult = saveMessageDao.batchQueryMessageByIds("84883", Lists.newArrayList("11381565014156652934_1670161999250_external", "11875603809554530463_1669866238854_external", "12349276461183118997_1670223332195_external"));
//        System.out.println(newMessageResult);
//    }
//
//    @Test
//    public void t2() {
//        QyweixinIdToOpenidPo a = new QyweixinIdToOpenidPo();
//        a.setCorpId("test");
//        a.setPlaintextId("pl");
//        a.setOpenid("op");
//        a.setType(1);
//        int s = qyweixinIdToOpenidDao.saveInfo(a);
//        System.out.println(s);
//    }

    @Test
    public void t3() {
        List<String> s = qyweixinIdToOpenidDao.getByExternalIds("test", Lists.newArrayList("pl"));
        System.out.println(s);
    }

//    @Test
//    public void t4() {
//        List<QyweixinIdToOpenidPo> s = qyweixinIdToOpenidDao.getByOpenIds("test", Lists.newArrayList("pl"));
//        System.out.println(s);
//    }

    @Test
    public void saveMessageToMongoTest() {
        saveMessageService.saveMessageToMongo("84883", 11L, 20L);
    }

    @Test
    public void saveAllMessageToMongoTest() {
        Result<Void> result = saveMessageService.saveAllMessageToMongo();
        System.out.println(result);
    }

    @Test
    public void saveMessageAllIdsTest() {
        Result<Void> result = saveMessageService.saveMessageAllIds();
        System.out.println(result);
    }

    @Test
    public void getMongoDataByIdTest() {
        Result<QywxMessageVo> result = saveMessageService.getMongoDataById("84883", "63dddb139001a37016b7428e");
        System.out.println(result);
    }

    @Test
    public void updateCorpSetting() {

        Result<Void> result = messageGeneratingService.updateCorpSetting();
        System.out.println(result.getData());
    }

    @Test
    public void insertMongoData() {
        QywxMessageVo message = new QywxMessageVo();

        message.setId(null);
        message.setFsEa("jjj7351");
        message.setMessageId("xxx");
        message.setSeq(50001L);
        message.setKeyVersion(2);
        message.setFromUser("xxx");
        message.setFromEncryptionUser(null);
        message.setToList(Lists.newArrayList("xxx"));
        message.setToEncryptionList(null);
        message.setRoomId(null);
        message.setMessageTime(1714214960133L);
        message.setMessageType("text");
        message.setContent("111");
        message.setMd5sum(null);
        message.setSdkFileId(null);
        message.setFileSize(null);
        message.setNpath(null);
        message.setFileName(null);
        message.setFileExt(null);
        Result<Integer> result = saveMessageService.insertMongoData("jjj7351", message);
        System.out.println(result);
    }
}
