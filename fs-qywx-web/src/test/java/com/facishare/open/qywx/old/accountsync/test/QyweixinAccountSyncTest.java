package com.facishare.open.qywx.old.accountsync.test;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.oa.base.dbproxy.mongo.dao.OaConnectorSyncEventDataMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorSyncEventDataDoc;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.info.EnterpriseTrialInfo;
import com.facishare.open.outer.oa.connector.common.api.info.FsEmployeeDetailInfo;
import com.facishare.open.qywx.accountinner.model.QyweixinExternalContact;
import com.facishare.open.qywx.accountsync.excel.FileManager;
import com.facishare.open.qywx.accountsync.excel.ReadExcel;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.manager.ExcelListener.BaseListener;
import com.facishare.open.qywx.web.manager.QYWeixinManager;
import com.facishare.open.qywx.accountsync.model.OaConnectorDataModel;
import com.facishare.open.qywx.accountsync.model.QyweixinEnterpriseOrder;
import com.facishare.open.qywx.accountsync.model.QyweixinUpdateOrganizationEvent;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.web.model.qyweixin.QyweixinDepartmentEmployeeListRsp;
import com.facishare.open.qywx.web.service.impl.QyweixinGatewayInnerServiceImpl;
import com.facishare.open.qywx.accountsync.utils.xml.QyweixinMsgBaseXml;
import com.facishare.open.qywx.accountsync.utils.xml.TagChangeEventXml;
import com.facishare.open.qywx.accountsync.utils.xml.XStreamUtils;
import com.facishare.open.qywx.save.vo.GenerateSettingVo;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.mongodb.bulk.BulkWriteResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.Message;
import org.bson.types.ObjectId;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.Serializable;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * Created by liuwei on 2018/07/20
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class QyweixinAccountSyncTest extends AbstractJUnit4SpringContextTests {

    @Resource
    QyweixinAccountSyncService qyweixinAccountSyncService;
    @Resource
    QyweixinGatewayInnerServiceImpl qyweixinGatewayInnerService;
    @Autowired
    RedisDataSource redisDataSource;

    @Autowired
    private QYWeixinManager qyWeixinManager;

    @Autowired
    private FileManager fileManager;

//    String appId = "wx4c7edab730f4fdc9";
//    String fsEa = "61257";  //58566

//    String appId = "wwa05370bc2611cb6e";
//    String fsEa = "61091";  //58566

    //    112
    String appId = "tj210a7c5f4fcad72a";
    String fsEa = "78613";

    @ReloadableProperty("crmAppId")
    private String crmAppId;

    @ReloadableProperty("repAppId")
    private String repAppId;

    @Resource
    private OaConnectorSyncEventDataMongoDao oaConnectorSyncEventDataMongoDao;


    ExecutorService qyWeixinThreadPool = Executors.newFixedThreadPool(20);


    @Test
    public void getCorpInfo() {

        Result<QyweixinCorpInfo> result = qyweixinAccountSyncService.getCorpInfo(fsEa, appId);
        log.info("trace getCorpInfo result:{}", JSONObject.toJSONString(result));
        // AssertResult(result.getErrorCode());
    }

    @Test
    public void getCountQyweixinPrivilegeEmployee() {
        Result<Integer> result = qyweixinAccountSyncService.getCountQyweixinPrivilegeEmployee("84883");
        log.info("getCountQyweixinPrivilegeEmployee result:{}", JSONObject.toJSONString(result));
    }

    @Test
    public void getDepartmentInfoList() throws InterruptedException {
        Result<List<QyweixinDepartmentInfo>> result = qyweixinAccountSyncService.getDepartmentInfoList("84883", "wx88a141937dd6f838",
                "1");
        Result<List<QyweixinDepartmentInfo>> result1 = qyweixinAccountSyncService.getDepartmentInfoList("85471", "wx88a141937dd6f838",
                "8");
        Result<List<QyweixinDepartmentInfo>> result2 = qyweixinAccountSyncService.getDepartmentInfoList(fsEa, appId, "");
        log.info("trace getDepartmentInfoList result:{}", result);
        log.info("trace getDepartmentInfoList result1:{}", result1);
        log.info("trace getDepartmentInfoList result2:{}", result2);
    }

    //    @Test
    public void getSyncDepartmentInfoList() throws InterruptedException {
        for (int i = 0; i < 1; i++) {
            qyWeixinThreadPool.execute(() -> {
                Result<List<QyweixinDepartmentInfo>> result = qyweixinAccountSyncService.getDepartmentInfoList(fsEa, appId, "2222");
                log.info("trace getDepartmentInfoList result:{}", JSONObject.toJSONString(result));
            });
        }
        Thread.sleep(2 * 1000L);
    }

    @Test
    public void getDepartmentEmployeeInfoList() {

        Result<List<QyweixinEmployeeInfo>> result = qyweixinAccountSyncService.getDepartmentEmployeeInfoList(fsEa, "2");
        //Result<List<QyweixinEmployeeInfo>> result1 = qyweixinAccountSyncService.getDepartmentEmployeeInfoList(fsEa, appId, "52");
        log.info("trace getDepartmentEmployeeInfoList result:{}", JSONObject.toJSONString(result));
        //log.info("trace getDepartmentEmployeeInfoList result1:{}", JSONObject.toJSONString(result1));
    }

    @Test
    public void getTagInfoList() {
        Result<List<QyweixinTag>> result = qyweixinAccountSyncService.getTagInfoList("84883", "dkdf3684b6720635f7");
        log.info("trace getTagInfoList result:{}", JSONObject.toJSONString(result));
        // AssertResult(result.getErrorCode());
    }

    @Test
    public void getTagEmployeeInfoList() {
        Result<QyweixinTagEmployeeList> result = qyweixinAccountSyncService.getTagEmployeeInfoList("82335",
                "dkdf3684b6720635f7", "2");
        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> rsp = qyWeixinManager.getUserInfo("dkdf3684b6720635f7",
                "wpwx1mDAAA2CCu5vnGOtDBWK6HuIwv_w",
                result.getData().getUserList().get(0).getUserId());
        log.info("trace getTagEmployeeInfoList result:{}", JSONObject.toJSONString(result));
    }

    @Test
    public void getDepartmentEmployeeList() {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> list = qyWeixinManager.getDepartmentEmployeeList("dkdf3684b6720635f7",
                "wpwx1mDAAA2CCu5vnGOtDBWK6HuIwv_w",
                "2");
        System.out.println(list);
    }

    @Test
    public void getEmployeeInfoBatch() {
        Result<List<QyweixinEmployeeInfo>> result = qyweixinAccountSyncService.getEmployeeInfoBatch("85471", "wx88a141937dd6f838", Lists.newArrayList("wowx1mDAAAdv8q0lzp_Mf9bW-HaaAsgw"));
        log.info("trace getDepartmentEmployeeInfoList result:{}", JSONObject.toJSONString(result));
        //AssertResult(result.getErrorCode());
    }

    @Test
    public void getEmployeeInfoBatchNew() {
        Result<List<QyweixinEmployeeInfo>> result = qyweixinAccountSyncService.getEmployeeInfoBatch(fsEa,
                Lists.newArrayList("xyy"));
        log.info("trace getDepartmentEmployeeInfoList result:{}", JSONObject.toJSONString(result));
        //AssertResult(result.getErrorCode());
    }

    //    @Test
    public void getUpdateUserInfo() {
        String body = "{\"addAllowParty\":[\"4\"],\"addAllowTag\":[\"1\"],\"addAllowUser\":[\"6121728\",\"6121757\"],\"appId\":\"wx4c7edab730f4fdc9\",\"eventType\":\"app_privilege\",\"fsEa\":\"58463\",\"reduceAllowParty\":[\"2\"],\"reduceAllowTag\":[\"\"],\"reduceAllowUser\":[\"XiaoZhi\",\"kiki\"],\"source\":\"qywx\"}";
        QyweixinUpdateOrganizationEvent qyweixinUpdateOrganizationEvent = JSONObject.parseObject(body, QyweixinUpdateOrganizationEvent.class);

        String fsEa = qyweixinUpdateOrganizationEvent.getFsEa();
        String appId = qyweixinUpdateOrganizationEvent.getAppId();
        Result<QyweixinTagEmployeeList> result = qyweixinAccountSyncService.getTagEmployeeInfoList(fsEa, appId,
                qyweixinUpdateOrganizationEvent.getAllowTag().stream().collect(Collectors.joining(",")));
        log.info("trace getTagEmployeeInfoList result:{}", JSONObject.toJSONString(result));

        Result<List<QyweixinEmployeeInfo>> result2 = qyweixinAccountSyncService.getDepartmentEmployeeInfoList(fsEa, appId,
                qyweixinUpdateOrganizationEvent.getAddAllowParty().stream().collect(Collectors.joining(",")));
        log.info("trace getDepartmentEmployeeInfoList result:{}", JSONObject.toJSONString(result2));

        Result<List<QyweixinEmployeeInfo>> result3 = qyweixinAccountSyncService.getEmployeeInfoBatch(fsEa, appId,
                qyweixinUpdateOrganizationEvent.getAddAllowUser());
        log.info("trace getEmployeeInfoBatch result:{}", JSONObject.toJSONString(result3));
    }

    @Test
    public void getAuthorizedAppInfo() {
//        Result<List<QyweixinEmployeeInfo>> result = qyweixinAccountSyncService.getDepartmentEmployeeInfoList(fsEa, appId, "1");
//        log.info("trace getDepartmentEmployeeInfoList result:{}", JSONObject.toJSONString(result));

        Result<List<QyweixinApp>> result = qyweixinAccountSyncService.getAuthorizedAppInfo("81961");
        log.info("trace getAuthorizedAppInfo result:{}", JSONObject.toJSONString(result));
        // AssertResult(result.getErrorCode());
    }

    @Test
    public void getQyweixinCurrentUser() {
        Map<String, Object> paraMap = new HashMap<>();
        String ticket = "b0cfff4eb657efafa31c83cf1fe5701b";
        paraMap.put("ticket", ticket);
        Result<QyweixinEmployeeInfo> result = qyweixinAccountSyncService.getQyweixinCurrentUser(paraMap);
        log.info("trace getQyweixinCurrentUser result:{}", JSONObject.toJSONString(result));
        //AssertResult(result.getErrorCode());
    }


    @Test
    public void getQyweixinCurrentUserByAuthCode() {
        Map<String, Object> paraMap = new HashMap<>();
        String AuthCode = "zgHo3M-l5_79iwdgqVD0RwK_5iTrgz7luPYVb9qhGo1XSHpJLFOuL6FejSOPn3V2GWtGK9cvQy2yBMs9rLz_xaJqzg_4srHofpJSYfwE4CE";
        paraMap.put("auth_code", AuthCode);
        Result<QyweixinEmployeeInfo> result = qyweixinAccountSyncService.getQyweixinCurrentUser(paraMap);
        log.info("trace getQyweixinCurrentUser result:{}", JSONObject.toJSONString(result));
        //AssertResult(result.getErrorCode());
    }

    @Test
    public void createJsapiSignature() {
        String url = "https://www.ceshi112.com/hcrm/wechat";
        String appId = "wx88a141937dd6f838";
        Result<QyweixinJsapiSignature> result = qyweixinAccountSyncService.createJsapiSignature(url,
                "74860",
                "wx88a141937dd6f838");
        log.info("trace createJsapiSignature result:{}", JSONObject.toJSONString(result));
        // AssertResult(result.getErrorCode());
    }

    //userid: wowx1mDAAA6IGljSUdTDz02E4QUaOpZg（多层嵌套）  wowx1mDAAAoznw4tehYOnEMUZst87UzA  corpid: ww7ccab8b94a37781e
    @Test
    public void getQyweixinExternalContacts() {
        Result<List<QyweixinExternalContactInfo>> result = qyweixinAccountSyncService.getQyweixinExternalContacts("dkdf3684b6720635f7",
                "wpwx1mDAAAEkT4RFWyauSxOiN4Xb-R8g",null,
                "E.74860.1000",
                Lists.newArrayList("wmMRLhDAAA6xDu3L-YzOZg9Zwc-48nYA"));
        log.info("getQyweixinExternalContacts result:{}", JSONObject.toJSONString(result));

        //gson转换嵌套多层list的时候会抛错，改用fastjson可以
//        String str = "{\"errcode\":0,\"errmsg\":\"ok\",\"external_contact\":{\"external_userid\":\"wowx1mDAAA6IGljSUdTDz02E4QUaOpZg\",\"name\":\"灵当CRM资深客服-贲园园\",\"type\":2,\"external_profile\":{\"external_attr\":[{\"type\":0,\"name\":\"灵当简介\",\"text\":{\"value\":\"2008年成立，10年行业经验\"}},{\"type\":1,\"name\":\"灵当官网\",\"web\":{\"url\":\"http://www.51mis.com\",\"title\":\"灵当CRM\"}}]},\"corp_name\":\"灵当科技\",\"corp_full_name\":\"上海灵当信息科技有限公司\",\"gender\":2},\"follow_user\":[{\"userid\":\"GuMeng\",\"remark\":\"\",\"description\":\"\",\"createtime\":1546479752},{\"userid\":\"berry\",\"remark\":\"\",\"description\":\"\",\"createtime\":1546500101}]}";
//        Gson gson = new Gson();
//        QyweixinExternalContactRsp qyweixinExternalContactRsp =JSONObject.parseObject(str,QyweixinExternalContactRsp.class);
//        System.out.println(qyweixinExternalContactRsp);
    }

    private void AssertResult(String errorCode) {
        Assert.assertEquals("s120050000", errorCode);
    }


    public void sendMessage() {

        String data = "{\"avatar\":\"http://p.qlogo.cn/bizmail/JiaQiaZOdbQicibtjvI4zINiazK01xGff3R1Al4ibYkYvar3iaWp3L1IZahLg/0\",\"corpId\":\"ww4ba39487c1f49492\",\"corpName\":\"测试小刘企业\",\"privilege\":{\"allowParty\":[1],\"allowTag\":[1],\"allowUser\":[\"6121728\",\"06221101\"]},\"appId\":\"wx4c7edab730f4fdc9\",\"userId\":\"LiuWei3\",\"userName\":\"刘威\"}";

        String TAG_ENTERPRISE = "qywx_enterprise_order";
        QyweixinEnterpriseOrder qyweixinAddEnterprise = new Gson().fromJson(data, QyweixinEnterpriseOrder.class);
        Message msg = new Message();
        msg.setTags(TAG_ENTERPRISE);
        msg.setBody(qyweixinAddEnterprise.toProto());
//        SendResult sendResult = qywxEventNotifyMQSender.send(msg);

    }

//    @Test
//    public void queryContractList() {
//        GenerateSettingVo vo = new GenerateSettingVo();
//        vo.setEa("82236");
//        vo.setFsTenantId(82236);
//        vo.setQywxCorpId("wwbc77ff71db9569db");
//        vo.setCorpSecret("QTN3_k3Y8OhxIOkkrdTMN0lPc0pXQA8bO_PshfaA1Fc");
//        Result<List<QyweixinExternalContactInfo>> listResult = qyweixinAccountSyncService.queryExternalContactList(vo, 1000);
//        System.out.println("QyweixinAccountSyncTest.queryContractList" + listResult);
//    }

    @Test
    public void testRedis() {
        redisDataSource.getRedisClient().get("qyweixin_suite_ticket_key_wx88a141937dd6f838");
        Long time = redisDataSource.getRedisClient().ttl("qyweixin_suite_ticket_key_wx88a141937dd6f838");
        System.out.println(time);
    }

    @Test
    public void testQuery() {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinExternalContact> qyweixinExternalContactRsp = qyWeixinManager.queryExternalAccount("8E7JLLDWWKlBxW-uJwrBCdNpf_trqXB0M0NMwRg4pXw", "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "wmwx1mDAAAEHC7C10PhMXCGverrKMlKQ");
        System.out.println(qyweixinExternalContactRsp);
    }

    @Test
    public void queryQywxSync(){
        Result<QyweixinExternalContactInfo> qyweixinExternalContactInfoResult = qyweixinAccountSyncService.queryExternalContactsForSelf("wwbc77ff71db9569db", "xgNb4UN36YUlIYKeml2wMIn1yGiRyrx7WpmBUNmeNvM", "***********");
        System.out.println(qyweixinExternalContactInfoResult);
    }



    @Test
    public void getCorpBindInfo() {
        Result<QyweixinCorpBindInfo> result = qyweixinAccountSyncService.getCorpBindInfo("ww32e8d3df5d0ef18c","wx88a141937dd6f838");
        System.out.println(result);
    }

//    @Test
//    public void testwitchExternalContactEmployeeIdS() {
//        //qyweixinAccountSyncService.test();
//        //wmwx1mDAAAkUNtFz4RhoGUiIyrs9wPxQ
//        //wowx1mDAAAYlWXMuLpJHBjKkdTVvprtg
//        //wowx1mDAAAos4LULh9mffdd-muH-0eJw
//        Result<List<QyweixinExternalUserIdInfo>> list = qyweixinAccountSyncService.switchExternalContactEmployeeId2("74410",
//                Lists.newArrayList("wmwx1mDAAAvIj-9JoHBfJ9upRwJw8Azg", "wmwx1mDAAAv_RrOnpYfJ2UzSp_at4owg"),null);
//        System.out.println(list);
//
//        //{"errcode":48002,"errmsg":"api forbidden, hint: [1646138773513970312309090], from ip: **************, more info at https://open.work.weixin.qq.com/devtool/query?e=48002","items":[]}
//    }
//
//    @Test
//    public void testFinishExternalMigration () {
//        Result<Void> result = qyweixinAccountSyncService.finishExternalMigration("82777");
//        System.out.println(result);
//    }
//
//    @Test
//    public void updateTag() {
//        String xml = "<xml><ToUserName><![CDATA[wwcac03c775f00d9ae]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>**********</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[change_contact]]></Event><ChangeType><![CDATA[update_tag]]></ChangeType><TagId>1</TagId><DelPartyItems><![CDATA[3]]></DelPartyItems></xml>";
//        //TagChangeEventXml tagChangeEventXml = XmlParser.fromXml(xml, TagChangeEventXml.class);
//        TagChangeEventXml tagChangeEventXml = XStreamUtils.parseXml(xml, TagChangeEventXml.class);
//        QyweixinMsgBaseXml baseMsgXml = XStreamUtils.parseXml(xml, QyweixinMsgBaseXml.class);
//        String infoType = StringUtils.isNotEmpty(baseMsgXml.getInfoType()) ? baseMsgXml.getInfoType() : tagChangeEventXml.getEvent();
//        qyweixinGatewayInnerService.updateTag(baseMsgXml, tagChangeEventXml, infoType, xml);
//    }

//    @Test
//    public void saveEmployeeAccountBind() {
//        qyweixinGatewayInnerService.saveEmployeeAccountBind("wwcac03c775f00d9ae","");
//    }

    @Test
    public void testExternalContactTransferCustomer() {
        QyweixinTransferCustomerInfo qyweixinTransferCustomerInfo = new QyweixinTransferCustomerInfo();
        qyweixinTransferCustomerInfo.setEa("84883");
        qyweixinTransferCustomerInfo.setTakeoverUserId("1035");
        qyweixinTransferCustomerInfo.setHandoverUserId("1021");
        qyweixinTransferCustomerInfo.setExternalUserId(Lists.newArrayList("wmwx1mDAAAT5X0BAeycZzIzm1215otGA"));
        Result<List<QyweixinTransferCustomerResult>> result = qyweixinAccountSyncService.externalContactTransferCustomer(qyweixinTransferCustomerInfo, false);
        System.out.println(result);
    }

    @Test
    public void testExternalContactTransferResult() {
        QyweixinTransferCustomerStatusInfo qyweixinTransferCustomerInfo = new QyweixinTransferCustomerStatusInfo();
        qyweixinTransferCustomerInfo.setEa("84883");
        qyweixinTransferCustomerInfo.setTakeoverUserId("1035");
        qyweixinTransferCustomerInfo.setHandoverUserId("1021");
        Result<QyweixinTransferCustomerStatusResult> result = qyweixinAccountSyncService.externalContactTransferResult(qyweixinTransferCustomerInfo, false);
        System.out.println(result);
    }

    @Data
    class Pojo implements Serializable {

        private String test;

    }

    @Test
    public void testGroupList() {
        List<Pojo> list = new LinkedList<>();
        Pojo pojo = new Pojo();
        pojo.setTest("test");
        list.add(pojo);
        Map<Integer, List<Pojo>> map = qyWeixinManager.groupList(list);
        System.out.println(map);
        System.out.println("-------------------");
        System.out.println(map.get(0));
        List<Pojo> list1 = map.get(1);
        System.out.println(list1);

    }

    @Test
    public void testExternalContactTransferGroupChat() {
        QyweixinTransferGroupChatInfo transferGroupChatInfo = new QyweixinTransferGroupChatInfo();
        transferGroupChatInfo.setEa("84883");
        transferGroupChatInfo.setNewOwner("1035");
        transferGroupChatInfo.setChatIdList(Lists.newArrayList("123"));
        Result<List<QyweixinTransferGroupChatResult>> result = qyweixinAccountSyncService.externalContactTransferGroupChat(transferGroupChatInfo, false);
        System.out.println(result);
    }
    @Test
    public void testUnassignedExternalContact() {
        QyweixinTransferGroupChatInfo transferGroupChatInfo = new QyweixinTransferGroupChatInfo();
        transferGroupChatInfo.setEa("82777");
        transferGroupChatInfo.setNewOwner("wowx1mDAAAMdB1vuVmKYTplHe-rEsDFQ");
        transferGroupChatInfo.setChatIdList(Lists.newArrayList("wrwx1mDAAAstYD6ywBjZOXLgaVdilNyA"));

        QyweixinUnassignedExternalContactInfo externalContactInfo = new QyweixinUnassignedExternalContactInfo();
        externalContactInfo.setEa("84883");
        Result<QyweixinUnassignedExternalContactResult> result = qyweixinAccountSyncService.unassignedExternalContact(externalContactInfo);
        System.out.println(result);
    }

    @Test
    public void syncExcel() throws IOException {
        String fileName = "D:\\fxiaoke\\WXWork\\testExcel\\DepqartmentAndEmployee.xlsx";
        for (int i = 0; i < 2; i++) {
            FileInputStream fileInputStream = new FileInputStream(fileName);
            ReadExcel.Arg<Map<Integer,String>> arg = new ReadExcel.Arg<>();
            BaseListener<Map<Integer, String>> listen = new BaseListener<Map<Integer, String>>() {
            };
            arg.setExcelListener(listen);
            arg.setInputStream(fileInputStream);
            arg.setSheetName(i == 0 ? "department" : "employee");
            fileManager.readExcelBySheetName(arg);
            System.out.println(listen);
        }

    }

//    @Test
//    public void testUpdateCorpInfo() {
//        qyweixinAccountSyncService.updateCorpInfo("82777");
//    }

    @Test
    public void testTransferExternalContact() {
        QyweixinTransferCustomerInfo qyweixinTransferCustomerInfo = new QyweixinTransferCustomerInfo();
        qyweixinTransferCustomerInfo.setEa("82777");
        qyweixinTransferCustomerInfo.setHandoverUserId("1006");
        qyweixinTransferCustomerInfo.setTakeoverUserId("1007");
        qyweixinTransferCustomerInfo.setExternalApiName("apiName");
        qyweixinTransferCustomerInfo.setExternalUserName("小贝贝");
        qyweixinTransferCustomerInfo.setHandoverDeptId("1001");
        qyweixinTransferCustomerInfo.setHandoverDeptName("研发部");
        qyweixinTransferCustomerInfo.setTakeoverDeptId("1002");
        qyweixinTransferCustomerInfo.setTakeoverDeptName("测试部");
        qyweixinTransferCustomerInfo.setExternalNickname("啦啦啦@微信");
        qyweixinTransferCustomerInfo.setExternalUserId(Lists.newArrayList("wmwx1mDAAAAkn-AFiQhjQX9K3SFrpqPQ"));
        Result<List<QyweixinTransferCustomerResult>> result = qyweixinAccountSyncService.transferExternalContact(qyweixinTransferCustomerInfo);
        System.out.println(result);
    }

//    @Test
//    public void testDeleteExternalContactTransfers() {
//        qyweixinAccountSyncService.deleteExternalContactTransfers(Lists.newArrayList(41, 45));
//    }

    @Test
    public void testGetEmployeeBasicInfoByMobile() {
        Result<FsEmployeeBasicInfo> info = qyweixinAccountSyncService.getEmployeeBasicInfoByMobile("82777", "***********");
        System.out.println(info);
    }

    @Test
    public void getCorpBind2() {
        Result<QyweixinCorpBindInfo> result = qyweixinAccountSyncService.getCorpBind2("84883", ConfigCenter.crmAppId,null);
        System.out.println(result);
    }

    @Test
    public void getUserInfoByUserId() {
        Result<List<QyweixinEmployeeInfo>> listResult = qyweixinAccountSyncService.batchGetEmployeeInfo("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA"
                , Lists.newArrayList("wowx1mDAAAfPMspC9iKzWLx16d_zjcRA", "wowx1mDAAA5X1U9Sp8rNyqA_-eAXs6mQ", "wowx1mDAAAJtVnCITQZYSX2AI5ASmwtA", "testfubufu"));
        System.out.println(listResult);
    }

    @Test
    public void testPlainToEncryption() {
        qyweixinAccountSyncService.plainToEncryption("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", Lists.newArrayList("wmQZ1uJQAA2H3kF5Gu2WgKb1LxwKbbsQ", "wmQZ1uJQAALxI0q80zKmkeIlSNOFaEQQ"));
    }

    @Test
    public void testAutoIdToOpenid() {
        qyweixinAccountSyncService.autoIdToOpenid();
    }

    @Test
    public void testGetOpenIds2() {
        Result<List<QyweixinGetOpenIdResult>> dd = qyweixinAccountSyncService.getOpenIds2("84883",
                Lists.newArrayList("chenzongxin", "miss.si", "test"),
                Lists.newArrayList("wmwx1mDAAAEHC7C10PhMXCGverrKMlKQ",  "wowx1mDAAAfPMspC9iKzWLx16d_zjcRA", "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ"),
                null);
        System.out.println(dd);
    }

    @Test
    public void testGetFsEmpUser() {
        Result<List<EmployeeDto>> fsEmpUser = qyweixinAccountSyncService.getFsEmpUser("84883", 1000, Lists.newArrayList(1022));
        System.out.println(fsEmpUser);
    }

    @Test
    public void uploadOaConnectorDataTest() {
        OaConnectorDataModel oaConnectorDataModel = new OaConnectorDataModel();
        oaConnectorDataModel.setChanel("qywx");
        oaConnectorDataModel.setDataType("login");
        Result<Void> uploadOaConnectorData = qyweixinAccountSyncService.uploadOaConnectorData(oaConnectorDataModel);
        System.out.println(uploadOaConnectorData);
    }

    @Test
    public void batchGetEmployeeInfo21() {
        Result<List<QyweixinEmployeeInfoResult>> result = qyweixinAccountSyncService.batchGetEmployeeInfo21("84883",
                Lists.newArrayList("1021", "122222", "1113"),
                null);
        System.out.println(result);
    }

    @Test
    public void getEnterpriseTrialInfo() {
        Result<EnterpriseTrialInfo> result = qyweixinAccountSyncService.getEnterpriseTrialInfo("bdjdsx6095");
        System.out.println(result);
    }

    @Test
    public void getFsCurEmployeeDetailInfo() {
        Result<FsEmployeeDetailInfo> result = qyweixinAccountSyncService.getFsCurEmployeeDetailInfo(84883, 1021);
        System.out.println(result);
    }

    @Test
    public void getDepartmentInfo() {
        Result<QyweixinDepartmentInfo> result = qyweixinAccountSyncService.getDepartmentInfo("84883", "1");
        System.out.println(result);
    }

    @Test
    public void getAppAccessToken() {
        Result<String> result = qyweixinAccountSyncService.getAppAccessToken("test", repAppId);
        System.out.println(result);
    }

    @Test
    public void autoGetUserAndDepartmentInfo() {
//        List<OaConnectorSyncEventDataDoc> syncEventDataDocs = oaConnectorSyncEventDataMongoDao.listByTenantId(0, 100);
//        oaConnectorSyncEventDataMongoDao.deleteByIds(syncEventDataDocs.stream().map(v -> v.getId().toString()).collect(Collectors.toList()));
        String xml = "<xml><SuiteId><![CDATA[wx105357ca56c6db18]]></SuiteId><InfoType><![CDATA[change_auth]]></InfoType><TimeStamp>**********</TimeStamp><AuthCorpId><![CDATA[wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA]]></AuthCorpId><State><![CDATA[abc]]></State><ExtraInfo></ExtraInfo></xml>";
        String xml1 = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><InfoType><![CDATA[change_auth]]></InfoType><TimeStamp>**********</TimeStamp><AuthCorpId><![CDATA[wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA]]></AuthCorpId><State><![CDATA[abc]]></State><ExtraInfo></ExtraInfo></xml>";
        String xml2 = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><InfoType><![CDATA[change_auth]]></InfoType><TimeStamp>**********</TimeStamp><AuthCorpId><![CDATA[wpwx1mDAAAOvtbJxYM8CScaAVqKJlC-Q]]></AuthCorpId><State><![CDATA[abc]]></State><ExtraInfo></ExtraInfo></xml>";
        String xml3 = "<xml><SuiteId><![CDATA[dkdf3684b6720635f7]]></SuiteId><InfoType><![CDATA[change_auth]]></InfoType><TimeStamp>**********</TimeStamp><AuthCorpId><![CDATA[wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA]]></AuthCorpId><State><![CDATA[abc]]></State><ExtraInfo></ExtraInfo></xml>";
        saveAppEvent("wpwx1mDAAAOvtbJxYM8CScaAVqKJlC-Q","wx88a141937dd6f838","change_auth", xml2);
        saveAppEvent("wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA","wx88a141937dd6f838","change_auth", xml1);
        saveAppEvent("wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA","wx105357ca56c6db18","change_auth", xml);
        saveAppEvent("wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA","dkdf3684b6720635f7","change_auth", xml3);
        saveAppEvent("wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA","wx88a141937dd6f838","change_contact", xml3);
        String xml4 = "<xml><ToUserName><![CDATA[wpwx1mDAAALG1GObCJSt5_5ruswcU9gA]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>**********</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[change_contact]]></Event><ChangeType>delete_user</ChangeType><UserID><![CDATA[wowx1mDAAAS0RUKBnFw-c6aMUVJv_A_A]]></UserID></xml>";
        saveAppEvent("wpwx1mDAAALG1GObCJSt5_5ruswcU9gA", "dkdf3684b6720635f7", "change_contact", xml4);

        Result<Void> result = qyweixinAccountSyncService.autoGetUserAndDepartmentInfo();
        System.out.println(result);
    }

    private void saveAppEvent(String outEa, String appId, String infoType, String plainMsg) {
        log.info("QyweixinAppEventTemplate.saveAppEvent,outEa={}, appId={}, infoType={}, plainMsg={}", outEa, appId, infoType, plainMsg);
        OaConnectorSyncEventDataDoc dataDoc = new OaConnectorSyncEventDataDoc();
        dataDoc.setId(ObjectId.get());
        dataDoc.setChannel(ChannelEnum.qywx);
        dataDoc.setAppId(appId);
        dataDoc.setOutEa(outEa);
        dataDoc.setEventType(infoType);
        dataDoc.setEvent(plainMsg);
        dataDoc.setStatus(0);
        dataDoc.setCreateTime(System.currentTimeMillis());
        dataDoc.setUpdateTime(System.currentTimeMillis());
        BulkWriteResult bulkWriteResult = oaConnectorSyncEventDataMongoDao.batchReplace(Lists.newArrayList(dataDoc));
        log.info("QyweixinAppEventTemplate.saveAppEvent,bulkWriteResult={}", bulkWriteResult);
    }
}
