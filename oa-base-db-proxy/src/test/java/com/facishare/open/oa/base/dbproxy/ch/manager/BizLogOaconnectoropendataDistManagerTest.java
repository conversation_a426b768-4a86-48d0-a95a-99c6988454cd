package com.facishare.open.oa.base.dbproxy.ch.manager;

import com.facishare.open.oa.base.dbproxy.BaseTest;
import com.facishare.open.oa.base.dbproxy.ch.entity.BizLogOaconnectoropendataDistBo;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class BizLogOaconnectoropendataDistManagerTest extends BaseTest {
    @Resource
    private BizLogOaconnectoropendataDistManager bizLogOaconnectoropendataDistManager;

    @Test
    public void getEntity() {
        List<BizLogOaconnectoropendataDistBo> enterpriseCreateError = bizLogOaconnectoropendataDistManager.findEnterpriseCreateError("qywx", "enterpriseCreate", "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
        System.out.println(enterpriseCreateError);
    }

}
