package com.facishare.open.oa.base.dbproxy;

import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring-test/applicationContext-test.xml"})
public class BaseTest {

    @BeforeClass
    public static void SetUp(){
        System.setProperty("process.profile", "fstest");
        System.setProperty("process.name","oa-base-dbproxy");
    }

    @Test
    public void emptyTest() {
        System.out.println("test passed");
    }
}