-- 为outer_oa_employee_data表添加索引
CREATE INDEX IF NOT EXISTS idx_employee_data_channel_out_ea_app_id ON outer_oa_employee_data (channel, out_ea, app_id);
CREATE INDEX IF NOT EXISTS idx_employee_data_out_user_id ON outer_oa_employee_data (out_user_id);

-- 为outer_oa_employee_bind表添加索引
CREATE INDEX IF NOT EXISTS idx_employee_bind_channel_out_ea_app_id ON outer_oa_employee_bind (channel, out_ea, app_id);
CREATE INDEX IF NOT EXISTS idx_employee_bind_fs_ea ON outer_oa_employee_bind (fs_ea);
CREATE INDEX IF NOT EXISTS idx_employee_bind_out_emp_id ON outer_oa_employee_bind (out_emp_id);
CREATE INDEX IF NOT EXISTS idx_employee_bind_bind_status ON outer_oa_employee_bind (bind_status);

-- 添加复合索引以优化未绑定员工查询
CREATE INDEX IF NOT EXISTS idx_employee_bind_composite ON outer_oa_employee_bind (channel, out_ea, app_id, out_emp_id, fs_ea, bind_status); 