<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context" 
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd 
                           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd 
                           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd">

    <context:component-scan base-package="com.facishare.open.oa.base.dbproxy"/>

    <import resource="classpath:spring/db-mongo-store.xml"/>
    <import resource="classpath:spring/cus-crmrest.xml"/>
    <import resource="classpath:spring/ei-ea-converter.xml"/>

    <!-- PostgreSQL数据库配置 -->
    <bean id="oaBasePgDB" class="com.github.mybatis.spring.DynamicDataSource" 
          p:configName="fs-erpdss-oa-base-config" 
          p:sectionNames="pg"/>

    <!-- MyBatis-Plus分页插件配置 -->
    <bean id="mybatisPlusInterceptor" class="com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor">
        <property name="interceptors">
            <list>
                <bean class="com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor">
                    <constructor-arg name="dbType" value="POSTGRE_SQL"/>
                    <property name="overflow" value="false"/>
                    <property name="maxLimit" value="500"/>
                </bean>
            </list>
        </property>
    </bean>

    <!-- PostgreSQL SqlSessionFactory配置 -->
    <bean id="oaBasePgSqlSessionFactory" class="com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean">
        <property name="dataSource" ref="oaBasePgDB"/>
        <property name="typeAliasesPackage" value="com.facishare.open.oa.base.dbproxy.pg.entity"/>
        <property name="configLocation" value="classpath:spring/db-pg-mybatis-config.xml"/>
        <property name="mapperLocations" value="classpath*:pg-mapper/*.xml"/>
        <property name="globalConfig" ref="globalConfig"/>
        <property name="plugins">
            <array>
                <!-- 加密拦截器 -->
                <bean class="com.facishare.open.oa.base.dbproxy.interceptor.EncryptInterceptor"/>
                <!-- 主从分离拦截器 -->
                <bean class="com.github.mybatis.interceptor.MasterSlaveInterceptor"/>
                <!-- 分页插件 -->
                <ref bean="mybatisPlusInterceptor"/>
                <!-- 解密拦截器 -->
                <bean class="com.facishare.open.oa.base.dbproxy.interceptor.DecryptInterceptor"/>       </array>
        </property>
    </bean>
    <bean id="globalConfig" class="com.baomidou.mybatisplus.core.config.GlobalConfig">
        <property name="dbConfig" ref="oaBaseDbConfig"/>
    </bean>

    <bean id="oaBaseDbConfig" class="com.baomidou.mybatisplus.core.config.GlobalConfig.DbConfig">
        <property name="keyGenerators">
            <list>
                <bean class="com.baomidou.mybatisplus.extension.incrementer.H2KeyGenerator"/>
            </list>
        </property>
    </bean>
    <!-- ClickHouse数据库配置 -->
    <bean id="oaBaseChDB" class="com.github.mybatis.spring.DynamicDataSource">
        <property name="configName" value="eye-clickhouse-db"/>
    </bean>

    <!-- ClickHouse SqlSessionFactory配置 -->
    <bean id="clickHouseSqlSessionFactory" class="com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean">
        <property name="dataSource" ref="oaBaseChDB"/>
        <property name="typeAliasesPackage" value="com.facishare.open.oa.base.dbproxy.ch.entity"/>
        <property name="configLocation" value="classpath:spring/db-ch-mybatis-config.xml"/>
    </bean>

    <!-- SQL解析器配置 -->
    <bean id="oaBaseDbCountSqlParser" class="com.baomidou.mybatisplus.extension.plugins.pagination.optimize.JsqlParserCountOptimize">
        <property name="optimizeJoin" value="true"/>
    </bean>

    <!-- Mapper扫描配置 -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.facishare.open.oa.base.dbproxy.pg.mapper"/>
        <property name="sqlSessionFactoryBeanName" value="oaBasePgSqlSessionFactory"/>
    </bean>

    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.facishare.open.oa.base.dbproxy.ch.mapper"/>
        <property name="sqlSessionFactoryBeanName" value="clickHouseSqlSessionFactory"/>
    </bean>

    <!-- 事务管理器配置 -->
    <bean id="oaBaseTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="oaBasePgDB"/>
    </bean>

    <!-- 开启事务注解 -->
    <tx:annotation-driven transaction-manager="oaBaseTransactionManager" proxy-target-class="true"/>

</beans>