<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">

    <bean id="transferDataController" class="com.facishare.open.oa.base.dbproxy.transfer.web.TransferDataController"/>
    <!-- 配置消息转换器支持JSON -->
    <bean class="org.springframework.web.servlet.mvc.annotation.AnnotationMethodHandlerAdapter">
        <property name="messageConverters">
            <list>
                <bean class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter"/>
            </list>
        </property>
    </bean>

    <bean id="transferMongoService" class="com.facishare.open.oa.base.dbproxy.transfer.service.TransferoService"/>

    <bean id="dataTransferMgr" class="com.facishare.open.oa.base.dbproxy.transfer.service.ErpDataTransferMgr"
          p:dataTransferDaoConfigName="fs-erpdss-oa-base-config" p:sectionNames="oaBaseMongo">
        <property name="enterpriseService">
            <bean class="com.facishare.open.oa.base.dbproxy.transfer.overwrite.EnterpriseServiceImpl"/>
        </property>
    </bean>

</beans>