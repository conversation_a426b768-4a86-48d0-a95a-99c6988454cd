package com.facishare.open.feishu.syncapi.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ExternalCalendarEventResult implements Serializable {
    private static final long serialVersionUID = 1L;

    @JSONField(name = "event")
    private EventDetails event;

    @Data
    public static class EventDetails implements Serializable {
        private static final long serialVersionUID = 1L;

        @J<PERSON><PERSON>ield(name = "event_id")
        private String eventId;

        @JSONField(name = "organizer_calendar_id")
        private String organizerCalendarId;

        private String summary;
        private String description;

        @J<PERSON>NField(name = "need_notification")
        private Boolean needNotification;

        @J<PERSON><PERSON>ield(name = "start_time")
        private TimeInfo startTime;

        @JSONField(name = "end_time")
        private TimeInfo endTime;

        private VChat vchat;
        private String visibility;

        @JSO<PERSON>ield(name = "attendee_ability")
        private String attendeeAbility;

        @J<PERSON><PERSON>ield(name = "free_busy_status")
        private String freeBusyStatus;

        private Location location;
        private Integer color;
        private List<Reminder> reminders;
        private String recurrence;
        private String status;

        @J<PERSON><PERSON>ield(name = "is_exception")
        private Boolean isException;

        @JSONField(name = "recurring_event_id")
        private String recurringEventId;

        @JSONField(name = "create_time")
        private String createTime;

        private List<Schema> schemas;

        @JSONField(name = "event_organizer")
        private EventOrganizer eventOrganizer;

        @JSONField(name = "app_link")
        private String appLink;
    }

    @Data
    public static class TimeInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        private String date;
        private String timestamp;
        private String timezone;
    }

    @Data
    public static class VChat implements Serializable {
        private static final long serialVersionUID = 1L;

        @JSONField(name = "vc_type")
        private String vcType;

        @JSONField(name = "icon_type")
        private String iconType;

        private String description;

        @JSONField(name = "meeting_url")
        private String meetingUrl;

        @JSONField(name = "meeting_settings")
        private MeetingSettings meetingSettings;
    }

    @Data
    public static class MeetingSettings implements Serializable {
        private static final long serialVersionUID = 1L;

        @JSONField(name = "owner_id")
        private String ownerId;

        @JSONField(name = "join_meeting_permission")
        private String joinMeetingPermission;

        @JSONField(name = "assign_hosts")
        private List<String> assignHosts;

        @JSONField(name = "auto_record")
        private Boolean autoRecord;

        @JSONField(name = "open_lobby")
        private Boolean openLobby;

        @JSONField(name = "allow_attendees_start")
        private Boolean allowAttendeesStart;
    }

    @Data
    public static class Location implements Serializable {
        private static final long serialVersionUID = 1L;

        private String name;
        private String address;
        private Double latitude;
        private Double longitude;
    }

    @Data
    public static class Reminder implements Serializable {
        private static final long serialVersionUID = 1L;

        private Integer minutes;
    }

    @Data
    public static class Schema implements Serializable {
        private static final long serialVersionUID = 1L;

        @JSONField(name = "ui_name")
        private String uiName;

        @JSONField(name = "ui_status")
        private String uiStatus;

        @JSONField(name = "app_link")
        private String appLink;
    }

    @Data
    public static class EventOrganizer implements Serializable {
        private static final long serialVersionUID = 1L;

        @JSONField(name = "user_id")
        private String userId;

        @JSONField(name = "display_name")
        private String displayName;
    }
}
