package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.entity.AppInfoEntity;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;

/**
 * 应用服务
 * <AUTHOR>
 * @date 20220717
 */
public interface AppService {
    Result<Integer> updateAppInfo(AppInfoEntity entity);

    Result<OuterOaAppInfoEntity> getAppInfo(String outEa, String appId);
}