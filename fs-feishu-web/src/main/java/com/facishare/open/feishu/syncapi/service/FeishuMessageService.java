package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.enums.MsgTypeEnum;
import com.facishare.open.feishu.syncapi.enums.ReceiverIdTypeEnum;
import com.facishare.open.feishu.syncapi.result.Result;

import java.io.Serializable;
import java.util.List;

/**
 * 飞书机器人消息服务
 * <AUTHOR>
 * @date 20220726
 */
public interface FeishuMessageService extends Serializable {

    /**
     * 发送飞书消息
     * @param appId
     * @param tenantKey
     * @param msgType
     * @param receiveId
     * @param content
     * @return
     */
    Result<Void> send(String appId,
                      String tenantKey,
                      MsgTypeEnum msgType,
                      String receiveId,
                      String content);

    /**
     * 发送飞书消息
     * @param appId
     * @param tenantKey
     * @param msgType
     * @param receiveIdType
     * @param receiveId
     * @param content
     * @return
     */
    Result<Void> send(String appId,
                      String tenantKey,
                      MsgTypeEnum msgType,
                      ReceiverIdTypeEnum receiveIdType,
                      String receiveId,
                      String content);

    /**
     * 批量发送飞书消息
     * @param appId
     * @param tenantKey
     * @param msgType
     * @param openIdList
     * @param content
     * @return
     */
    Result<String> batchSend(String appId,
                             String tenantKey,
                             MsgTypeEnum msgType,
                             List<String> openIdList,
                             String content);

    /**
     * 批量发送飞书消息
     * @param appId
     * @param tenantKey
     * @param msgType
     * @param receiveIdType
     * @param openIdList
     * @param content
     * @return
     */
    Result<String> batchSend(String appId,
                             String tenantKey,
                             MsgTypeEnum msgType,
                             ReceiverIdTypeEnum receiveIdType,
                             List<String> openIdList,
                             String content);

    /**
     * 收到飞书订单后，发送欢迎消息给应用安装人员
     * @param appId
     * @param tenantKey
     * @param openUserId
     * @return
     */
    Result<Void> sendWelcomeMsg(String appId,
                                String tenantKey,
                                String openUserId);

    /**
     * 给指定的messageId发送消息
     * @param appId
     * @param tenantKey
     * @param messageId
     * @param content
     * @param msgType
     * @return
     */
    Result<Void> reply(String appId,
                       String tenantKey,
                       String messageId,
                       String content,
                       MsgTypeEnum msgType);
}
