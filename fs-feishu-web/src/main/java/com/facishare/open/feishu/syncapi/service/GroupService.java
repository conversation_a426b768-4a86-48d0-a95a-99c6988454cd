package com.facishare.open.feishu.syncapi.service;


import com.facishare.open.feishu.syncapi.model.Group.GetGroupListData;
import com.facishare.open.feishu.syncapi.model.Group.GetGroupMemberData;
import com.facishare.open.feishu.syncapi.model.Group.Group;
import com.facishare.open.feishu.syncapi.result.Result;

/**
 * 飞书对接-用户组接口
 * 作者：周健鑫
 * 时间：2022/5/26 10:01:26
 */

public interface GroupService {

    /**
     * 飞书对接-自建项目-获取用户组
     * @param appId 应用唯一标识，创建应用后获得
     * @param groupId   用户组id
     * @return  用户组信息
     */
    Result<Group> getGroup(String appId,String groupId);

    /**
     * 飞书对接-自建项目-获取用户组
     * @param appId 应用唯一标识，创建应用后获得
     * @param groupId   用户组id
     * @return  用户组信息
     */
    Result<Group> getGroup(String appId,String tenantKey,String groupId);

    /**
     * 飞书对接-自建项目-获取用户组列表
     * @param appId 应用唯一标识，创建应用后获得
     * @param type  用户组类型 可选值有：1->普通用户组 默认值：1
     * @param pageSize  分页大小 最大值：50
     * @param pageToken 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
     * @return  用户组列表信息
     */
    Result<GetGroupListData> getGroupList(String appId,Integer type, Integer pageSize, String pageToken);

    /**
     * 飞书对接-自建项目-获取用户组列表
     * @param appId 应用唯一标识，创建应用后获得
     * @param type  用户组类型 可选值有：1->普通用户组 默认值：1
     * @param pageSize  分页大小 最大值：50
     * @param pageToken 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
     * @return  用户组列表信息
     */
    Result<GetGroupListData> getGroupList(String appId,String tenantKey,Integer type, Integer pageSize, String pageToken);

    /**
     * 飞书对接-自建项目-获取用户组成员信息
     * @param appId 应用唯一标识，创建应用后获得
     * @param groupId   用户组id
     * @param pageSize  分页大小 最大值：50
     * @param pageToken 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
     * @param memberIdType  欲获取成员ID类型。当member_type =user时候，member_id_type表示user_id_type，可选值为open_id：member_type =user时候，表示用户的open_id union_id：member_type =user时候，表示用户的union_id user_id：member_type =user时候，表示用户的user_id，默认值为open_id
     * @param memberType    期待获取的用户组成员的类型，取值为 user 默认值为 user
     * @return  用户组中成员信息
     */
    Result<GetGroupMemberData> getGroupMemberList(String appId,String groupId, Integer pageSize, String pageToken, String memberIdType, String memberType);

    /**
     * 飞书对接-自建项目-获取用户组成员信息
     * @param appId 应用唯一标识，创建应用后获得
     * @param groupId   用户组id
     * @param pageSize  分页大小 最大值：50
     * @param pageToken 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
     * @param memberIdType  欲获取成员ID类型。当member_type =user时候，member_id_type表示user_id_type，可选值为open_id：member_type =user时候，表示用户的open_id union_id：member_type =user时候，表示用户的union_id user_id：member_type =user时候，表示用户的user_id，默认值为open_id
     * @param memberType    期待获取的用户组成员的类型，取值为 user 默认值为 user
     * @return  用户组中成员信息
     */
    Result<GetGroupMemberData> getGroupMemberList(String appId,String tenantKey,String groupId, Integer pageSize, String pageToken, String memberIdType, String memberType);
}
