package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.info.CustomUrlInfo;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;

import java.util.List;

/**
 * 自定义服务
 */
public interface CustomService {
    Result<List<CustomUrlInfo>> getCustomUrls(ChannelEnum channelEnum, String tenantId);
}
