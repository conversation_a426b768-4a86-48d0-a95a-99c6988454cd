package com.facishare.open.feishu.syncapi.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ExternalCalendarEventAddAttendeeResult implements Serializable {
    private static final long serialVersionUID = 1L;

    @JSO<PERSON>ield(name = "attendees")
    private List<Attendee> attendees;

    @Data
    public static class Attendee implements Serializable {
        private static final long serialVersionUID = 1L;

        private String type;

        @JSONField(name = "attendee_id")
        private String attendeeId;

        @JSONField(name = "rsvp_status")
        private String rsvpStatus;

        @JSONField(name = "is_optional")
        private Boolean isOptional;

        @J<PERSON><PERSON>ield(name = "is_organizer")
        private Boolean isOrganizer;

        @JSONField(name = "is_external")
        private Boolean isExternal;

        @JSONField(name = "display_name")
        private String displayName;

        @JSONField(name = "chat_members")
        private List<ChatMember> chatMembers;

        @JSONField(name = "user_id")
        private String userId;

        @J<PERSON><PERSON>ield(name = "chat_id")
        private String chatId;

        @J<PERSON><PERSON><PERSON>(name = "room_id")
        private String roomId;

        @JSONField(name = "third_party_email")
        private String thirdPartyEmail;

        @JSONField(name = "operate_id")
        private String operateId;

        @JSONField(name = "resource_customization")
        private List<ResourceCustomization> resourceCustomization;
    }

    @Data
    public static class ChatMember implements Serializable {
        private static final long serialVersionUID = 1L;

        @JSONField(name = "rsvp_status")
        private String rsvpStatus;

        @JSONField(name = "is_optional")
        private Boolean isOptional;

        @JSONField(name = "display_name")
        private String displayName;

        @JSONField(name = "is_organizer")
        private Boolean isOrganizer;

        @JSONField(name = "is_external")
        private Boolean isExternal;
    }

    @Data
    public static class ResourceCustomization implements Serializable {
        private static final long serialVersionUID = 1L;

        @JSONField(name = "index_key")
        private String indexKey;

        @JSONField(name = "input_content")
        private String inputContent;

        @JSONField(name = "options")
        private List<Option> options;
    }

    @Data
    public static class Option implements Serializable {
        private static final long serialVersionUID = 1L;

        @JSONField(name = "option_key")
        private String optionKey;

        @JSONField(name = "others_content")
        private String othersContent;
    }
}
