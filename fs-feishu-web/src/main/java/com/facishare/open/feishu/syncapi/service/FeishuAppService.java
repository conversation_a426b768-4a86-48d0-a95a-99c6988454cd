package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.arg.QueryTenantAccessTokenArg;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.GetAppAdminUserListData;

public interface FeishuAppService {
    /**
     * 获取飞书应用管理员
     * @return
     */
    Result<GetAppAdminUserListData> getAdminUserList(String appId,String tenantKey);

    /**
     * 暴露token接口，其他业务不能通过dubbo或者dubbo转rest调用
     */

    Result<String> getTenantAccessToken(String channel, QueryTenantAccessTokenArg arg);
}
