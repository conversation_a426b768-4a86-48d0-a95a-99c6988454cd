package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.arg.CreateCustomerAndUpdateMappingArg;
import com.facishare.open.feishu.syncapi.arg.CreateOrderArg;
import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.entity.OrderInfoEntity;
import com.facishare.open.feishu.syncapi.model.event.FeishuOrderPaidEvent;
import com.facishare.open.feishu.syncapi.model.info.EnterpriseTrialInfo;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.QueryTenantInfoData;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaOrderInfoEntity;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCrmOrderMultipleProductArg;

import java.io.Serializable;
import java.util.List;

/**
 * 订单后台服务
 * <AUTHOR>
 * @date 20220719
 */
public interface OrderService extends Serializable {
    String SUCCESS = "success";
    String FAIL = "fail";

    /**
     * 查询企业绑定关系
     * @param outEa
     * @return
     */
    Result<List<OuterOaEnterpriseBindEntity>> getEnterpriseBindList(String outEa);

    /**
     * 新增或更新订单信息
     * @param event
     * @return
     */
    Result<OuterOaOrderInfoEntity> saveOrder(FeishuOrderPaidEvent event);

    Result<Void> createOrder(CreateOrderArg arg);

    Result<Void> saveOrderAndAddCrmOrder(FeishuOrderPaidEvent event);

    Result<QueryTenantInfoData> getFeishuEnterpriseInfo(String appId, String outEa);

    Result<UserData.User> getAppInstallerInfo(String appId, String outEa);

    Result<String> genFsEa(String enterpriseName);

    Result<Void> createCustomerAndUpdateMapping(CreateCustomerAndUpdateMappingArg arg);

    /**
     * 判断表里是否有已创建企业的绑定记录
     * @param ea
     * @return
     */
    Result<Boolean> isEnterpriseBind(String ea);

    /**
     * 更新企业和管理员中间表状态为正常状态
     * @param ea
     * @param adminUserId
     * @return
     */
    Result<Void> updateEnterpriseAndAdminMapping(String ea, String adminUserId);

    /**
     * 给应用开通人员发送欢迎消息
     * @param ea
     * @return
     */
    Result<Void> sendWelcomeMsg(String ea);

    /**
     * 初始化企业通讯录
     * @param ea
     * @return
     */
    Result<Void> initEnterpriseContacts(String ea);

//    /**
//     * 订单付款事件处理逻辑
//     * @param event
//     * @return
//     */
//    Result<String> onOrderPaid(FeishuOrderPaidEvent event);

//    /**
//     * 下单开通CRM企业
//     * @param entity
//     * @param installUserId
//     * @param installerName
//     * @return
//     */
//    Result<Void> openEnterprise(OrderInfoEntity entity,String installUserId,String installerName);


    Result<EnterpriseTrialInfo> getEnterpriseTrialInfo(String fsEa, String outEa);

    com.facishare.open.order.contacts.proxy.api.result.Result<Void> buyConnector(String fsEa,
                                                                                 String orderAmount,
                                                                                 Long beginTime,
                                                                                 Long endTime);

    com.facishare.open.order.contacts.proxy.api.result.Result<Void> buyConnectorConfigProductId(String fsEa,
                                                                                 String orderAmount,
                                                                                 Long beginTime,
                                                                                 Long endTime,String productId,Integer quantity);
}
