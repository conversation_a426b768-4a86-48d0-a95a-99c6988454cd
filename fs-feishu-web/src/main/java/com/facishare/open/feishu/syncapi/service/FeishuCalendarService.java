package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.model.externalCalendar.ExternalCalendarDetail;
import com.facishare.open.feishu.syncapi.model.externalCalendar.ExternalCalendarEvent;
import com.facishare.open.feishu.syncapi.model.externalCalendar.ExternalCalendarEventAddAttendeeDetail;
import com.facishare.open.feishu.syncapi.model.externalCalendar.ExternalCalendarEventDelAttendeeDetail;
import com.facishare.open.feishu.syncapi.result.ExternalCalendarEventAddAttendeeResult;
import com.facishare.open.feishu.syncapi.result.ExternalCalendarEventResult;
import com.facishare.open.feishu.syncapi.result.ExternalCalendarResult;
import com.facishare.open.feishu.syncapi.result.Result;

public interface FeishuCalendarService {
    Result<ExternalCalendarResult> createCalendars(String appId,
                                                   String outEa,
                                                   ExternalCalendarDetail externalCalendarDetail);



    Result<ExternalCalendarEventResult> createCalendarEvent(String appId,
                                                            String outEa,
                                                            String calendarId,
                                                            ExternalCalendarEvent externalCalendarEvent);

    Result<ExternalCalendarEventResult> updateCalendarEvent(String appId,
                                                            String outEa,
                                                            String calendarId,
                                                            String calendarEventId,
                                                            ExternalCalendarEvent externalCalendarEvent);

    Result<Void> deleteCalendarEvent(String appId,
                                     String outEa,
                                     String calendarId,
                                     String calendarEventId);

    Result<ExternalCalendarEventAddAttendeeResult> addCalendarEventAttendees(String appId,
                                                                             String outEa,
                                                                             String calendarId,
                                                                             String calendarEventId,
                                                                             ExternalCalendarEventAddAttendeeDetail externalCalendarEventAddAttendeeDetail);

    Result<Void> deleteCalendarEventAttendees(String appId,
                                              String outEa,
                                              String calendarId,
                                              String calendarEventId,
                                              ExternalCalendarEventDelAttendeeDetail externalCalendarEventDelAttendeeDetail);
}
