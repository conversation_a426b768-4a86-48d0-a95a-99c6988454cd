package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.model.jsapi.JsApiSignatureModel;
import com.facishare.open.feishu.syncapi.result.Result;

public interface JsApiService {
    /**
     * 获取飞书JsApi签名
     * @param appId
     * @param fsEa
     * @param url
     * @return
     */
    Result<JsApiSignatureModel> getJsApiSignature(String appId, String fsEa, String url);

    /**
     * 获取飞书JsApi签名
     * @param appId
     * @param fsEa
     * @param url
     * @return
     */
    Result<JsApiSignatureModel> getJsApiSignature2(String appId, String fsEa, String outEa, String url);
}
