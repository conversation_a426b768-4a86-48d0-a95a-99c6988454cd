package com.facishare.open.feishu.sync.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.feishu.sync.mapper.CalendarEventAttendeeMapper;
import com.facishare.open.feishu.syncapi.entity.CalendarEventAttendeeEntity;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class CalendarEventAttendeeManager {
    @Resource
    private CalendarEventAttendeeMapper calendarEventAttendeeMapper;

    public Integer insert(CalendarEventAttendeeEntity entity) {
        int count = calendarEventAttendeeMapper.insert(entity);
        LogUtils.info("CalendarEventAttendeeManager.insert,count={}",count);
        return count;
    }

    public Integer update(CalendarEventAttendeeEntity entity) {
        int count = calendarEventAttendeeMapper.updateById(entity);
        LogUtils.info("CalendarEventAttendeeManager.update,count={}",count);
        return count;
    }

    public List<CalendarEventAttendeeEntity> queryEntities(ChannelEnum channel, String fsEa, String outEa, String eventId) {
        LambdaQueryWrapper<CalendarEventAttendeeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CalendarEventAttendeeEntity::getChannel, channel);
        wrapper.eq(CalendarEventAttendeeEntity::getFsEa, fsEa);
        wrapper.eq(CalendarEventAttendeeEntity::getOutEa, outEa);
        wrapper.eq(CalendarEventAttendeeEntity::getEventId, eventId);

        return calendarEventAttendeeMapper.selectList(wrapper);
    }

    public Integer deleteByFsUserIds(ChannelEnum channel, String fsEa, String outEa, String eventId, List<String> fsUserIds) {
        LambdaQueryWrapper<CalendarEventAttendeeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CalendarEventAttendeeEntity::getChannel, channel);
        wrapper.eq(CalendarEventAttendeeEntity::getFsEa, fsEa);
        wrapper.eq(CalendarEventAttendeeEntity::getOutEa, outEa);
        wrapper.eq(CalendarEventAttendeeEntity::getEventId, eventId);
        if(CollectionUtils.isNotEmpty(fsUserIds)) {
            wrapper.in(CalendarEventAttendeeEntity::getFsUserId, fsUserIds);
        }

        return calendarEventAttendeeMapper.delete(wrapper);
    }
}
