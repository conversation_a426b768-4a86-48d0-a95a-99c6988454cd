package com.facishare.open.feishu.syncapi.service.whatsapp;

import com.facishare.open.feishu.syncapi.data.whatsapp.WhatsappBindInfo;
import com.facishare.open.feishu.syncapi.result.Result;

public interface WhatsappEnterpriseBindService {
    Result<String> saveEnterpriseBindInfo(WhatsappBindInfo bindInfo);

    Result<WhatsappBindInfo> queryEnterpriseBindInfo(String fsEa);

    Result<Object> getEnterpriseAllPhone(String fsEa);

    Result<String> updateEnterpriseBindInfo(WhatsappBindInfo bindInfo);
}
