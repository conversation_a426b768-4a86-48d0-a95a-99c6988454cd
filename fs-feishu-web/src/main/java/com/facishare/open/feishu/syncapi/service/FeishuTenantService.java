package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.QueryTenantInfoData;

public interface FeishuTenantService {
    Result<QueryTenantInfoData> queryTenantInfo(String appId, String tenantKey);

    Result<QueryTenantInfoData> validTenantInfo(String appId, String appSecret);

    Result<QueryTenantInfoData> initQueryTenantInfo(String appId, String appSecret,String domain);
}