package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.arg.CreateTodoPushArg;
import com.facishare.open.feishu.syncapi.arg.DealTodoPushArg;
import com.facishare.open.feishu.syncapi.arg.DeleteTodoPushArg;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.model.event.ExternalDealTodoEvent;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.UpdateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.result.CreateTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DealTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DeleteTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.UpdateTodoResult;

public interface ExternalTodoMsgService {

    /**
     * 新增待办消息
     * @param createTodoPushArg
     * @return
     */
    Result<Void> createTodo(CreateTodoPushArg createTodoPushArg);

    /**
     * 处理待办消息
     * @param dealTodoPushArg
     * @return
     */
    Result<Void> dealTodo(DealTodoPushArg dealTodoPushArg);

    /**
     * 删除待办消息
     * @param deleteTodoPushArg
     * @return
     */
    Result<Void> deleteTodo(DeleteTodoPushArg deleteTodoPushArg);

    Result<Void> dealCrmTodo(ExternalDealTodoEvent externalDealTodoEvent);

    Result<Void> retryCrmTodo(OuterOaEnterpriseBindEntity entity);
}
