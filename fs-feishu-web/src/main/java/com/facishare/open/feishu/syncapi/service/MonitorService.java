package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.oa.base.dbproxy.ch.manager.OAConnectorOpenDataModel;
import com.facishare.open.feishu.syncapi.result.Result;

public interface MonitorService {
    /**
     * 开通相关监控上报
     */
    Result<Void> uploadOaConnectorOpenData(OAConnectorOpenDataModel oaConnectorOpenDataModel);

    Result<Void> alertMoreEmployeeMessage(OAConnectorOpenDataModel oaConnectorOpenDataModel);
}
