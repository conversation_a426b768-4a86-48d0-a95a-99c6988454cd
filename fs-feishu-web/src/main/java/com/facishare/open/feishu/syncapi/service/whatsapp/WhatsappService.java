package com.facishare.open.feishu.syncapi.service.whatsapp;

import com.facishare.open.feishu.syncapi.data.whatsapp.*;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.result.whatsapp.*;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;

public interface WhatsappService {
    Result<Object> getWhatsappPhone(WhatsappBindInfo whatsappBindInfo);

    Result<WhatsappUploadFileResult> whatsappUploadFile(EnterpriseBindEntity enterpriseBindEntity, WhatsappUploadMedia uploadMedia);

    Result<WhatsappSendMsgResult> whatsappSendMsg(EnterpriseBindEntity enterpriseBindEntity, WhatsappSendMsg sendMsg);

    Result<WhatsappGetTemplateResult> whatsappGetTemplate(EnterpriseBindEntity enterpriseBindEntity, WhatsappGetTemplate template);

    Result<WhatsappCreateTemplateResult> whatsappCreateTemplate(EnterpriseBindEntity enterpriseBindEntity, WhatsappCreateTemplate template);

    Result<WhatsappUploadFileResult> whatsappUploadTemplateFile(EnterpriseBindEntity enterpriseBindEntity, WhatsappUploadMedia uploadMedia);

    Result<WhatsappUpdateTemplateResult> whatsappUpdateTemplate(EnterpriseBindEntity enterpriseBindEntity,WhatsappUpdateTemplate template);

    Result<WhatsappDeleteTemplateResult> whatsappDeleteTemplate(EnterpriseBindEntity enterpriseBindEntity, WhatsappDeleteTemplate template);

    Result<WhatsappGetFileResult> whatsappGetMedia(EnterpriseBindEntity enterpriseBindEntity, WhatsappGetMedia template);

    Result<WhatsappBalanceResult> whatsappGetBalance(WhatsappBindInfo whatsappBindInfo);
}
