package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.proto.OutEventDateChangeProto;
import com.facishare.open.feishu.syncapi.result.Result;

public interface ToolsService {
    Result<Void> initEnterprisesCache();

    Result<Void> initEnterpriseCache(String fsEa, String outEa);

    Result<Void> clearEnterpriseInfoCache(String fsEa, String outEa, Integer type, String appId);

    Result<String> queryFsEnterpriseOpen(String displayId);

    Result<String> queryFsEmployeeOpen(String displayId, String phone);

    Result<String> queryEnterpriseBindType(String fsEa, String outEa);

    Result<String> queryFsEmployeeStatus(String dataCenterId, String phone);

    Result<Void> pushCorpBindData2Cloud(String domain);

    Result<Void> pushAppBindData2Cloud(String domain);

    Result<Void> pushEnterpriseData2Cloud(String fsEa, String domain);

    Result<Integer> updateEnterpriseDomain(String fsEa, String domain);

    Result<Void> pushCloudData(OutEventDateChangeProto proto);

    Result<Integer> deleteEnterpriseBindById(String fsEa, String outEa, String domain);

    Result<Integer> deletePhoneBind(String fsEa, String phone);

    Result<Integer> brushDataAppIdByFeishu();
}
