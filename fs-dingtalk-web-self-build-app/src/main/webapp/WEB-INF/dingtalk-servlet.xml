<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	                       http://www.springframework.org/schema/mvc
	                       http://www.springframework.org/schema/mvc/spring-mvc-3.1.xsd">
    <import resource="classpath:spring/applicationContext.xml"/>

    <mvc:default-servlet-handler/>

    <bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="viewClass" value="org.springframework.web.servlet.view.JstlView"/>
        <property name="prefix" value="/"/>
        <property name="suffix" value=".*"/>
    </bean>

<!--    &lt;!&ndash; 对模型视图名称的解析,在请求时模型视图名称添加前后缀&ndash;&gt;-->
<!--    <bean id="viewResolver"-->
<!--          class="org.springframework.web.servlet.view.InternalResourceViewResolver">-->
<!--        &lt;!&ndash;<property name="viewClass"-->
<!--            value="org.springframework.web.servlet.view.JstlView" />&ndash;&gt;-->
<!--        <property name="viewClass"-->
<!--                  value="org.springframework.web.servlet.view.JstlView" />-->
<!--        &lt;!&ndash; 通过setter方法注入前缀 /WEB-INF/views/showMsg.jsp&ndash;&gt;-->
<!--        <property name="prefix" value="/" />-->
<!--        &lt;!&ndash; 通过setter方法注入后缀 &ndash;&gt;-->
<!--        <property name="suffix" value=".jsp" />-->

<!--        <property name="order" value="1" />-->
<!--    </bean>-->

<!--    <bean id="htmlviewResolver"-->
<!--          class="org.springframework.web.servlet.view.InternalResourceViewResolver">-->
<!--        <property name="viewClass" value="org.springframework.web.servlet.view.JstlView"/>-->
<!--        <property name="order" value="2" />-->
<!--        <property name="prefix" value="/"/>-->
<!--        <property name="suffix" value=".html" />-->
<!--        <property name="contentType" value="text/html;charset=UTF-8"></property>-->
<!--    </bean>-->



    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="defaultEncoding" value="UTF-8"/>
        <property name="maxUploadSize" value="10485760000"/>
        <property name="maxInMemorySize" value="40960"/>
    </bean>

    <bean id="utf8charset" class="java.nio.charset.Charset"
          factory-method="forName">
        <constructor-arg value="UTF-8"/>
    </bean>
    <mvc:annotation-driven>
        <mvc:message-converters>
            <bean class="org.springframework.http.converter.StringHttpMessageConverter">
                <constructor-arg ref="utf8charset"/>
                <property name="writeAcceptCharset" value="false"/>
                <property name="supportedMediaTypes">
                    <list>
                        <value>application/json;charset=UTF-8</value>
                    </list>
                </property>
            </bean>
            <bean class="org.springframework.http.converter.json.GsonHttpMessageConverter"></bean>
        </mvc:message-converters>
    </mvc:annotation-driven>

    <!-- 拦截器配置 -->
    <mvc:interceptors>
        <bean class="com.facishare.open.ding.web.interceptor.UserInterceptor"/>
        <bean class="com.facishare.open.ding.web.interceptor.PrivlegeInterceptor"/>
    </mvc:interceptors>

</beans>