package com.facishare.open.ding.provider.config;

import com.alibaba.fastjson.JSONArray;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.swagger.models.auth.In;

import java.util.List;
import java.util.Set;

/**
 * Create by max on 2019/07/05
 **/
public class ConfigCenter {

    public static String CRM_REST_OBJ_URL="http://172.31.101.246:17263/API/v1/rest/object";
    public static Set<String> OA_GRAY_TENANTS= Sets.newHashSet();
    public static Set<String> DATA_GRAY_TENANTS= Sets.newHashSet();
    public static Set<String> CRM_TO_BIZ_TYPES= Sets.newHashSet();
    public static Set<String> WORK_ORDER_BIZ_TYPES= Sets.newHashSet();
    public static Set<String> OA_WORK_ORDER_TENANTS= Sets.newHashSet();
    public static String SYS_TODO_USER_IDS;
    public static List<String> QPS_LIMIT_CODE = Lists.newArrayList("90002", "Forbidden.AccessDenied.QpsLimitForApi", "90018", "Forbidden.AccessDenied.QpsLimitForAppkeyAndApi");
    public static String QPS_LIMIT_MAX_EA = "{\"default\":100,\"1111\":2}";
    public static Set<String> NOT_UPDATE_EA = Sets.newHashSet();
    public static Set<String> QPS_LIMIT_EA = Sets.newHashSet();
    public static String DING_CLIENT_IP;
    public static String AVA_FS_COMMON_WEBVIEW_URL = "https://www.ceshi112.com/hcrm/dingtalk/#/ava_fs_common/pages/webview/index?url={url}&redirect=1";
    public static String DING_FUNCTION_URL = "https://www.ceshi112.com/hcrm/dingtalk/function/";
    public static String DING_FILE_URL = "https://www.ceshi112.com/dps/preview/bypath?path={path}&showHeader=1";
    public static String DING_ATME_URL = "https://www.ceshi112.com/hcrm/dingtalk?feedId={feedId}#/feed/detail";
    public static Set<String> BI_AND_FILE_MESSAGE_EA = Sets.newHashSet("true");
    public static String XOR_SECRET_KEY;
    //密钥
    public static String BASE64_SECRET = "";
    public static String CRM_RATE_LIMIT = "{\"defaultRateLimit\":20,\"84883\":2}";

    public static String DINGTALK_SKIP_LOGIN_URL="https://login.dingtalk.com/oauth2/auth?redirect_uri=%s&response_type=code&client_id=%s&scope=openid&state=%s&prompt=consent";
    //新版登陆页面
    public static Set<String> MESSAGE_LOGIN_URL_TENANTS =Sets.newHashSet();
    //支持待办消息工作台通知，状态修改
    public static Set<String> SUPPORT_UPDATE_TODO_MESSAGE_STATUS_TENANTS =Sets.newHashSet();
    public static String AUTH_TOKEN_LOGIN_URL="https://www.ceshi112.com/dingtalkinner/business/authorizeByAuthCode";
    public static String PAAS_FUNCTION_URL = "http://**************:4859";
    /**
     * 灰度开始日期
     */
    public static Long GRAY_START_DATE_TIME = 1732636800000L;
    public static Set<String> NOT_CHECK_PROXY_CLIENT_IPS;

    /**
     * 最大发送人数限制
     */
    public static int MAX_RECEIVER_LIMIT = 50;

    public static Set<String> DINGTALK_LICENSES;

    public static String CRM_SHORT_URL = "http://************:13020/egress-api-service/api/v2/private-short-urls";

    public static String FILE_VIEW_URL = "ava-preview-nfile?forceRedirectH5=true&nfDatas={nfDatas}";
    public static Set<String> TODO_SLIDER_PC_OPEN_TENANTS_BROWSER=Sets.newHashSet();
    public static Set<String> TODO_SLIDER_PC_OPEN_TENANTS_WORK_BENCH=Sets.newHashSet();
    static {
        ConfigFactory.getInstance().getConfig("fs-dingtalk-provider", config -> {
            CRM_REST_OBJ_URL = config.get("CRM_REST_OBJ_URL", CRM_REST_OBJ_URL);
            OA_GRAY_TENANTS= ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("OA_GRAY_TENANTS", "")));
            DATA_GRAY_TENANTS= ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("DATA_GRAY_TENANTS", "")));
            //401,402,403,404,406,408,410,411,452,456,457,460
            String biz=config.get("CRM_TO_BIZ_TYPES");
            CRM_TO_BIZ_TYPES=  ImmutableSet.copyOf(
                    Splitter.on(",").split(config.get("CRM_TO_BIZ_TYPES", "")));
            OA_WORK_ORDER_TENANTS= ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("OA_WORK_ORDER_TENANTS", "")));
            WORK_ORDER_BIZ_TYPES=  ImmutableSet.copyOf(
                    Splitter.on(",").split(config.get("WORK_ORDER_BIZ_TYPES", "")));
            MESSAGE_LOGIN_URL_TENANTS=  ImmutableSet.copyOf(
                    Splitter.on(",").split(config.get("MESSAGE_LOGIN_URL_TENANTS", "")));
            SUPPORT_UPDATE_TODO_MESSAGE_STATUS_TENANTS=  ImmutableSet.copyOf(
                    Splitter.on(",").split(config.get("SUPPORT_UPDATE_TODO_MESSAGE_STATUS_TENANTS", "")));
            TODO_SLIDER_PC_OPEN_TENANTS_BROWSER=  ImmutableSet.copyOf(
                    Splitter.on(",").split(config.get("TODO_SLIDER_PC_OPEN_TENANTS_BROWSER", "")));
            TODO_SLIDER_PC_OPEN_TENANTS_WORK_BENCH=  ImmutableSet.copyOf(
                    Splitter.on(",").split(config.get("TODO_SLIDER_PC_OPEN_TENANTS_WORK_BENCH", "")));
            SYS_TODO_USER_IDS = config.get("SYS_TODO_USER_IDS", SYS_TODO_USER_IDS);
            QPS_LIMIT_MAX_EA = config.get("QPS_LIMIT_MAX_EA", QPS_LIMIT_MAX_EA);
            NOT_UPDATE_EA = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("NOT_UPDATE_EA", "")));
            QPS_LIMIT_EA = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("QPS_LIMIT_EA", "")));
            DING_CLIENT_IP = config.get("DING_CLIENT_IP", DING_CLIENT_IP);
            AVA_FS_COMMON_WEBVIEW_URL = config.get("AVA_FS_COMMON_WEBVIEW_URL", AVA_FS_COMMON_WEBVIEW_URL);
            DING_FUNCTION_URL = config.get("DING_FUNCTION_URL", DING_FUNCTION_URL);
            DING_FILE_URL = config.get("DING_FILE_URL", DING_FILE_URL);
            DING_ATME_URL = config.get("DING_ATME_URL", DING_ATME_URL);
            BI_AND_FILE_MESSAGE_EA = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("BI_AND_FILE_MESSAGE_EA", "")));
            XOR_SECRET_KEY = config.get("XOR_SECRET_KEY", XOR_SECRET_KEY);
            BASE64_SECRET = config.get("BASE64_SECRET", BASE64_SECRET);
            CRM_RATE_LIMIT = config.get("CRM_RATE_LIMIT", CRM_RATE_LIMIT);
            AUTH_TOKEN_LOGIN_URL = config.get("AUTH_TOKEN_LOGIN_URL", AUTH_TOKEN_LOGIN_URL);
            GRAY_START_DATE_TIME = config.getLong("GRAY_START_DATE_TIME", GRAY_START_DATE_TIME);
            NOT_CHECK_PROXY_CLIENT_IPS = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("NOT_CHECK_PROXY_CLIENT_IPS", "")));
            DINGTALK_LICENSES = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("DINGTALK_LICENSES", "ding_talk_dock_app")));
            CRM_SHORT_URL = config.get("CRM_SHORT_URL", CRM_SHORT_URL);
            FILE_VIEW_URL = config.get("FILE_VIEW_URL", FILE_VIEW_URL);
            MAX_RECEIVER_LIMIT = config.getInt("MAX_RECEIVER_LIMIT", MAX_RECEIVER_LIMIT);
        });

        ConfigFactory.getInstance().getConfig("fs-feishu-config", config -> {
            PAAS_FUNCTION_URL = config.get("PAAS_FUNCTION_URL", PAAS_FUNCTION_URL);
        });

    }
}